{"name": "uzmacloud-api", "version": "1.0.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "api:test": "node scripts/test-api.js", "api:health": "node scripts/test-api.js health", "api:bank-receipts": "node scripts/test-api.js bank-receipts"}, "dependencies": {"@aws-sdk/client-rekognition": "^3.696.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/platform-express": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/mongoose": "^11.0.3", "@nestjs/schedule": "^4.1.1", "@nestjs/mapped-types": "^2.0.5", "mongoose": "^7.6.3", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "@types/multer-s3": "^3.0.3", "crypto-js": "^4.2.0", "xlsx": "^0.18.5", "moment": "^2.30.1", "ejs": "^3.1.10", "exceljs": "^4.4.0", "aws-sdk": "^2.1691.0", "bcrypt": "^5.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0", "@types/multer": "^1.4.11"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}