import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ProjectInsight } from '../schema/project-insight.schema';
import { CreateProjectInsightDto } from '../dto/create-project-insight.dto';

@Injectable()
export class ProjectInsightService {
    constructor(@InjectModel(ProjectInsight.name) private readonly projectInsightModel: Model<ProjectInsight>) { }

    // Fetch all categories
    async getAllCategories(): Promise<ProjectInsight[]> {
        return await this.projectInsightModel.find().exec();
    }

    // Fetch a single category by name
    async getCategoryByName(categoryName: string): Promise<ProjectInsight | any> {
        return await this.projectInsightModel.findOne({ 'categories.categoryName': categoryName }).exec();
    }

    // Read gallery by _id, or fetch all, sorted by 'createdAt' in descending order
    async getCategoryById(id?: string): Promise<ProjectInsight | any> {
        if (id) {
            // Ensure the ID is treated as a MongoDB ObjectId
            return this.projectInsightModel.findOne({ _id: new Types.ObjectId(id) }).sort({ createdAt: -1 }).exec();
        }

        throw new NotFoundException(`Project Insight with title '${id}' not found`);
        // Fetch all galleries, sorted by 'createdAt' in descending order
        // return this.projectInsightModel.findOne().sort({ createdAt: -1 }).exec();
    }

    // Update category data
    async updateProjectInsight(id: string, updateData: Partial<ProjectInsight>): Promise<ProjectInsight | any> {
        return await this.projectInsightModel
            .findByIdAndUpdate(id, updateData, { new: true }) // Return the updated document
            .exec();
    }

    // Add new category
    async addCategory(categoryData): Promise<ProjectInsight> {
        return await this.projectInsightModel.create(categoryData);
    }

    // Create a new project insight
    async createInsight(createInsightDto: CreateProjectInsightDto): Promise<ProjectInsight> {
        const newInsight = new this.projectInsightModel({
            title: createInsightDto.title,
            areaOfConcern: createInsightDto.areaOfConcern,
            progressSummary: createInsightDto.progressSummary.map((summary) => ({
                headers: summary.headers,
                values: summary.values,
            })),
            updatedBy: createInsightDto.updatedBy,
        });

        return await newInsight.save();
    }

    // Fetch a single insight by title
    async getInsightByTitle(title: string): Promise<ProjectInsight> {
        const insight = await this.projectInsightModel.findOne({ title }).exec();

        if (!insight) {
            throw new NotFoundException(`Project Insight with title '${title}' not found`);
        }

        return insight;
    }
}
