import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as XLSX from 'xlsx';
import { YGL01, YGL01Document } from '../schema/ygl_01.schema';

@Injectable()
export class YGL01Service {
  constructor(
    @InjectModel(YGL01.name) private ygl01Model: Model<YGL01Document>,
  ) { }

  async importYGL01(body: any, buffer: Buffer): Promise<string> {
    // Parse the uploaded Excel file
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const sheet = workbook.Sheets[workbook.SheetNames[0]];

    // Read data from the Excel file starting from row 4 and include headers from row 3
    const excelData = XLSX.utils.sheet_to_json(sheet, { header: 1, range: 2 });

    // Get the headers from the 4th row (index 0 of excelData)
    const headers: any = excelData[0];
    const rows: any[] = excelData.slice(1);

    // Store new records to be inserted
    const newYGL01: any[] = [];

    for (let i = 1; i < rows.length; i++) { // Start from 1 to skip the first row
      const row = rows[i];
    
      if (this.isRowEmpty(row)) {
        console.log("Skipping empty row:", row);
        continue;
      }
    
      const ygl01Data: { [key: string]: any } = {};
    
      // Map each header to the corresponding value in the row
      headers.forEach((header, index) => {
        ygl01Data[header] = row[index];
      });
    
      // Skip rows with missing type
      if (ygl01Data['Type'] === '') {
        continue;
      }
    
      // console.log("ROW ", row);
    
      newYGL01.push({
        CompanyCode: body.CompanyCode,
        CompanyName: body.CompanyName,
        LedgerType: body.LedgerType,
        StartDate: body.StartDate,
        EndDate: body.EndDate,
        GLCode: ygl01Data['GL Code'],
        GLDescription: ygl01Data['GL Description'],
        Type: ygl01Data['Type'],
        DocumentNo: ygl01Data['Document Number'],
        Date: ygl01Data['Date'],
        Description: ygl01Data['Description'],
        Reference: ygl01Data['Free Reference'],
        OffsetAccount: ygl01Data['Offset Account'],
        SourceDocument: ygl01Data['Source Document'],
        ExtraField: ygl01Data['Extra Field'],
        MM: ygl01Data['MM'],
        BRC: ygl01Data['BRC'],
        BRCDate: ygl01Data['BRC Date'],
        DepartmentCode: ygl01Data['[Dept]'],
        DepartmentName: ygl01Data['[Dept]'],
        ProjectCode: ygl01Data['[Project]'],
        ProjectName: ygl01Data['[Project]'],
        BusinessAccount: ygl01Data['[Biz Act]'],
        BusinessName: ygl01Data['[Biz Act]'],
        Currency: ygl01Data['Cur'],
        DocumentAmount: ygl01Data['Doc.Currency'],
        DebitAmount: ygl01Data['Local(Debit)'],
        CreditAmount: ygl01Data['Local(Credit)'],
        CumulativeBalance: ygl01Data['Cumulative Balance'],
        Movement: ygl01Data['Movement'],
        User: ygl01Data['User'],
        Division: ygl01Data['Division'],
        SubDivision: ygl01Data['Sub-Division'],
        Line: ygl01Data['Line'],
        isExcluded: false,
        remark: '',
      });
    }
    
    console.log(`${newYGL01.length} new YGL01 imported successfully`);
    
    await this.ygl01Model.insertMany(newYGL01);
    return `${newYGL01.length} new YGL01 imported successfully`;
  }

  async getAllYGL01(page: number = 1, limit: number = 10): Promise<{ data: any[]; total: number; page: number; limit: number }> {
    try {
      // Calculate the number of records to skip
      const skip = (page - 1) * limit;
  
      // Fetch the total number of YGL01 records
      const total = await this.ygl01Model.countDocuments();
  
      // Fetch the YGL01 records with pagination
      const data = await this.ygl01Model
        .find()
        .skip(skip)
        .limit(limit)
        .exec();
  
      // Return the paginated data with metadata
      return {
        data,
        total,
        page,
        limit,
      };
    } catch (error) {
      throw new Error(`Error fetching YGL01 records: ${error.message}`);
    }
  }  

  convertToDate(dateString: string): Date {
    // Check if dateString is valid and follows `DD/MM/YYYY` format
    if (!dateString || typeof dateString !== 'string') return new Date();

    // Split the dateString into [DD, MM, YYYY]
    const parts = dateString.split('/');

    // Ensure there are exactly 3 parts (day, month, year)
    if (parts.length !== 3) return new Date();

    // Rearrange to `YYYY-MM-DD` format and create a new Date object
    const formattedDate = `${parts[2]}-${parts[1]}-${parts[0]}`;
    return new Date(formattedDate);
  }

  isRowEmpty(row: any[]): boolean {
    const nonEmptyCells = row.filter(cell => cell !== null && cell !== undefined && cell !== '');
    return nonEmptyCells.length === 0;  // Only process rows with at least one meaningful value
  }

  async findAll(): Promise<any> {
    const payments = await this.ygl01Model.aggregate([
      {
        $addFields: {
          parsedDate: {
            $dateFromString: {
              dateString: "$date",
              format: "%d/%m/%Y"  // Specify the format for conversion
            }
          }
        }
      },
      {
        $sort: { parsedDate: -1 }  // Sort by the converted date in descending order
      },
      {
        $project: {
          parsedDate: 0  // Remove the temporary `parsedDate` field from the result
        }
      }
    ]).exec();
  
    return { success: true, data: payments };
  }
}
