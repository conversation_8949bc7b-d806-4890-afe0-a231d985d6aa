import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateProjectReportDto } from '../dto/create-project-report.dto';
import { UpdateProjectReportDto } from '../dto/update-project-report.dto';
import { ProjectReport } from '../schema/project-report.schema';

@Injectable()
export class ProjectReportService {
    constructor(
        @InjectModel(ProjectReport.name) private readonly projectReportModel: Model<ProjectReport>,
    ) { }

    async create(createProjectReportDto: CreateProjectReportDto): Promise<ProjectReport> {
        const newReport = new this.projectReportModel(createProjectReportDto);
        return newReport.save();
    }

    async findAll(): Promise<any[]> {
        const data = await this.projectReportModel.aggregate([
            {
                $group: {
                    _id: '$month',
                    month: { $first: '$month' },
                    reports: { $push: '$$ROOT' },
                },
            },
        ]).exec();

        // Use JavaScript to sort the results
        return data.sort((a, b) => new Date(`01-${b.month}`).getTime() - new Date(`01-${a.month}`).getTime());
    }

    async findByMonth(month: string): Promise<ProjectReport[]> {
        return this.projectReportModel.find({ month }).populate('updatedBy').exec();
    }

    async update(id: string, updateProjectReportDto: UpdateProjectReportDto): Promise<ProjectReport> {
        const updatedReport = await this.projectReportModel.findByIdAndUpdate(
            id,
            updateProjectReportDto,
            { new: true },
        );
        if (!updatedReport) {
            throw new NotFoundException(`Report with ID ${id} not found`);
        }
        return updatedReport;
    }

    async delete(id: string): Promise<void> {
        const result = await this.projectReportModel.findByIdAndDelete(id);
        if (!result) {
            throw new NotFoundException(`Report with ID ${id} not found`);
        }
    }
}
