import { IsArray, IsNotEmpty, IsString, IsNumber, ValidateNested, IsDecimal } from 'class-validator';
import { Type } from 'class-transformer';
import { Decimal128 } from 'mongoose';

class BankReceipt {
  @IsString()
  @IsNotEmpty()
  receiptNumber: string;

  @IsString()
  @IsNotEmpty()
  receiptDate: string; // ISO string for date

  amount: number;

  @IsString()
  @IsNotEmpty()
  bankName: string;

  @IsString()
  @IsNotEmpty()
  accountNumber: string;

  @IsString()
  @IsNotEmpty()
  s3Url: string;

  @IsString()
  remark: string;
}

export class UpdateBankReceiptsDto {
  @IsString()
  @IsNotEmpty()
  paymentAdviceId: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BankReceipt)
  bankReceipts: BankReceipt[];
}
