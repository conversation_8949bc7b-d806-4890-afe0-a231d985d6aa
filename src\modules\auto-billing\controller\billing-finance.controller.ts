// src/billing/controller/billing-finance.controller.ts
import { Controller, Post, Body, Get, Param, Put, Delete } from '@nestjs/common';
import { BillingFinanceService } from '../service/billing-finance.service';

@Controller('billing-finance')
export class BillingFinanceController {
  constructor(private billingFinanceService: BillingFinanceService) {}

  @Post()
  async createFinanceFlow(@Body() financeFlowData: any) {
    return this.billingFinanceService.createFinanceFlow(financeFlowData);
  }

  @Get()
  async getAllFinanceFlows() {
    return this.billingFinanceService.getAllFinanceFlows();
  }

  @Get('project/:projectId')
  async getFinanceFlowByProject(@Param('projectId') projectId: string) {
    return this.billingFinanceService.getFinanceFlow(projectId);
  }

  @Get('department/:departmentId')
  async getFinanceFlowByDepartment(@Param('departmentId') departmentId: string) {
    return this.billingFinanceService.getFinanceFlow(null, departmentId);
  }

  @Get('global')
  async getGlobalFinanceFlow() {
    return this.billingFinanceService.getFinanceFlow();
  }

  @Put(':financeFlowId')
  async updateFinanceFlow(
    @Param('financeFlowId') financeFlowId: string,
    @Body() updateData: any
  ) {
    return this.billingFinanceService.updateFinanceFlow(financeFlowId, updateData);
  }

  @Delete(':financeFlowId')
  async deactivateFinanceFlow(@Param('financeFlowId') financeFlowId: string) {
    return this.billingFinanceService.deactivateFinanceFlow(financeFlowId);
  }
}