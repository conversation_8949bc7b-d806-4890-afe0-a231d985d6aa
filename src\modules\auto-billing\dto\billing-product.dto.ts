import { IsString, <PERSON>NotEmpty, IsOptional, IsBoolean, IsNumber, IsObject } from 'class-validator';

export class CreateProductDto {
  @IsString()
  @IsNotEmpty()
  productName: string;

  @IsString()
  @IsNotEmpty()
  departmentId: string;

  @IsString()
  @IsNotEmpty()
  projectId: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsNotEmpty()
  unit: string;

  @IsNumber()
  @IsNotEmpty()
  price: number;

  @IsNumber()
  @IsOptional()
  tax?: number;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsObject()
  @IsOptional()
  additionalDetails?: Record<string, any>;
}

export class UpdateProductDto {
  @IsString()
  @IsOptional()
  productName?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  unit?: string;

  @IsNumber()
  @IsOptional()
  price?: number;

  @IsNumber()
  @IsOptional()
  tax?: number;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsObject()
  @IsOptional()
  additionalDetails?: Record<string, any>;
}

export class ProductResponseDto {
  id: string;
  productId: string;
  productName: string;
  departmentId: string;
  projectId: string;
  description?: string;
  unit: string;
  price: number;
  tax: number;
  isActive: boolean;
  additionalDetails?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}