import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ApprovalFlowDocument = ApprovalFlow & Document;

@Schema({ timestamps: true, collection: 'approval_flow_approver' })
class Approver {
  @Prop({ required: true, type: Types.ObjectId, ref: 'User' })
  userId: Types.ObjectId;
  
  @Prop({ required: true })
  role: string;

  @Prop({ required: true })
  order: number;

  @Prop({ default: true })
  isActive: boolean;
}

export const ApproverSchema = SchemaFactory.createForClass(Approver);

@Schema({ timestamps: true, collection: 'approval_master' })
export class ApprovalFlow extends Document {
  @Prop({ required: true, unique: true })
  flowId: string;

  @Prop({ type: Types.ObjectId, ref: 'Department', required: true })
  department: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Project', required: true })
  project: Types.ObjectId;

  @Prop({ required: true })
  documentType: string; // e.g., 'billing', 'purchase_order', etc.

  @Prop({ type: [ApproverSchema], required: true })
  approvers: Approver[];

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: 'sequential' })
  approvalType: string; // 'sequential' or 'parallel'

  @Prop()
  description?: string;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop()
  updatedAt?: Date;
}

export const ApprovalFlowSchema = SchemaFactory.createForClass(ApprovalFlow);