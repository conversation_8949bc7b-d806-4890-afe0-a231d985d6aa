import { Controller, Get, Param, Put, Body, Post, Query } from '@nestjs/common';
import { ProjectInsightService } from '../service/project-insight.service';
import { CreateProjectInsightDto } from '../dto/create-project-insight.dto';

@Controller('project-insight')
export class ProjectInsightController {
  constructor(private readonly projectInsightService: ProjectInsightService) { }

  // Get all categories
  @Get('categories')
  async getAllCategories() {
    return this.projectInsightService.getAllCategories();
  }

  // Get a specific category by name
  @Get('category/:categoryName')
  async getCategory(@Param('categoryName') categoryName: string) {
    return this.projectInsightService.getCategoryByName(categoryName);
  }

  // Get a specific category by id
  @Get('getById')
  async getCategoryById(@Query('_id') _id: string) {
    return this.projectInsightService.getCategoryById(_id);
  }

  // Update a specific category
  @Put('category/:categoryName')
  async updateCategory(@Param('categoryName') id: string, @Body() categoryData) {
    return this.projectInsightService.updateProjectInsight(id, categoryData);
  }

  // Add a new category
  @Post('category')
  async addCategory(@Body() categoryData) {
    return this.projectInsightService.addCategory(categoryData);
  }

  @Post()
  async createInsight(
    @Body() createInsightDto: CreateProjectInsightDto // Validate the incoming request
  ) {
    return this.projectInsightService.createInsight(createInsightDto);
  } 
  
  // Endpoint to fetch a ProjectInsight by title
  @Get(':title')
  async getInsightByTitle(@Param('title') title: string) {
    return this.projectInsightService.getInsightByTitle(title);
  }  
}
