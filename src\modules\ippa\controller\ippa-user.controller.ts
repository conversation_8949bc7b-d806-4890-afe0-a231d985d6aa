import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  Body,
  BadRequestException,
  HttpStatus,
  Req,
  InternalServerErrorException,
  Query,
  Get,
  Param,
  NotFoundException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Types } from 'mongoose';
import { IppaUserService } from '../service/ippa-user.service';
import { ApiOperation, ApiParam, ApiQuery, ApiResponse } from '@nestjs/swagger';
import { IppaUserResponseDto } from '../dto/ippa-user.dto';
import { isPopulatedDepartment, isPopulatedUser } from '../dto/ippa-user.interfaces';
import { SecurityService } from 'src/modules/user/service/security.service';

class CreateUsersDto {
  defaultRoleId: string;
}

class CreateIppaUsersDto {
  // No additional fields needed as data comes from Excel
}

@Controller('ippa-users')
export class IppaUserController {
  constructor(
    private readonly ippaUserService: IppaUserService,
    private readonly securityService: SecurityService,) {}

  @Post('upload-excel-create-users')
  @UseInterceptors(FileInterceptor('file'))
  async uploadExcelAndCreateUsers(
    @UploadedFile() file: Express.Multer.File,
    @Body('defaultRoleId') defaultRoleId: string,
    @Req() req: any, // You'll need to implement authentication to get current user
  ) {
    if (!file) {
      throw new BadRequestException('Excel file is required');
    }

    if (!defaultRoleId) {
      throw new BadRequestException('Default role ID is required');
    }

    try {
      // You'll need to get the current user ID from your authentication system
      const createdBy = new Types.ObjectId(); // Replace with actual current user ID
      const defaultRole = new Types.ObjectId(defaultRoleId);

      const result =
        await this.ippaUserService.processExcelAndCreateMissingUsers(
          file,
          createdBy,
          defaultRole,
        );

      return {
        statusCode: HttpStatus.OK,
        message: 'Users processed successfully',
        data: {
          summary: {
            totalProcessed: result.totalProcessed,
            successfullyCreated: result.createdUsers.length,
            failed: result.failedUsers.length,
          },
          createdUsers: result.createdUsers.map((user) => ({
            email: user.email,
            isNewUser: user.isNewUser,
            userId: user.userId,
          })),
          failedUsers: result.failedUsers,
          nextStep:
            'Use the create-ippa-users endpoint to create IPPA user records',
        },
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('upload-excel-create-ippa-users')
  @UseInterceptors(FileInterceptor('file'))
  async uploadExcelAndCreateIppaUsers(
    @UploadedFile() file: Express.Multer.File,
    @Req() req: any, // You'll need to implement authentication to get current user
  ) {
    if (!file) {
      throw new BadRequestException('Excel file is required');
    }

    try {
      // You'll need to get the current user ID from your authentication system
      // const createdBy = new Types.ObjectId(); // Replace with actual current user ID

      const result = await this.ippaUserService.createIppaUsersFromExcel(file);

      return {
        statusCode: HttpStatus.OK,
        message: 'IPPA users created successfully',
        data: {
          summary: {
            totalCreated: result.createdIppaUsers.length,
            failed: result.failedIppaUsers.length,
          },
          createdIppaUsers: result.createdIppaUsers.map((user) => ({
            id: user._id,
            email: user.email,
            fullName: user.fullName,
            department: user.department,
            position: user.position,
          })),
          failedIppaUsers: result.failedIppaUsers,
        },
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('check-email')
  async checkEmailExists(@Body('email') email: string) {
    if (!email) {
      throw new BadRequestException('Email is required');
    }

    const exists = await this.ippaUserService.checkEmailExists(email);
    const user = exists
      ? await this.ippaUserService.getUserByEmail(email)
      : null;

    return {
      statusCode: HttpStatus.OK,
      message: 'Email check completed',
      data: {
        email,
        exists,
        userId: user?._id || null,
      },
    };
  }

  @Post('by-email')
  @ApiOperation({
    summary: 'Get IPPA user by email',
    description:
      'Retrieve IPPA user information by email address (case-insensitive)',
  })
  @ApiParam({
    name: 'email',
    description: 'Email address of the IPPA user',
    example: '<EMAIL>',
  })
  @ApiResponse({
    status: 200,
    description: 'IPPA user found successfully',
    type: IppaUserResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'IPPA user not found',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid email format',
  })
  async getIppaUserByEmail(@Body() encryptedData: any): Promise<any> {

    try {
      const body = this.securityService.decrypt(encryptedData.data);

      // Validate email format
      if (!body || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(body)) {
        throw new BadRequestException('Please provide a valid email address');
      }

      const ippaUser = await this.ippaUserService.getIppaUserByEmail(body);

      if (!ippaUser) {
        throw new NotFoundException(
          `IPPA user with email '${body}' not found`,
        );
      }

      // Transform the response
      const responseData: IppaUserResponseDto = {
        _id: ippaUser._id.toString(),
        username: ippaUser.username,
        fullName: ippaUser.fullName,
        email: ippaUser.email,
        company: ippaUser.company,
        group: ippaUser.group,
        businessUnit: ippaUser.businessUnit,
        employeeId: ippaUser.employeeId,
        employeeGrade: ippaUser.employeeGrade,
        position: ippaUser.position,
        positionLevel: ippaUser.positionLevel,
        division: ippaUser.division,
        department: ippaUser.department,
        departmentCode: ippaUser.departmentCode,
        departmentId: ippaUser.departmentId,
        section: ippaUser.section,
        workLocation: ippaUser.workLocation,
        joinedDate: ippaUser.joinedDate,
        isActive: ippaUser.isActive,
      };

      // Add line manager info if available
      if (isPopulatedUser(ippaUser.lineManager)) {
        responseData.lineManagerInfo = {
          _id: ippaUser.lineManager._id.toString(),
          fullName: ippaUser.lineManager.fullName,
          email: ippaUser.lineManager.email,
          designation: ippaUser.lineManager.designation,
        };
      }

      // Add department info if available
      if (isPopulatedDepartment(ippaUser.departmentRef)) {
        responseData.departmentInfo = {
          _id: ippaUser.departmentRef._id.toString(),
          code: ippaUser.departmentRef.code,
          description: ippaUser.departmentRef.description,
        };

        // Add manager info if available
        if (isPopulatedUser(ippaUser.departmentRef.manager)) {
          responseData.departmentInfo.manager = {
            _id: ippaUser.departmentRef.manager._id.toString(),
            fullName: ippaUser.departmentRef.manager.fullName,
            email: ippaUser.departmentRef.manager.email,
          };
        }

        // Add HOD info if available
        if (isPopulatedUser(ippaUser.departmentRef.hod)) {
          responseData.departmentInfo.hod = {
            _id: ippaUser.departmentRef.hod._id.toString(),
            fullName: ippaUser.departmentRef.hod.fullName,
            email: ippaUser.departmentRef.hod.email,
          };
        }
      }

      // return responseData;
      return { data: this.securityService.encrypt(responseData) };

    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Error retrieving IPPA user: ${error.message}`,
      );
    }
  }

  @Get('search')
  @ApiOperation({
    summary: 'Search IPPA users',
    description: 'Search IPPA users by various criteria with pagination',
  })
  @ApiQuery({
    name: 'email',
    required: false,
    description: 'Email to search for',
  })
  @ApiQuery({
    name: 'fullName',
    required: false,
    description: 'Full name to search for',
  })
  @ApiQuery({
    name: 'employeeId',
    required: false,
    description: 'Employee ID to search for',
  })
  @ApiQuery({
    name: 'department',
    required: false,
    description: 'Department to search for',
  })
  @ApiQuery({
    name: 'company',
    required: false,
    description: 'Company to search for',
  })
  @ApiQuery({
    name: 'isActive',
    required: false,
    description: 'Active status filter',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of results to return (default: 50)',
  })
  @ApiQuery({
    name: 'skip',
    required: false,
    description: 'Number of results to skip (default: 0)',
  })
  @ApiResponse({
    status: 200,
    description: 'Search completed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            users: {
              type: 'array',
              items: { $ref: '#/components/schemas/IppaUserResponseDto' },
            },
            total: { type: 'number' },
            limit: { type: 'number' },
            skip: { type: 'number' },
          },
        },
      },
    },
  })
  async searchIppaUsers(
    @Query('email') email?: string,
    @Query('fullName') fullName?: string,
    @Query('employeeId') employeeId?: string,
    @Query('department') department?: string,
    @Query('company') company?: string,
    @Query('isActive') isActive?: string,
    @Query('limit') limit?: string,
    @Query('skip') skip?: string,
  ): Promise<{
    success: boolean;
    message: string;
    data: {
      users: any[];
      total: number;
      limit: number;
      skip: number;
    };
  }> {
    try {
      const searchCriteria = {
        email,
        fullName,
        employeeId,
        department,
        company,
        isActive: isActive ? isActive === 'true' : undefined,
        limit: limit ? parseInt(limit) : 50,
        skip: skip ? parseInt(skip) : 0,
      };

      const result = await this.ippaUserService.searchIppaUsers(searchCriteria);

      return {
        success: true,
        message: `Found ${result.total} IPPA users`,
        data: {
          users: result.users,
          total: result.total,
          limit: searchCriteria.limit,
          skip: searchCriteria.skip,
        },
      };
    } catch (error) {
      throw new InternalServerErrorException(
        `Error searching IPPA users: ${error.message}`,
      );
    }
  }
}
