// import { Controller, Post, Body, UseGuards } from '@nestjs/common';
// import * as bcrypt from 'bcrypt';
// import * as AWS from 'aws-sdk';
// import { UserService } from 'src/user/service/user.service';

// @Controller('enroll')
// export class EnrollController {
//   private rekognition: AWS.Rekognition;

//   constructor(private readonly userService: UserService) {
//     this.rekognition = new AWS.Rekognition({
//       accessKeyId: process.env.AWS_ACCESS_KEY_ID,
//       secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
//       region: process.env.AWS_REGION,
//     });
//   }

//   @Post('face')
//   async enrollFace(@Body() body: { userId: number; faceImage: Buffer }) {
//     const user = await this.userService.findOneById(body.userId);
//     if (user) {
//       user.faceId = await this.enrollFaceInRekognition(body.faceImage);
//       user.isEnrolled = true;
//       await this.userService.save(user);
//       return { message: 'Face enrolled successfully' };
//     }
//     throw new Error('User not found');
//   }

//   @Post('password')
//   async enrollPassword(@Body() body: { userId: number; password: string }) {
//     const user = await this.userService.findOneById(body.userId);
//     if (user) {
//       user.password = await bcrypt.hash(body.password, 10);
//       user.isEnrolled = true;
//       await this.userService.save(user);
//       return { message: 'Password enrolled successfully' };
//     }
//     throw new Error('User not found');
//   }

//   private async enrollFaceInRekognition(faceImage: Buffer): Promise<string> {
//     const params = {
//       CollectionId: 'uzmaes',
//       Image: {
//         Bytes: faceImage,
//       },
//     };

//     try {
//       const result = await this.rekognition.indexFaces(params).promise();
//       if (result.FaceRecords.length > 0) {
//         return result.FaceRecords[0].Face.FaceId;
//       }
//       throw new Error('Face indexing failed');
//     } catch (error) {
//       throw new Error('AWS Rekognition error: ' + error.message);
//     }
//   }
// }
