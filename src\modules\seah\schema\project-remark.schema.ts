import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from 'src/modules/user/schema/user.schema';

export type ProjectRemarkDocument = ProjectRemark & Document;

@Schema({ timestamps: true, collection: 'project_remark' })
export class ProjectRemark {
  // Make this field unique to avoid duplicates
  @Prop()
  remark: string;
  
  @Prop()
  description: string;

  @Prop()
  BPNo: string;

  @Prop()
  BPName: string;

  @Prop()
  referenceNo: string;

  @Prop()
  poNo: string;

  @Prop()
  invoiceNo: string;

  @Prop()
  department: string;

  @Prop()
  project: string;

  @Prop()
  status: string;

  @Prop()
  PIC: string;

  @Prop()
  type: string; // PO, LOA, Consultant

  @Prop()
  isExcluded: boolean;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  updatedBy: Types.ObjectId | User;

  @Prop({attachments: [
    {
      remark: { type: String },
      type: { type: String },
      s3Url: { type: String },
    }
  ]})
    attachments: Array<{
      remark: string;
      type: string;
      s3Url: string;
    }>;
}

export const ProjectRemarkSchema = SchemaFactory.createForClass(ProjectRemark);
