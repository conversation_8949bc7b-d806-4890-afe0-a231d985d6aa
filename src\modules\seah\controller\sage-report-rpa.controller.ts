import { Controller, Get, Post, Body, Param, Put, Delete } from '@nestjs/common';
import { SageReportRPA } from '../schema/sage-pv.schema';
import { SageReportRPAService } from '../service/sage-report-rpa.service';

@Controller('sage-report-rpa')
export class SageReportRPAController {
  constructor(private readonly sageReportRPAService: SageReportRPAService) {}

  @Post()
  create(@Body() createSageReportRPADto: Partial<SageReportRPA>) {
    return this.sageReportRPAService.create(createSageReportRPADto);
  }

  @Get()
  findAll() {
    return this.sageReportRPAService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.sageReportRPAService.findOne(id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateSageReportRPADto: Partial<SageReportRPA>) {
    return this.sageReportRPAService.update(id, updateSageReportRPADto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.sageReportRPAService.remove(id);
  }

  @Put(':id/mark-as-posted')
  markAsPosted(@Param('id') id: string) {
    return this.sageReportRPAService.markAsPosted(id);
  }
}