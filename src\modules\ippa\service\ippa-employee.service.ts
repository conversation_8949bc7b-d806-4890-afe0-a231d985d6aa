import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  IPPASubmission,
  IPPASubmissionDocument,
  IPPAStatus,
  ReviewerType,
  ApprovalWorkflowStep,
} from '../schema/ippa-submission.schema';
import { IppaUser, IppaUserDocument } from '../schema/ippa-user.schema';
import {
  isPopulatedLineManager,
  PopulatedIppaUser,
} from '../dto/populated-ippa-user.interface';
import {
  Department,
  DepartmentDocument,
} from 'src/modules/user/schema/department.schema';
import { User, UserDocument } from 'src/modules/user/schema/user.schema';
import { report } from 'process';
import { IPPAMessage, IPPAMessageDocument } from '../schema/ippa-message.schema';
import { CreateIPPADto } from '../dto/ippa-employee.dto';
import { SecurityService } from 'src/modules/user/service/security.service';

interface SubmissionStatusInfo {
  totalSubmissions: number;
  approved: number;
  pendingReview: number;
  completed: number;
  submitted: number;
  draft: number;
}

interface DetailedObjective {
  index: number;
  keyResultsArea: string;
  workObjective: string;
  targetBase: string;
  targetExceed: string;
  targetStretch: string;
  weightage: number;
  yearEndResults: string;
  performanceAssessment: string;
  employeeRating: number;
  employeeWeightedPoints: number;
  supervisorRating: number | null;
  supervisorWeightedPoints: number | null;
  supervisorComments: string | null;
  hasEmployeeRating: boolean;
  hasSupervisorRating: boolean;
}

interface DetailedDevelopmentPlan {
  index: number;
  competencyType: string;
  areaForImprovement: string;
  activity: string;
  timeline: string;
  yearEndComments: string;
  supervisorFeedback: string | null;
  hodRemarks: string | null;
  hasYearEndComments: boolean;
  hasSupervisorFeedback: boolean;
  hasHodRemarks: boolean;
}

interface DetailedAttachment {
  fileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  uploadedAt: Date;
  description: string;
  fileSizeFormatted: string;
  reviewerType?: ReviewerType;
  reviewerName?: string;
  reviewedAt?: Date;
}

interface DetailedReview {
  reviewerType: ReviewerType;
  reviewerId: Types.ObjectId;
  reviewerName: string;
  remarks: string;
  attachments: DetailedAttachment[];
  overallScore: number | null;
  reviewedAt: Date;
  department: string;
  attachmentCount: number;
  hasRemarks: boolean;
  hasAttachments: boolean;
}

interface DetailedApprovalWorkflowStep {
  stepId: number;
  title: string;
  personId: Types.ObjectId;
  personName: string;
  department: string;
  date: string;
  status: string;
  isCompleted: boolean;
  isPending: boolean;
  isUpcoming: boolean;
  statusColor: string;
  formattedDate: string;
}

interface SubmissionSummaryStats {
  totalWeightage: number;
  completedObjectives: number;
  pendingObjectives: number;
  developmentPlansWithFeedback: number;
  totalAttachments: number;
  reviewsCompleted: number;
  workflowStepsCompleted: number;
  workflowStepsPending: number;
}

// Updated SubmissionSummary interface - replace your existing one
interface SubmissionSummary {
  ippaId: string;
  employee: IppaUser;
  submissionId: string;
  employeeName: string;
  employeeId: string;
  department: string;
  departmentCode: string;
  position: string;
  grade: string;
  status: IPPAStatus;
  statusLabel: string;
  statusColor: string;
  performanceScore: number;
  performanceRating: string;
  totalObjectives: number;
  totalDevelopmentPlans: number;
  attachmentsCount: number;
  submittedAt: Date | null;
  lastModifiedAt: Date | null;
  financialYear: string;
  reviewPeriod: string;
  currentReviewer?: {
    name: string;
    email: string;
    department: string;
  } | null;
  approvalWorkflow: ApprovalWorkflowStep[];
  isDraft: boolean;
  daysSinceAction: number;
  isOverdue: boolean;
  canEdit: boolean;
  nextAction: string;

  // Detailed information
  objectives: DetailedObjective[];
  developmentPlans: DetailedDevelopmentPlan[];
  reviews: DetailedReview[];
  workflowDetails: DetailedApprovalWorkflowStep[];
  allAttachments: DetailedAttachment[];
  summaryStats: SubmissionSummaryStats;
  isOwnSubmission: boolean;
  isForApproval: boolean;
  isPlaceholder: boolean;
  unreadMessageCount: number;
}

// Updated MySubmissionsResponse interface - replace your existing one
interface MySubmissionsResponse {
  statusInfo: SubmissionStatusInfo;
  submissions: SubmissionSummary[];
  totalCount: number;
  pagination: {
    currentPage: number;
    totalPages: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  filters: {
    status?: IPPAStatus;
    financialYear?: string;
    sortBy?: string;
    sortOrder?: string;
    search?: string;
  };
}

@Injectable()
export class IppaEmployeeService {
  constructor(
    @InjectModel(IPPASubmission.name)
    private ippaSubmissionModel: Model<IPPASubmissionDocument>,
    @InjectModel(IppaUser.name) private ippaUserModel: Model<IppaUserDocument>,
    @InjectModel(User.name)
    private userModel: Model<UserDocument>,
    @InjectModel(Department.name)
    private departmentModel: Model<DepartmentDocument>,
    @InjectModel(IPPAMessage.name)
    private messageModel: Model<IPPAMessageDocument>,
    private securityService: SecurityService
  ) { }

  // Generate unique IPPA ID
  private generateIPPAId(): string {
    const date = new Date();
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
    const randomNum = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    return `UZMA-PPA-${dateStr}-${randomNum}`;
  }

  // Calculate performance rating based on score
  private calculatePerformanceRating(score: number): {
    rating: string;
    description: string;
  } {
    if (score >= 4.1) {
      return {
        rating: 'STRETCH EXPECTATION',
        description: 'Excellent performance far beyond expectations',
      };
    } else if (score >= 3.1) {
      return {
        rating: 'EXCEED EXPECTATION',
        description:
          'Achieves beyond the targets set and proactively adds value/initiatives and effort in daily work',
      };
    } else if (score >= 2.6) {
      return {
        rating: 'MEETS EXPECTATION',
        description: 'Achieves expectations set at the required level',
      };
    } else if (score >= 1.6) {
      return {
        rating: 'BELOW EXPECTATION',
        description: 'Does not fully meet the expectations set',
      };
    } else {
      return {
        rating: 'SIGNIFICANTLY BELOW EXPECTATION FOR THE ROLE',
        description: 'Below requirement for successful job performance...',
      };
    }
  }

  // Build approval workflow
  private async buildApprovalWorkflow(
    employeeData: any,
    employeeMaster?: any,
    options?: {
      forceIncludeHOD?: boolean;
      customHRReviewer?: { id: string; name: string };
    },
  ): Promise<any[]> {
    const steps: any = [];
    let stepId = 1;

    // Step 1: Employee Submission
    steps.push({
      stepId: stepId++,
      title: 'Prepared by',
      personId: employeeMaster._id,
      personName: employeeMaster.fullName,
      department: employeeData.department,
      date: new Date().toLocaleString('en-GB'),
      status: 'completed',
      isRequired: true,
      stepType: 'employee',
    });

    // Step 2: Line Manager Review (Always required)
    if (employeeData.lineManager) {
      steps.push({
        stepId: stepId++,
        title: 'Approved by',
        personId: employeeData.lineManager._id,
        personName: employeeData.lineManager.fullName,
        department:
          employeeData.lineManager.department || employeeData.department,
        date: '-',
        status: 'pending',
        isRequired: true,
        stepType: 'supervisor',
      });
    }

    // Step 3: HOD Review (Based on department configuration)
    const hodStep = await this.checkAndAddHODStep(
      employeeData,
      stepId,
      options?.forceIncludeHOD,
    );
    if (hodStep) {
      steps.push(hodStep);
      stepId++;
    }

    // Final Step: HR Review
    const hrReviewer =
      options?.customHRReviewer || (await this.getDefaultHRReviewer());
    if (hrReviewer) {
      steps.push({
        stepId: stepId++,
        title: 'Reviewed by',
        personId: new Types.ObjectId(hrReviewer.id),
        personName: hrReviewer.name,
        department: 'HR',
        date: '-',
        status: 'upcoming',
        isRequired: true,
        stepType: 'hr',
      });
    }
    return steps;
  }

  /**
   * Check if HOD step should be included and create the step
   */
  private async checkAndAddHODStep(
    employeeData: any,
    stepId: number,
    forceInclude?: boolean,
  ): Promise<any | null> {
    // Get department configuration
    const department = await this.departmentModel
      .findOne({
        code: employeeData.departmentCode,
        isActive: true,
      })
      .populate('hodPersonId', 'fullName email department')
      .lean();

    if (!department) {
      console.warn(
        `Department not found for code: ${employeeData.departmentCode}`,
      );
      return null;
    }

    // Check if HOD approval is enabled for this department
    const shouldIncludeHOD = forceInclude || department.enableHodApproval;

    if (!shouldIncludeHOD || !department.hodPersonId) {
      return null;
    }

    // Ensure HOD is different from line manager
    const hodId = department.hodPersonId._id || department.hodPersonId;
    const lineManagerId = employeeData.lineManager?._id;

    if (hodId.toString() === lineManagerId?.toString()) {
      console.log('HOD is same as line manager, skipping HOD step');
      return null;
    }

    return {
      stepId: stepId,
      title: 'Approved by (HOD)',
      personId: hodId,
      personName: (department.hodPersonId as any)?.fullName || 'HOD',
      department: department.description,
      date: '-',
      status: 'upcoming',
      isRequired: department.enableHodApproval,
      stepType: 'hod',
    };
  }

  /**
   * Get default HR reviewer
   */
  private async getDefaultHRReviewer(): Promise<{
    id: string;
    name: string;
  } | null> {
    // You can implement logic to find HR users
    // For now, return the static HR reviewer
    return {
      id: '674829e0f3d796aa36ff1e0d',
      name: 'Zamhari Zaidi',
    };
  }

  /**
   * Update workflow when moving to next step
   */
  async updateWorkflowStep(
    workflow: any[],
    currentStepId: number,
    approverId: Types.ObjectId,
  ): Promise<{
    nextStatus: string;
    nextReviewerId: Types.ObjectId | null;
    nextStepId: number;
  }> {
    // Mark current step as completed
    const currentStep = workflow.find((step) => step.stepId === currentStepId);
    if (currentStep) {
      currentStep.status = 'completed';
      currentStep.date = new Date().toLocaleString('en-GB');
    }

    // Find next step
    const nextStep = workflow.find((step) => step.status === 'upcoming');

    if (nextStep) {
      nextStep.status = 'pending';

      // Determine next status based on step type
      let nextStatus: string;
      switch (nextStep.stepType) {
        case 'supervisor':
          nextStatus = 'pending_supervisor_review';
          break;
        case 'hod':
          nextStatus = 'pending_hod_review';
          break;
        case 'hr':
          nextStatus = 'pending_hr_review';
          break;
        default:
          nextStatus = 'pending_review';
      }

      return {
        nextStatus,
        nextReviewerId: nextStep.personId,
        nextStepId: nextStep.stepId,
      };
    }

    // No more steps - should be completed
    return {
      nextStatus: 'completed',
      nextReviewerId: null,
      nextStepId: workflow.length,
    };
  }

  /**
   * Get workflow status for display
   */
  getWorkflowStatusInfo(workflow: any[]): {
    currentStep: number;
    totalSteps: number;
    completedSteps: number;
    pendingSteps: number;
    upcomingSteps: number;
    currentStepTitle: string;
    nextStepTitle: string;
    isCompleted: boolean;
  } {
    const totalSteps = workflow.length;
    const completedSteps = workflow.filter(
      (step) => step.status === 'completed',
    ).length;
    const pendingSteps = workflow.filter(
      (step) => step.status === 'pending',
    ).length;
    const upcomingSteps = workflow.filter(
      (step) => step.status === 'upcoming',
    ).length;

    const currentStep = workflow.find((step) => step.status === 'pending');
    const nextStep = workflow.find((step) => step.status === 'upcoming');

    return {
      currentStep: completedSteps + 1,
      totalSteps,
      completedSteps,
      pendingSteps,
      upcomingSteps,
      currentStepTitle: currentStep?.title || 'Completed',
      nextStepTitle: nextStep?.title || 'No further steps',
      isCompleted: completedSteps === totalSteps,
    };
  }

  /**
   * Check if user can approve at current step
   */
  canUserApproveStep(
    workflow: any[],
    userId: Types.ObjectId,
  ): {
    canApprove: boolean;
    stepType: string;
    stepTitle: string;
    isRequired: boolean;
  } {
    const currentStep = workflow.find((step) => step.status === 'pending');

    if (!currentStep) {
      return {
        canApprove: false,
        stepType: 'none',
        stepTitle: 'No pending step',
        isRequired: false,
      };
    }

    const canApprove = currentStep.personId.toString() === userId.toString();

    return {
      canApprove,
      stepType: currentStep.stepType,
      stepTitle: currentStep.title,
      isRequired: currentStep.isRequired,
    };
  }

  /**
   * Get all pending approvals for a user across all submissions
   */
  async getUserPendingApprovals(userId: Types.ObjectId): Promise<{
    supervisorApprovals: number;
    hodApprovals: number;
    hrApprovals: number;
    totalPending: number;
  }> {
    // This would typically query the IPPA submissions
    // For now, return mock data
    return {
      supervisorApprovals: 0,
      hodApprovals: 0,
      hrApprovals: 0,
      totalPending: 0,
    };
  }

  /**
   * Validate workflow configuration
   */
  validateWorkflow(workflow: any[]): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Check for required steps
    const requiredStepTypes = ['employee', 'supervisor', 'hr'];
    const presentStepTypes = workflow.map((step) => step.stepType);

    requiredStepTypes.forEach((requiredType) => {
      if (!presentStepTypes.includes(requiredType)) {
        errors.push(`Missing required step type: ${requiredType}`);
      }
    });

    // Check step ID sequence
    const stepIds = workflow.map((step) => step.stepId).sort((a, b) => a - b);
    for (let i = 0; i < stepIds.length; i++) {
      if (stepIds[i] !== i + 1) {
        errors.push(`Step IDs must be sequential starting from 1`);
        break;
      }
    }

    // Check for duplicate persons in workflow
    const personIds = workflow.map((step) => step.personId.toString());
    const uniquePersonIds = [...new Set(personIds)];
    if (personIds.length !== uniquePersonIds.length) {
      errors.push('Duplicate persons found in workflow');
    }

    // Check status consistency
    const completedSteps = workflow.filter(
      (step) => step.status === 'completed',
    );
    const pendingSteps = workflow.filter((step) => step.status === 'pending');
    const upcomingSteps = workflow.filter((step) => step.status === 'upcoming');

    if (pendingSteps.length > 1) {
      errors.push('Only one step can be pending at a time');
    }

    if (
      completedSteps.length > 0 &&
      pendingSteps.length === 0 &&
      upcomingSteps.length > 0
    ) {
      errors.push(
        'Cannot have upcoming steps without a pending step when workflow is in progress',
      );
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
  /**
   * Submit IPPA form (Employee) - FIXED VERSION
   */
  async submitIPPA(encryptedData: any): Promise<IPPASubmissionDocument | any> {
    // Get employee data with line manager info - Use proper population

    const ippaData = this.securityService.decrypt(encryptedData.data);


    const employeeQuery = await this.ippaUserModel
      .findOne({ email: ippaData.employeeEmail.toLowerCase().trim() })
      .populate({
        path: 'lineManager',
        select: '_id fullName email designation department',
        model: 'User',
      })
      .populate({
        path: 'departmentRef',
        select: '_id code description',
        model: 'Department',
      })
      .lean()
      .exec();

    const employeeMaster = await this.userModel.findOne({ email: ippaData.employeeEmail.toLowerCase().trim() })
      .select('_id username fullName email')
      .lean()

    // console.log(employeeQuery);

    if (!employeeQuery) {
      throw new NotFoundException('Employee not found');
    }

    // Type assertion for populated data
    const employee = employeeQuery as PopulatedIppaUser;

    // Check if IPPA already exists for this year
    const existingIPPA = await this.ippaSubmissionModel.findOne({
      employeeId: employee._id,
      financialYear: ippaData.financialYear,
    });

    if (existingIPPA && existingIPPA.status !== IPPAStatus.DRAFT) {
      throw new BadRequestException(
        'IPPA already submitted for this financial year',
      );
    }

    // Calculate employee scores
    const totalWeightage = ippaData.objectives.reduce(
      (sum, obj) => sum + obj.weightage,
      0,
    );
    const totalWeightedPoints = ippaData.objectives.reduce((sum, obj) => {
      const weightedPoints = (obj.employeeRating * obj.weightage) / 100;
      obj.employeeWeightedPoints = weightedPoints;
      return sum + weightedPoints;
    }, 0);
    const overallScore =
      totalWeightage > 0 ? totalWeightedPoints / (totalWeightage / 100) : 0;

    // Build approval workflow
    const approvalWorkflow = await this.buildApprovalWorkflow(employee, employeeMaster);

    // Safely extract line manager data
    let lineManagerId: any = null;
    let lineManagerName = '';
    let lineManagerEmail = '';

    if (isPopulatedLineManager(employee.lineManager)) {
      lineManagerId = employee.lineManager._id;
      lineManagerName = employee.lineManager.fullName;
      lineManagerEmail = employee.lineManager.email;
    }

    // Create or update IPPA submission
    const ippaSubmission = existingIPPA || new this.ippaSubmissionModel();

    Object.assign(ippaSubmission, {
      ippaId: existingIPPA?.ippaId || this.generateIPPAId(),
      employeeId: employee._id,
      employeeName: employee.fullName,
      employeeEmail: employee.email,
      employeeDepartment: employee.department,
      employeePosition: employee.position,
      employeeGrade: employee.employeeGrade,
      lineManagerId: lineManagerId,
      lineManagerName: lineManagerName,
      lineManagerEmail: lineManagerEmail,
      financialYear: ippaData.financialYear,
      reviewPeriod: ippaData.reviewPeriod,
      status: IPPAStatus.PENDING_SUPERVISOR_REVIEW,
      currentReviewerId: lineManagerId,
      objectives: ippaData.objectives,
      developmentPlans: ippaData.developmentPlans,
      approvalWorkflow: approvalWorkflow,
      currentWorkflowStep: 2, // Line manager review
      employeeTotalWeightage: totalWeightage,
      employeeTotalWeightedPoints: totalWeightedPoints,
      employeeOverallScore: overallScore,
      submissionDepartment: employee.department,
      submissionDepartmentCode: employee.departmentCode,
      submittedAt: new Date(),
      isDraft: false,
      lastModifiedBy: employee._id,
      lastModifiedAt: new Date(),
    });

    // Add employee review section
    ippaSubmission.reviews = [
      {
        reviewerType: ReviewerType.EMPLOYEE,
        reviewerId: employee._id,
        reviewerName: employee.fullName,
        remarks: ippaData.employeeRemarks || '',
        attachments: ippaData.employeeAttachments || [],
        overallScore: overallScore,
        reviewedAt: new Date(),
        department: employee.department,
      },
    ];

    return await ippaSubmission.save();
  }

  /**
   * Save as draft - FIXED VERSION
   */
  async saveDraft(
    employeeEmail: string,
    ippaData: any,
  ): Promise<IPPASubmissionDocument> {
    const employee = await this.ippaUserModel
      .findOne({ email: employeeEmail.toLowerCase().trim() })
      .lean();

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    let ippaSubmission = await this.ippaSubmissionModel.findOne({
      employeeId: employee._id,
      financialYear: ippaData.financialYear,
    });

    if (!ippaSubmission) {
      ippaSubmission = new this.ippaSubmissionModel({
        ippaId: this.generateIPPAId(),
        employeeId: employee._id,
        employeeName: employee.fullName,
        employeeEmail: employee.email,
        employeeDepartment: employee.department,
        employeePosition: employee.position,
        employeeGrade: employee.employeeGrade,
        submissionDepartment: employee.department,
        submissionDepartmentCode: employee.departmentCode,
        financialYear: ippaData.financialYear,
        status: IPPAStatus.DRAFT,
      });
    }

    // Update with draft data
    Object.assign(ippaSubmission, {
      objectives: ippaData.objectives || [],
      developmentPlans: ippaData.developmentPlans || [],
      reviewPeriod: ippaData.reviewPeriod,
      isDraft: true,
      lastModifiedBy: employee._id,
      lastModifiedAt: new Date(),
    });

    return await ippaSubmission.save();
  }

  /**
   * Get specific submission
   */
  async getSubmission(
    submissionId: string,
    userEmail: string,
  ): Promise<IPPASubmissionDocument> {
    const submission = await this.ippaSubmissionModel
      .findById(submissionId)
      .populate('employeeId', 'fullName email department')
      .populate('lineManagerId', 'fullName email designation')
      .exec();

    if (!submission) {
      throw new NotFoundException('IPPA submission not found');
    }

    // Check access rights
    const user = await this.ippaUserModel.findOne({ email: userEmail }).lean();
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Employee can access their own submission
    if (submission.employeeEmail === userEmail) {
      return submission;
    }

    // Line manager can access their reportee's submission
    if (submission.lineManagerEmail === userEmail) {
      return submission;
    }

    // Add HR and HOD access checks here
    throw new BadRequestException('Access denied');
  }

  // Helper method to find HR user(s)
  private async findHRUser(): Promise<any> {
    // Implementation depends on how you identify HR users
    return {
      _id: '674829e0f3d796aa36ff1e0d',
      fullName: 'Zamhari Zaidi',
      email: '<EMAIL>',
    };
  }

  // async getMySubmissions(
  //   employeeEmail: string,
  //   page: number = 1,
  //   limit: number = 10,
  //   status: IPPAStatus,
  //   filters?: {
  //     status?: IPPAStatus;
  //     financialYear?: string;
  //     sortBy?:
  //       | 'financialYear'
  //       | 'submittedAt'
  //       | 'lastModifiedAt'
  //       | 'performanceScore';
  //     sortOrder?: 'asc' | 'desc';
  //     search?: string;
  //   },
  // ): Promise<
  //   MySubmissionsResponse & {
  //     filters: {
  //       status?: IPPAStatus;
  //       financialYear?: string;
  //       sortBy?:
  //         | 'financialYear'
  //         | 'submittedAt'
  //         | 'lastModifiedAt'
  //         | 'performanceScore';
  //       sortOrder?: 'asc' | 'desc';
  //       search?: string;
  //     };
  //     approverInfo?: {
  //       name: string;
  //       email: string;
  //       department: string;
  //       roles: string[];
  //     };
  //   }
  // > {
  //   const employee = await this.ippaUserModel
  //     .findOne({ email: employeeEmail.toLowerCase().trim() })
  //     .populate('lineManager', 'fullName email designation')
  //     .lean();

  //   if (!employee) {
  //     throw new NotFoundException('Employee not found');
  //   }

  //   // Get user role information
  //   const userRole = await this.getUserIPPARole(employeeEmail);

  //   let queryFilter: any = {};
  //   let isToApproveRequest = false;

  //   // Handle 'to-approve' status specially
  //   if (status === 'to-review') {
  //     isToApproveRequest = true;

  //     // Combine both own submissions AND submissions to approve
  //     queryFilter = {
  //       $or: [
  //         // Own submissions
  //         { employeeId: employee._id },

  //         // Line Manager approvals
  //         {
  //           currentReviewerId: userRole.user.id,
  //           'approvalWorkflow.personId': userRole.user.id,
  //           status: IPPAStatus.PENDING_SUPERVISOR_REVIEW,
  //           'approvalWorkflow.stepType': 'supervisor',
  //         },
  //         // HOD approvals (if they are HOD)
  //         {
  //           status: IPPAStatus.PENDING_HOD_REVIEW,
  //           'approvalWorkflow.stepType': 'hod',
  //           'approvalWorkflow.personId': userRole.user.id,
  //           'approvalWorkflow.status': 'pending',
  //         },
  //         // HR approvals (if they are HR)
  //         {
  //           status: IPPAStatus.PENDING_HR_REVIEW,
  //           'approvalWorkflow.stepType': 'hr',
  //           'approvalWorkflow.personId': userRole.user.id,
  //           'approvalWorkflow.status': 'pending',
  //         },
  //       ],
  //     };
  //   } else {
  //     // Original logic for employee's own submissions only
  //     queryFilter = { employeeId: employee._id };

  //     if (status && status === IPPAStatus.SUBMITTED) {
  //       queryFilter.status = IPPAStatus.PENDING_SUPERVISOR_REVIEW;
  //     } else if (status) {
  //       queryFilter.status = status;
  //     }
  //   }

  //   // Add common filters
  //   if (filters?.financialYear) {
  //     queryFilter.financialYear = filters.financialYear;
  //   }

  //   // Add search functionality
  //   if (filters?.search) {
  //     const searchFields: any[] = [
  //       { ippaId: { $regex: filters.search, $options: 'i' } },
  //       { reviewPeriod: { $regex: filters.search, $options: 'i' } },
  //       { financialYear: { $regex: filters.search, $options: 'i' } },
  //     ];

  //     // For to-approve requests, also search by employee name and department
  //     if (isToApproveRequest) {
  //       searchFields.push(
  //         { employeeName: { $regex: filters.search, $options: 'i' } },
  //         { submissionDepartment: { $regex: filters.search, $options: 'i' } },
  //       );
  //     }

  //     const searchQuery = { $or: searchFields };

  //     if (queryFilter.$and) {
  //       queryFilter.$and.push(searchQuery);
  //     } else {
  //       queryFilter.$and = [searchQuery];
  //     }
  //   }

  //   // Get all submissions for status counting - always include both own and to-approve
  //   const ownSubmissionsFilter = { employeeId: employee._id };
  //   const toApproveFilter = {
  //     $or: [
  //       {
  //         currentReviewerId: userRole.user.id,
  //         'approvalWorkflow.personId': userRole.user.id,
  //         'approvalWorkflow.stepType': 'supervisor',
  //       },
  //       {
  //         'approvalWorkflow.stepType': 'hod',
  //         'approvalWorkflow.personId': userRole.user.id,
  //       },
  //       {
  //         'approvalWorkflow.stepType': 'hr',
  //         'approvalWorkflow.personId': userRole.user.id,
  //       },
  //     ],
  //   };

  //   // Get own submissions for status counting
  //   const ownSubmissions = await this.ippaSubmissionModel
  //     .find(ownSubmissionsFilter)
  //     .lean()
  //     .exec();

  //   // Get submissions to approve for status counting
  //   const toApproveSubmissions = await this.ippaSubmissionModel
  //     .find(toApproveFilter)
  //     .lean()
  //     .exec();

  //   // Filter to-approve submissions to only active pending ones
  //   const activeToApproveSubmissions = toApproveSubmissions.filter(
  //     (submission) => {
  //       return (
  //         (submission.status === IPPAStatus.PENDING_SUPERVISOR_REVIEW &&
  //           submission.currentReviewerId?.toString() === userRole.user.id) ||
  //         (submission.status === IPPAStatus.PENDING_HOD_REVIEW &&
  //           submission.approvalWorkflow?.some(
  //             (step) =>
  //               step.stepType === 'hod' &&
  //               step.personId === userRole.user.id &&
  //               step.status === 'pending',
  //           )) ||
  //         (submission.status === IPPAStatus.PENDING_HR_REVIEW &&
  //           submission.approvalWorkflow?.some(
  //             (step) =>
  //               step.stepType === 'hr' &&
  //               step.personId === userRole.user.id &&
  //               step.status === 'pending',
  //           ))
  //       );
  //     },
  //   );

  //   // Combine all submissions for status counting (avoiding duplicates)
  //   const allSubmissionIds = new Set([
  //     ...ownSubmissions.map((s) => s._id.toString()),
  //     ...activeToApproveSubmissions.map((s) => s._id.toString()),
  //   ]);

  //   const allSubmissions = [
  //     ...ownSubmissions,
  //     ...activeToApproveSubmissions.filter(
  //       (s) =>
  //         !ownSubmissions.some(
  //           (own) => own._id.toString() === s._id.toString(),
  //         ),
  //     ),
  //   ];

  //   // Calculate status information
  //   const statusInfo: SubmissionStatusInfo = {
  //     totalSubmissions: allSubmissions.length,
  //     approved: allSubmissions.filter(
  //       (s) => s.status === IPPAStatus.SUPERVISOR_REVIEWED,
  //     ).length,
  //     pendingReview: allSubmissions.filter(
  //       (s) => s.status === IPPAStatus.PENDING_HR_REVIEW,
  //     ).length,
  //     completed: allSubmissions.filter((s) => s.status === IPPAStatus.COMPLETED)
  //       .length,
  //     submitted: allSubmissions.filter(
  //       (s) => s.status === IPPAStatus.PENDING_SUPERVISOR_REVIEW,
  //     ).length,
  //     draft: allSubmissions.filter((s) => s.status === IPPAStatus.DRAFT).length,
  //   };

  //   // Add to-approve count
  //   statusInfo['toApprove'] = activeToApproveSubmissions.length;

  //   // Build sort criteria
  //   let sortCriteria: any = {};
  //   const sortBy = filters?.sortBy || 'financialYear';
  //   const sortOrder = filters?.sortOrder === 'asc' ? 1 : -1;

  //   switch (sortBy) {
  //     case 'financialYear':
  //       sortCriteria.financialYear = sortOrder;
  //       sortCriteria.createdAt = -1; // Secondary sort
  //       break;
  //     case 'submittedAt':
  //       sortCriteria.submittedAt = sortOrder;
  //       break;
  //     case 'lastModifiedAt':
  //       sortCriteria.lastModifiedAt = sortOrder;
  //       break;
  //     case 'performanceScore':
  //       // This will be handled after data retrieval since it's a calculated field
  //       sortCriteria.financialYear = -1;
  //       break;
  //     default:
  //       sortCriteria.financialYear = -1;
  //       sortCriteria.createdAt = -1;
  //   }

  //   // For to-approve requests, sort by submission date (oldest first)
  //   if (isToApproveRequest && !filters?.sortBy) {
  //     sortCriteria = { submittedAt: 1 };
  //   }

  //   // Get total count for pagination
  //   const totalCount =
  //     await this.ippaSubmissionModel.countDocuments(queryFilter);

  //   // Get paginated submissions with detailed information
  //   const skip = (page - 1) * limit;
  //   const submissions = await this.ippaSubmissionModel
  //     .find(queryFilter)
  //     .populate('currentReviewerId', 'fullName email department')
  //     .populate('employeeId', 'fullName email department position') // Add this for to-approve requests
  //     .sort(sortCriteria)
  //     .skip(skip)
  //     .limit(limit)
  //     .lean()
  //     .exec();

  //   // Format submissions for frontend
  //   let formattedSubmissions: SubmissionSummary[] = submissions.map(
  //     (submission): SubmissionSummary => {
  //       // Determine if this submission belongs to the current user or is for approval
  //       const isOwnSubmission =
  //         submission.employeeId.toString() === employee._id.toString();
  //       const isForApproval = !isOwnSubmission;

  //       // Calculate performance score (use supervisor score if available, otherwise employee score)
  //       const performanceScore =
  //         submission.supervisorOverallScore ||
  //         submission.employeeOverallScore ||
  //         0;
  //       const performanceRating =
  //         this.calculatePerformanceRating(performanceScore);

  //       // Get status display information
  //       const statusDisplay = this.getStatusDisplay(submission.status);

  //       // Count attachments from reviews
  //       const attachmentsCount =
  //         submission.reviews?.reduce((count, review) => {
  //           return count + (review.attachments?.length || 0);
  //         }, 0) || 0;

  //       // Get current reviewer information
  //       let currentReviewer;
  //       if (
  //         submission.currentReviewerId &&
  //         (submission as any).currentReviewerId?.fullName
  //       ) {
  //         currentReviewer = {
  //           name: (submission as any).currentReviewerId.fullName,
  //           email: (submission as any).currentReviewerId.email,
  //           department:
  //             (submission as any).currentReviewerId.department || 'N/A',
  //         };
  //       }

  //       // Calculate days since submission/last action
  //       const daysSinceAction = this.calculateDaysSinceAction(submission);

  //       // Check if submission is overdue
  //       const isOverdue = this.checkIfOverdue(submission);

  //       // Process objectives with detailed information
  //       const detailedObjectives = (submission.objectives || []).map(
  //         (obj, index) => ({
  //           index: index + 1,
  //           keyResultsArea: obj.keyResultsArea,
  //           workObjective: obj.workObjective,
  //           targetBase: obj.targetBase,
  //           targetExceed: obj.targetExceed,
  //           targetStretch: obj.targetStretch,
  //           weightage: obj.weightage,
  //           yearEndResults: obj.yearEndResults,
  //           performanceAssessment: obj.performanceAssessment,
  //           employeeRating: obj.employeeRating,
  //           employeeWeightedPoints: obj.employeeWeightedPoints || 0,
  //           supervisorRating: obj.supervisorRating || null,
  //           supervisorWeightedPoints: obj.supervisorWeightedPoints || null,
  //           supervisorComments: obj.supervisorComments || null,
  //           hasEmployeeRating: !!obj.employeeRating,
  //           hasSupervisorRating: !!obj.supervisorRating,
  //         }),
  //       );

  //       // Process development plans with detailed information
  //       const detailedDevelopmentPlans = (
  //         submission.developmentPlans || []
  //       ).map((plan, index) => ({
  //         index: index + 1,
  //         competencyType: plan.competencyType,
  //         areaForImprovement: plan.areaForImprovement,
  //         activity: plan.activity,
  //         timeline: plan.timeline,
  //         yearEndComments: plan.yearEndComments || '',
  //         supervisorFeedback: plan.supervisorFeedback || null,
  //         hodRemarks: plan.hodRemarks || null,
  //         hasYearEndComments: !!plan.yearEndComments,
  //         hasSupervisorFeedback: !!plan.supervisorFeedback,
  //         hasHodRemarks: !!plan.hodRemarks,
  //       }));

  //       // Process reviews with detailed attachments
  //       const detailedReviews = (submission.reviews || []).map((review) => ({
  //         reviewerType: review.reviewerType,
  //         reviewerId: review.reviewerId,
  //         reviewerName: review.reviewerName,
  //         remarks: review.remarks || '',
  //         attachments: (review.attachments || []).map((attachment) => ({
  //           fileName: attachment.fileName,
  //           filePath: attachment.filePath,
  //           fileSize: attachment.fileSize,
  //           mimeType: attachment.mimeType,
  //           uploadedAt: attachment.uploadedAt,
  //           description: attachment.description || '',
  //           fileSizeFormatted: this.formatFileSize(attachment.fileSize),
  //         })),
  //         overallScore: review.overallScore || null,
  //         reviewedAt: review.reviewedAt,
  //         department: review.department || '',
  //         attachmentCount: review.attachments?.length || 0,
  //         hasRemarks: !!review.remarks,
  //         hasAttachments: (review.attachments?.length || 0) > 0,
  //       }));

  //       // Process approval workflow with enhanced details
  //       const detailedApprovalWorkflow = (
  //         submission.approvalWorkflow || []
  //       ).map((step) => ({
  //         stepId: step.stepId,
  //         title: step.title,
  //         personId: step.personId,
  //         personName: step.personName,
  //         department: step.department || '',
  //         date: step.date,
  //         status: step.status,
  //         isCompleted: step.status === 'completed',
  //         isPending: step.status === 'pending',
  //         isUpcoming: step.status === 'upcoming',
  //         statusColor: this.getWorkflowStepColor(step.status),
  //         formattedDate: step.date !== '-' ? step.date : 'Pending',
  //       }));

  //       // Aggregate all attachments across reviews
  //       const allAttachmentsFlat = detailedReviews.reduce((acc, review) => {
  //         const reviewAttachments = review.attachments.map((att) => ({
  //           ...att,
  //           reviewerType: review.reviewerType,
  //           reviewerName: review.reviewerName,
  //           reviewedAt: review.reviewedAt,
  //         }));
  //         return [...acc, ...reviewAttachments];
  //       }, []);

  //       // Base submission summary - use submission's employee data for to-approve submissions
  //       const submissionEmployee = isOwnSubmission
  //         ? employee
  //         : {
  //             _id: submission.employeeId,
  //             fullName: submission.employeeName,
  //             email: (submission as any).employeeId?.email || '',
  //             department: submission.submissionDepartment,
  //             position: submission.employeePosition,
  //           };

  //       const submissionSummary: SubmissionSummary = {
  //         ippaId: submission.ippaId,
  //         submissionId: submission._id.toString(),
  //         employee: employee,
  //         employeeName: submission.employeeName,
  //         employeeId: submission.employeeId.toString(),
  //         department: submission.submissionDepartment,
  //         departmentCode: submission.submissionDepartmentCode,
  //         position: submission.employeePosition,
  //         grade: submission.employeeGrade,
  //         status: submission.status,
  //         statusLabel: statusDisplay.label,
  //         statusColor: statusDisplay.color,
  //         performanceScore: parseFloat(performanceScore.toFixed(1)),
  //         performanceRating: performanceRating.rating,
  //         totalObjectives: submission.objectives?.length || 0,
  //         totalDevelopmentPlans: submission.developmentPlans?.length || 0,
  //         attachmentsCount: attachmentsCount,
  //         submittedAt: submission.submittedAt || null,
  //         lastModifiedAt: submission.lastModifiedAt || null,
  //         financialYear: submission.financialYear,
  //         reviewPeriod: submission.reviewPeriod,
  //         currentReviewer: currentReviewer,
  //         approvalWorkflow: submission.approvalWorkflow || [],
  //         isDraft: submission.isDraft || false,
  //         daysSinceAction: daysSinceAction,
  //         isOverdue: isOverdue,
  //         canEdit: isOwnSubmission ? this.canEditSubmission(submission) : false,
  //         nextAction: isOwnSubmission
  //           ? this.getNextAction(submission)
  //           : 'Review Required',

  //         // Detailed information
  //         objectives: detailedObjectives,
  //         developmentPlans: detailedDevelopmentPlans,
  //         reviews: detailedReviews,
  //         workflowDetails: detailedApprovalWorkflow,
  //         allAttachments: allAttachmentsFlat,

  //         // Summary statistics
  //         summaryStats: {
  //           totalWeightage: detailedObjectives.reduce(
  //             (sum, obj) => sum + obj.weightage,
  //             0,
  //           ),
  //           completedObjectives: detailedObjectives.filter(
  //             (obj) => obj.hasSupervisorRating,
  //           ).length,
  //           pendingObjectives: detailedObjectives.filter(
  //             (obj) => !obj.hasSupervisorRating,
  //           ).length,
  //           developmentPlansWithFeedback: detailedDevelopmentPlans.filter(
  //             (plan) => plan.hasSupervisorFeedback,
  //           ).length,
  //           totalAttachments: allAttachmentsFlat.length,
  //           reviewsCompleted: detailedReviews.length,
  //           workflowStepsCompleted: detailedApprovalWorkflow.filter(
  //             (step) => step.isCompleted,
  //           ).length,
  //           workflowStepsPending: detailedApprovalWorkflow.filter(
  //             (step) => step.isPending,
  //           ).length,
  //         },

  //         // Add flags to identify submission type
  //         isOwnSubmission: isOwnSubmission,
  //         isForApproval: isForApproval,
  //       };

  //       // Add approval-specific information for submissions requiring approval
  //       if (isForApproval && userRole) {
  //         const approverRole = this.getApproverRole(
  //           submission,
  //           userRole.user.id,
  //         );
  //         const canApprove = this.canApprove(submission, userRole.user.id);
  //         const daysPending = this.calculateDaysPending(submission);
  //         const nextActionForApprover = this.getNextActionForApprover(
  //           submission,
  //           approverRole,
  //         );

  //         submissionSummary['approverRole'] = approverRole;
  //         submissionSummary['canApprove'] = canApprove;
  //         submissionSummary['daysPending'] = daysPending;
  //         submissionSummary['nextActionForApprover'] = nextActionForApprover;
  //       }

  //       return submissionSummary;
  //     },
  //   );

  //   // Handle performance score sorting if requested
  //   if (filters?.sortBy === 'performanceScore') {
  //     formattedSubmissions.sort((a, b) => {
  //       const comparison = a.performanceScore - b.performanceScore;
  //       return filters.sortOrder === 'asc' ? comparison : -comparison;
  //     });
  //   }

  //   const response = {
  //     statusInfo: statusInfo,
  //     submissions: formattedSubmissions,
  //     totalCount: totalCount,
  //     pagination: {
  //       currentPage: page,
  //       totalPages: Math.ceil(totalCount / limit),
  //       pageSize: limit,
  //       hasNextPage: page < Math.ceil(totalCount / limit),
  //       hasPrevPage: page > 1,
  //     },
  //     filters: filters || {},
  //   } as any;

  //   // Add approver information for to-approve requests
  //   if (isToApproveRequest && userRole) {
  //     response['approverInfo'] = {
  //       name: userRole.user.fullName,
  //       email: userRole.user.email,
  //       department: userRole.user.department,
  //       roles: userRole.roles,
  //     };
  //   }

  //   return response;
  // }

  // async getMySubmissions(
  //   employeeEmail: string,
  //   page: number = 1,
  //   limit: number = 10,
  //   status: IPPAStatus | 'to-review' | 'all',
  //   filters?: {
  //     status?: IPPAStatus | 'to-review' | 'all';
  //     financialYear?: string;
  //     sortBy?:
  //       | 'financialYear'
  //       | 'submittedAt'
  //       | 'lastModifiedAt'
  //       | 'performanceScore';
  //     sortOrder?: 'asc' | 'desc';
  //     search?: string;
  //   },
  // ): Promise<
  //   MySubmissionsResponse & {
  //     filters: {
  //       status?: IPPAStatus | 'to-review' | 'all';
  //       financialYear?: string;
  //       sortBy?:
  //         | 'financialYear'
  //         | 'submittedAt'
  //         | 'lastModifiedAt'
  //         | 'performanceScore';
  //       sortOrder?: 'asc' | 'desc';
  //       search?: string;
  //     };
  //     approverInfo?: {
  //       name: string;
  //       email: string;
  //       department: string;
  //       roles: string[];
  //     };
  //   }
  // > {
  //   const employee = await this.ippaUserModel
  //     .findOne({ email: employeeEmail.toLowerCase().trim() })
  //     .populate('lineManager', 'fullName email designation')
  //     .lean();

  //   if (!employee) {
  //     throw new NotFoundException('Employee not found');
  //   }

  //   // Get user role information
  //   const userRole = await this.getUserIPPARole(employeeEmail);

  //   let queryFilter: any = {};
  //   let isToReviewRequest = false;
  //   let isAllRequest = false;

  //   // Handle special statuses
  //   if (status === 'to-review' || filters?.status === 'to-review') {
  //     isToReviewRequest = true;

  //     // Only submissions to approve (NOT own submissions)
  //     queryFilter = {
  //       $or: [
  //         // Line Manager approvals
  //         {
  //           currentReviewerId: userRole.user.id,
  //           'approvalWorkflow.personId': userRole.user.id,
  //           status: IPPAStatus.PENDING_SUPERVISOR_REVIEW,
  //           'approvalWorkflow.stepType': 'supervisor',
  //           employeeId: { $ne: employee._id }, // Exclude own submissions
  //         },
  //         // HOD approvals (if they are HOD)
  //         {
  //           status: IPPAStatus.PENDING_HOD_REVIEW,
  //           'approvalWorkflow.stepType': 'hod',
  //           'approvalWorkflow.personId': userRole.user.id,
  //           'approvalWorkflow.status': 'pending',
  //           employeeId: { $ne: employee._id }, // Exclude own submissions
  //         },
  //         // HR approvals (if they are HR)
  //         {
  //           status: IPPAStatus.PENDING_HR_REVIEW,
  //           'approvalWorkflow.stepType': 'hr',
  //           'approvalWorkflow.personId': userRole.user.id,
  //           'approvalWorkflow.status': 'pending',
  //           employeeId: { $ne: employee._id }, // Exclude own submissions
  //         },
  //       ],
  //     };
  //   } else if (status === 'all' || filters?.status === 'all') {
  //     isAllRequest = true;

  //     // Combine both own submissions AND submissions to approve
  //     queryFilter = {
  //       $or: [
  //         // Own submissions
  //         { employeeId: employee._id },

  //         // Line Manager approvals
  //         {
  //           currentReviewerId: userRole.user.id,
  //           'approvalWorkflow.personId': userRole.user.id,
  //           status: IPPAStatus.PENDING_SUPERVISOR_REVIEW,
  //           'approvalWorkflow.stepType': 'supervisor',
  //         },
  //         // HOD approvals (if they are HOD)
  //         {
  //           status: IPPAStatus.PENDING_HOD_REVIEW,
  //           'approvalWorkflow.stepType': 'hod',
  //           'approvalWorkflow.personId': userRole.user.id,
  //           'approvalWorkflow.status': 'pending',
  //         },
  //         // HR approvals (if they are HR)
  //         {
  //           status: IPPAStatus.PENDING_HR_REVIEW,
  //           'approvalWorkflow.stepType': 'hr',
  //           'approvalWorkflow.personId': userRole.user.id,
  //           'approvalWorkflow.status': 'pending',
  //         },
  //       ],
  //     };
  //   } else {
  //     // Specific status - only employee's own submissions
  //     queryFilter = { employeeId: employee._id };

  //     if (status && status === IPPAStatus.SUBMITTED) {
  //       queryFilter.status = IPPAStatus.PENDING_SUPERVISOR_REVIEW;
  //     } else if (status) {
  //       queryFilter.status = status;
  //     }
  //   }

  //   // Add common filters
  //   if (filters?.financialYear) {
  //     queryFilter.financialYear = filters.financialYear;
  //   }

  //   // Add search functionality
  //   if (filters?.search) {
  //     const searchFields: any[] = [
  //       { ippaId: { $regex: filters.search, $options: 'i' } },
  //       { reviewPeriod: { $regex: filters.search, $options: 'i' } },
  //       { financialYear: { $regex: filters.search, $options: 'i' } },
  //     ];

  //     // For to-review and all requests, also search by employee name and department
  //     if (isToReviewRequest || isAllRequest) {
  //       searchFields.push(
  //         { employeeName: { $regex: filters.search, $options: 'i' } },
  //         { submissionDepartment: { $regex: filters.search, $options: 'i' } },
  //       );
  //     }

  //     const searchQuery = { $or: searchFields };

  //     if (queryFilter.$and) {
  //       queryFilter.$and.push(searchQuery);
  //     } else {
  //       queryFilter.$and = [searchQuery];
  //     }
  //   }

  //   // Get all submissions for status counting - always get comprehensive counts
  //   const ownSubmissionsFilter = { employeeId: employee._id };
  //   const toReviewFilter = {
  //     $or: [
  //       {
  //         currentReviewerId: userRole.user.id,
  //         'approvalWorkflow.personId': userRole.user.id,
  //         'approvalWorkflow.stepType': 'supervisor',
  //         employeeId: { $ne: employee._id }, // Exclude own submissions from to-review count
  //       },
  //       {
  //         'approvalWorkflow.stepType': 'hod',
  //         'approvalWorkflow.personId': userRole.user.id,
  //         employeeId: { $ne: employee._id }, // Exclude own submissions from to-review count
  //       },
  //       {
  //         'approvalWorkflow.stepType': 'hr',
  //         'approvalWorkflow.personId': userRole.user.id,
  //         employeeId: { $ne: employee._id }, // Exclude own submissions from to-review count
  //       },
  //     ],
  //   };

  //   // Get own submissions for status counting
  //   const ownSubmissions = await this.ippaSubmissionModel
  //     .find(ownSubmissionsFilter)
  //     .lean()
  //     .exec();

  //   // Get submissions to review for status counting
  //   const toReviewSubmissions = await this.ippaSubmissionModel
  //     .find(toReviewFilter)
  //     .lean()
  //     .exec();

  //   // Filter to-review submissions to only active pending ones
  //   const activeToReviewSubmissions = toReviewSubmissions.filter(
  //     (submission) => {
  //       return (
  //         (submission.status === IPPAStatus.PENDING_SUPERVISOR_REVIEW &&
  //           submission.currentReviewerId?.toString() === userRole.user.id) ||
  //         (submission.status === IPPAStatus.PENDING_HOD_REVIEW &&
  //           submission.approvalWorkflow?.some(
  //             (step) =>
  //               step.stepType === 'hod' &&
  //               step.personId === userRole.user.id &&
  //               step.status === 'pending',
  //           )) ||
  //         (submission.status === IPPAStatus.PENDING_HR_REVIEW &&
  //           submission.approvalWorkflow?.some(
  //             (step) =>
  //               step.stepType === 'hr' &&
  //               step.personId === userRole.user.id &&
  //               step.status === 'pending',
  //           ))
  //       );
  //     },
  //   );

  //   // Combine all submissions for comprehensive status counting (avoiding duplicates)
  //   const allSubmissions = [
  //     ...ownSubmissions,
  //     ...activeToReviewSubmissions.filter(
  //       (s) =>
  //         !ownSubmissions.some(
  //           (own) => own._id.toString() === s._id.toString(),
  //         ),
  //     ),
  //   ];

  //   // Calculate status information based on own submissions
  //   const statusInfo: SubmissionStatusInfo = {
  //     totalSubmissions: ownSubmissions.length,
  //     approved: ownSubmissions.filter(
  //       (s) => s.status === IPPAStatus.SUPERVISOR_REVIEWED,
  //     ).length,
  //     pendingReview: ownSubmissions.filter(
  //       (s) => s.status === IPPAStatus.PENDING_HR_REVIEW,
  //     ).length,
  //     completed: ownSubmissions.filter((s) => s.status === IPPAStatus.COMPLETED)
  //       .length,
  //     submitted: ownSubmissions.filter(
  //       (s) => s.status === IPPAStatus.PENDING_SUPERVISOR_REVIEW,
  //     ).length,
  //     draft: ownSubmissions.filter((s) => s.status === IPPAStatus.DRAFT).length,
  //   };

  //   // Add special counts
  //   statusInfo['toReview'] = activeToReviewSubmissions.length;
  //   statusInfo['all'] = allSubmissions.length;

  //   // Build sort criteria
  //   let sortCriteria: any = {};
  //   const sortBy = filters?.sortBy || 'financialYear';
  //   const sortOrder = filters?.sortOrder === 'asc' ? 1 : -1;

  //   switch (sortBy) {
  //     case 'financialYear':
  //       sortCriteria.financialYear = sortOrder;
  //       sortCriteria.createdAt = -1; // Secondary sort
  //       break;
  //     case 'submittedAt':
  //       sortCriteria.submittedAt = sortOrder;
  //       break;
  //     case 'lastModifiedAt':
  //       sortCriteria.lastModifiedAt = sortOrder;
  //       break;
  //     case 'performanceScore':
  //       // This will be handled after data retrieval since it's a calculated field
  //       sortCriteria.financialYear = -1;
  //       break;
  //     default:
  //       sortCriteria.financialYear = -1;
  //       sortCriteria.createdAt = -1;
  //   }

  //   // For to-review requests, sort by submission date (oldest first)
  //   if (isToReviewRequest && !filters?.sortBy) {
  //     sortCriteria = { submittedAt: 1 };
  //   }

  //   // Get total count for pagination
  //   const totalCount =
  //     await this.ippaSubmissionModel.countDocuments(queryFilter);

  //   // Get paginated submissions with detailed information
  //   const skip = (page - 1) * limit;
  //   const submissions = await this.ippaSubmissionModel
  //     .find(queryFilter)
  //     .populate('currentReviewerId', 'fullName email department')
  //     .populate('employeeId', 'fullName email department position')
  //     .sort(sortCriteria)
  //     .skip(skip)
  //     .limit(limit)
  //     .lean()
  //     .exec();

  //   // Format submissions for frontend
  //   let formattedSubmissions: SubmissionSummary[] = submissions.map(
  //     (submission): SubmissionSummary => {
  //       // Determine if this submission belongs to the current user or is for approval
  //       const isOwnSubmission =
  //         submission.employeeId.toString() === employee._id.toString();
  //       const isForApproval = !isOwnSubmission;

  //       // Calculate performance score (use supervisor score if available, otherwise employee score)
  //       const performanceScore =
  //         submission.supervisorOverallScore ||
  //         submission.employeeOverallScore ||
  //         0;
  //       const performanceRating =
  //         this.calculatePerformanceRating(performanceScore);

  //       // Get status display information
  //       const statusDisplay = this.getStatusDisplay(submission.status);

  //       // Count attachments from reviews
  //       const attachmentsCount =
  //         submission.reviews?.reduce((count, review) => {
  //           return count + (review.attachments?.length || 0);
  //         }, 0) || 0;

  //       // Get current reviewer information
  //       let currentReviewer;
  //       if (
  //         submission.currentReviewerId &&
  //         (submission as any).currentReviewerId?.fullName
  //       ) {
  //         currentReviewer = {
  //           name: (submission as any).currentReviewerId.fullName,
  //           email: (submission as any).currentReviewerId.email,
  //           department:
  //             (submission as any).currentReviewerId.department || 'N/A',
  //         };
  //       }

  //       // Calculate days since submission/last action
  //       const daysSinceAction = this.calculateDaysSinceAction(submission);

  //       // Check if submission is overdue
  //       const isOverdue = this.checkIfOverdue(submission);

  //       // Process objectives with detailed information
  //       const detailedObjectives = (submission.objectives || []).map(
  //         (obj, index) => ({
  //           index: index + 1,
  //           keyResultsArea: obj.keyResultsArea,
  //           workObjective: obj.workObjective,
  //           targetBase: obj.targetBase,
  //           targetExceed: obj.targetExceed,
  //           targetStretch: obj.targetStretch,
  //           weightage: obj.weightage,
  //           yearEndResults: obj.yearEndResults,
  //           performanceAssessment: obj.performanceAssessment,
  //           employeeRating: obj.employeeRating,
  //           employeeWeightedPoints: obj.employeeWeightedPoints || 0,
  //           supervisorRating: obj.supervisorRating || null,
  //           supervisorWeightedPoints: obj.supervisorWeightedPoints || null,
  //           supervisorComments: obj.supervisorComments || null,
  //           hasEmployeeRating: !!obj.employeeRating,
  //           hasSupervisorRating: !!obj.supervisorRating,
  //         }),
  //       );

  //       // Process development plans with detailed information
  //       const detailedDevelopmentPlans = (
  //         submission.developmentPlans || []
  //       ).map((plan, index) => ({
  //         index: index + 1,
  //         competencyType: plan.competencyType,
  //         areaForImprovement: plan.areaForImprovement,
  //         activity: plan.activity,
  //         timeline: plan.timeline,
  //         yearEndComments: plan.yearEndComments || '',
  //         supervisorFeedback: plan.supervisorFeedback || null,
  //         hodRemarks: plan.hodRemarks || null,
  //         hasYearEndComments: !!plan.yearEndComments,
  //         hasSupervisorFeedback: !!plan.supervisorFeedback,
  //         hasHodRemarks: !!plan.hodRemarks,
  //       }));

  //       // Process reviews with detailed attachments
  //       const detailedReviews = (submission.reviews || []).map((review) => ({
  //         reviewerType: review.reviewerType,
  //         reviewerId: review.reviewerId,
  //         reviewerName: review.reviewerName,
  //         remarks: review.remarks || '',
  //         attachments: (review.attachments || []).map((attachment) => ({
  //           fileName: attachment.fileName,
  //           filePath: attachment.filePath,
  //           fileSize: attachment.fileSize,
  //           mimeType: attachment.mimeType,
  //           uploadedAt: attachment.uploadedAt,
  //           description: attachment.description || '',
  //           fileSizeFormatted: this.formatFileSize(attachment.fileSize),
  //         })),
  //         overallScore: review.overallScore || null,
  //         reviewedAt: review.reviewedAt,
  //         department: review.department || '',
  //         attachmentCount: review.attachments?.length || 0,
  //         hasRemarks: !!review.remarks,
  //         hasAttachments: (review.attachments?.length || 0) > 0,
  //       }));

  //       // Process approval workflow with enhanced details
  //       const detailedApprovalWorkflow = (
  //         submission.approvalWorkflow || []
  //       ).map((step) => ({
  //         stepId: step.stepId,
  //         title: step.title,
  //         personId: step.personId,
  //         personName: step.personName,
  //         department: step.department || '',
  //         date: step.date,
  //         status: step.status,
  //         isCompleted: step.status === 'completed',
  //         isPending: step.status === 'pending',
  //         isUpcoming: step.status === 'upcoming',
  //         statusColor: this.getWorkflowStepColor(step.status),
  //         formattedDate: step.date !== '-' ? step.date : 'Pending',
  //       }));

  //       // Aggregate all attachments across reviews
  //       const allAttachmentsFlat = detailedReviews.reduce((acc, review) => {
  //         const reviewAttachments = review.attachments.map((att) => ({
  //           ...att,
  //           reviewerType: review.reviewerType,
  //           reviewerName: review.reviewerName,
  //           reviewedAt: review.reviewedAt,
  //         }));
  //         return [...acc, ...reviewAttachments];
  //       }, []);

  //       const submissionSummary: SubmissionSummary = {
  //         ippaId: submission.ippaId,
  //         submissionId: submission._id.toString(),
  //         employee: employee, // Always use logged-in employee for type consistency
  //         employeeName: submission.employeeName,
  //         employeeId: submission.employeeId.toString(),
  //         department: submission.submissionDepartment,
  //         departmentCode: submission.submissionDepartmentCode,
  //         position: submission.employeePosition,
  //         grade: submission.employeeGrade,
  //         status: submission.status,
  //         statusLabel: statusDisplay.label,
  //         statusColor: statusDisplay.color,
  //         performanceScore: parseFloat(performanceScore.toFixed(1)),
  //         performanceRating: performanceRating.rating,
  //         totalObjectives: submission.objectives?.length || 0,
  //         totalDevelopmentPlans: submission.developmentPlans?.length || 0,
  //         attachmentsCount: attachmentsCount,
  //         submittedAt: submission.submittedAt || null,
  //         lastModifiedAt: submission.lastModifiedAt || null,
  //         financialYear: submission.financialYear,
  //         reviewPeriod: submission.reviewPeriod,
  //         currentReviewer: currentReviewer,
  //         approvalWorkflow: submission.approvalWorkflow || [],
  //         isDraft: submission.isDraft || false,
  //         daysSinceAction: daysSinceAction,
  //         isOverdue: isOverdue,
  //         canEdit: isOwnSubmission ? this.canEditSubmission(submission) : false,
  //         nextAction: isOwnSubmission
  //           ? this.getNextAction(submission)
  //           : 'Review Required',

  //         // Detailed information
  //         objectives: detailedObjectives,
  //         developmentPlans: detailedDevelopmentPlans,
  //         reviews: detailedReviews,
  //         workflowDetails: detailedApprovalWorkflow,
  //         allAttachments: allAttachmentsFlat,

  //         // Summary statistics
  //         summaryStats: {
  //           totalWeightage: detailedObjectives.reduce(
  //             (sum, obj) => sum + obj.weightage,
  //             0,
  //           ),
  //           completedObjectives: detailedObjectives.filter(
  //             (obj) => obj.hasSupervisorRating,
  //           ).length,
  //           pendingObjectives: detailedObjectives.filter(
  //             (obj) => !obj.hasSupervisorRating,
  //           ).length,
  //           developmentPlansWithFeedback: detailedDevelopmentPlans.filter(
  //             (plan) => plan.hasSupervisorFeedback,
  //           ).length,
  //           totalAttachments: allAttachmentsFlat.length,
  //           reviewsCompleted: detailedReviews.length,
  //           workflowStepsCompleted: detailedApprovalWorkflow.filter(
  //             (step) => step.isCompleted,
  //           ).length,
  //           workflowStepsPending: detailedApprovalWorkflow.filter(
  //             (step) => step.isPending,
  //           ).length,
  //         },

  //         // Add flags to identify submission type
  //         isOwnSubmission: isOwnSubmission,
  //         isForApproval: isForApproval,
  //       };

  //       // Add approval-specific information for submissions requiring approval
  //       if (isForApproval && userRole) {
  //         const approverRole = this.getApproverRole(
  //           submission,
  //           userRole.user.id,
  //         );
  //         const canApprove = this.canApprove(submission, userRole.user.id);
  //         const daysPending = this.calculateDaysPending(submission);
  //         const nextActionForApprover = this.getNextActionForApprover(
  //           submission,
  //           approverRole,
  //         );

  //         submissionSummary['approverRole'] = approverRole;
  //         submissionSummary['canApprove'] = canApprove;
  //         submissionSummary['daysPending'] = daysPending;
  //         submissionSummary['nextActionForApprover'] = nextActionForApprover;
  //       }

  //       return submissionSummary;
  //     },
  //   );

  //   // Handle performance score sorting if requested
  //   if (filters?.sortBy === 'performanceScore') {
  //     formattedSubmissions.sort((a, b) => {
  //       const comparison = a.performanceScore - b.performanceScore;
  //       return filters.sortOrder === 'asc' ? comparison : -comparison;
  //     });
  //   }

  //   const response = {
  //     statusInfo: statusInfo,
  //     submissions: formattedSubmissions,
  //     totalCount: totalCount,
  //     pagination: {
  //       currentPage: page,
  //       totalPages: Math.ceil(totalCount / limit),
  //       pageSize: limit,
  //       hasNextPage: page < Math.ceil(totalCount / limit),
  //       hasPrevPage: page > 1,
  //     },
  //     filters: filters || {},
  //   } as any;

  //   // Add approver information for to-review and all requests
  //   if ((isToReviewRequest || isAllRequest) && userRole) {
  //     response['approverInfo'] = {
  //       name: userRole.user.fullName,
  //       email: userRole.user.email,
  //       department: userRole.user.department,
  //       roles: userRole.roles,
  //     };
  //   }

  //   return response;
  // }

  async getMySubmissions(
    employeeEmail: string,
    page: number = 1,
    limit: number = 10,
    status: IPPAStatus,
    filters?: {
      status?: IPPAStatus;
      financialYear?: string;
      sortBy?:
      | 'financialYear'
      | 'submittedAt'
      | 'lastModifiedAt'
      | 'performanceScore';
      sortOrder?: 'asc' | 'desc';
      search?: string;
    },
  ): Promise<
    MySubmissionsResponse & {
      filters: {
        status?: IPPAStatus | 'to-review' | 'all';
        financialYear?: string;
        sortBy?:
        | 'financialYear'
        | 'submittedAt'
        | 'lastModifiedAt'
        | 'performanceScore';
        sortOrder?: 'asc' | 'desc';
        search?: string;
      };
      approverInfo?: {
        name: string;
        email: string;
        department: string;
        roles: string[];
      };
    }
  > {
    const employee = await this.ippaUserModel
      .findOne({ email: employeeEmail.toLowerCase().trim() })
      .populate('lineManager', 'fullName email designation')
      .lean();

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    // Get user role information
    const userRole = await this.getUserIPPARole(employeeEmail);

    let queryFilter: any = {};
    let isToReviewRequest = false;
    let isAllRequest = false;

    // Handle special statuses
    if (status === 'to-review' || filters?.status === 'to-review') {
      isToReviewRequest = true;

      // For to-review, we don't use the normal queryFilter at all
      // We'll handle this in the submission fetching logic below
      queryFilter = {}; // Empty filter - we'll handle everything manually
    } else if (status == null || filters?.status === null) {
      isAllRequest = true;

      // Combine both own submissions AND submissions to approve
      queryFilter = {
        $or: [
          // Own submissions
          { employeeId: employee._id },

          // Line Manager approvals
          {
            currentReviewerId: userRole.user.id,
            'approvalWorkflow.personId': userRole.user.id,
            status: IPPAStatus.PENDING_SUPERVISOR_REVIEW,
            'approvalWorkflow.stepType': 'supervisor',
          },
          // HOD approvals (if they are HOD)
          {
            status: IPPAStatus.PENDING_HOD_REVIEW,
            'approvalWorkflow.stepType': 'hod',
            'approvalWorkflow.personId': userRole.user.id,
            'approvalWorkflow.status': 'pending',
          },
          // HR approvals (if they are HR)
          {
            status: IPPAStatus.PENDING_HR_REVIEW,
            'approvalWorkflow.stepType': 'hr',
            'approvalWorkflow.personId': userRole.user.id,
            'approvalWorkflow.status': 'pending',
          },
        ],
      };
    } else {
      // Specific status - only employee's own submissions
      queryFilter = { employeeId: employee._id };

      if (status && status === IPPAStatus.SUBMITTED) {
        queryFilter.status = IPPAStatus.PENDING_SUPERVISOR_REVIEW;
      } else if (status) {
        queryFilter.status = status;
      }
    }

    // Add common filters
    if (filters?.financialYear) {
      queryFilter.financialYear = filters.financialYear;
    }

    // Add search functionality
    if (filters?.search) {
      const searchFields: any[] = [
        { ippaId: { $regex: filters.search, $options: 'i' } },
        { reviewPeriod: { $regex: filters.search, $options: 'i' } },
        { financialYear: { $regex: filters.search, $options: 'i' } },
      ];

      // For to-review and all requests, also search by employee name and department
      if (isToReviewRequest || isAllRequest) {
        searchFields.push(
          { employeeName: { $regex: filters.search, $options: 'i' } },
          { submissionDepartment: { $regex: filters.search, $options: 'i' } },
        );
      }

      const searchQuery = { $or: searchFields };

      if (queryFilter.$and) {
        queryFilter.$and.push(searchQuery);
      } else {
        queryFilter.$and = [searchQuery];
      }
    }

    // Get all submissions for status counting - always get comprehensive counts
    const ownSubmissionsFilter = { employeeId: employee._id };
    const toReviewFilter = {
      $or: [
        {
          currentReviewerId: userRole.user.id,
          'approvalWorkflow.personId': userRole.user.id,
          'approvalWorkflow.stepType': 'supervisor',
          employeeId: { $ne: employee._id }, // Exclude own submissions from to-review count
        },
        {
          'approvalWorkflow.stepType': 'hod',
          'approvalWorkflow.personId': userRole.user.id,
          employeeId: { $ne: employee._id }, // Exclude own submissions from to-review count
        },
        {
          'approvalWorkflow.stepType': 'hr',
          'approvalWorkflow.personId': userRole.user.id,
          employeeId: { $ne: employee._id }, // Exclude own submissions from to-review count
        },
      ],
    };

    // Get own submissions for status counting
    const ownSubmissions = await this.ippaSubmissionModel
      .find(ownSubmissionsFilter)
      .lean()
      .exec();

    // Get submissions to review for status counting
    const toReviewSubmissions = await this.ippaSubmissionModel
      .find(toReviewFilter)
      .lean()
      .exec();

    // Filter to-review submissions to only active pending ones
    const activeToReviewSubmissions = toReviewSubmissions.filter(
      (submission) => {
        return (
          (submission.status === IPPAStatus.PENDING_SUPERVISOR_REVIEW &&
            submission.currentReviewerId?.toString() === userRole.user.id) ||
          (submission.status === IPPAStatus.PENDING_HOD_REVIEW &&
            submission.approvalWorkflow?.some(
              (step) =>
                step.stepType === 'hod' &&
                step.personId === userRole.user.id &&
                step.status === 'pending',
            )) ||
          (submission.status === IPPAStatus.PENDING_HR_REVIEW &&
            submission.approvalWorkflow?.some(
              (step) =>
                step.stepType === 'hr' &&
                step.personId === userRole.user.id &&
                step.status === 'pending',
            ))
        );
      },
    );

    // Combine all submissions for comprehensive status counting (avoiding duplicates)
    const allSubmissions = [
      ...ownSubmissions,
      ...activeToReviewSubmissions.filter(
        (s) =>
          !ownSubmissions.some(
            (own) => own._id.toString() === s._id.toString(),
          ),
      ),
    ];
    // Calculate status information based on own submissions
    const statusInfo: SubmissionStatusInfo = {
      // totalSubmissions: allSubmissions.length,
      totalSubmissions: ownSubmissions.length,
      approved: ownSubmissions.filter(
        (s) => s.status === IPPAStatus.SUPERVISOR_REVIEWED,
      ).length,
      pendingReview: ownSubmissions.filter(
        (s) => s.status === IPPAStatus.PENDING_HR_REVIEW,
      ).length,
      completed: ownSubmissions.filter((s) => s.status === IPPAStatus.COMPLETED)
        .length,
      submitted: ownSubmissions.filter(
        (s) => s.status === IPPAStatus.PENDING_SUPERVISOR_REVIEW,
      ).length,
      draft: ownSubmissions.filter((s) => s.status === IPPAStatus.DRAFT).length,
    };

    // For to-review, we need to show ALL reportees, even those who haven't submitted
    const reporteeIds = userRole.reportees.map((reportee) => reportee.id); // Keep as ObjectIds for database query
    // Get all existing submissions from reportees (any status)
    const allReporteeSubmissions = await this.ippaSubmissionModel
      .find({
        employeeId: { $in: reporteeIds },
        // Add financial year filter if specified
        ...(filters?.financialYear
          ? { financialYear: filters.financialYear }
          : {}),
      })
      .populate('currentReviewerId', 'fullName email department')
      .populate({
        path: 'employeeId',
        select: 'fullName email department position grade employeeId',
        populate: {
          path: 'lineManager',
          select: 'fullName email designation'
        }
      })
      .lean()
      .exec();

    // Calculate status information based on own submissions + submitted reportees
    const submittedReportees = allReporteeSubmissions.filter(
      (sub) => sub.status === IPPAStatus.PENDING_SUPERVISOR_REVIEW,
    );

    statusInfo['totalSubmissions'] =
      // ownSubmissions.length + submittedReportees.length;
      ownSubmissions.length;
    statusInfo['toReview'] = reporteeIds.length;
    // Add special counts
    statusInfo['all'] = allSubmissions.length;

    // Build sort criteria
    let sortCriteria: any = {};
    const sortBy = filters?.sortBy || 'financialYear';
    const sortOrder = filters?.sortOrder === 'asc' ? 1 : -1;

    switch (sortBy) {
      case 'financialYear':
        sortCriteria.financialYear = sortOrder;
        sortCriteria.createdAt = -1; // Secondary sort
        break;
      case 'submittedAt':
        sortCriteria.submittedAt = sortOrder;
        break;
      case 'lastModifiedAt':
        sortCriteria.lastModifiedAt = sortOrder;
        break;
      case 'performanceScore':
        // This will be handled after data retrieval since it's a calculated field
        sortCriteria.financialYear = -1;
        break;
      default:
        sortCriteria.financialYear = -1;
        sortCriteria.createdAt = -1;
    }

    // For to-review requests, sort by submission date (oldest first)
    if (isToReviewRequest && !filters?.sortBy) {
      sortCriteria = { submittedAt: 1 };
    }
    let submissions;
    let totalCount = 0;

    // Get total count for pagination
    // totalCount is already set in the isToReviewRequest logic above
    if (!isToReviewRequest) {
      totalCount = await this.ippaSubmissionModel.countDocuments(queryFilter);
    }

    if (isToReviewRequest) {
      // Find reportees who have NOT submitted (no submission record at all)
      const submittedEmployeeIds = new Set(
        allReporteeSubmissions.map((sub) =>
          sub.employeeId._id
            ? sub.employeeId._id.toString()
            : sub.employeeId.toString(),
        ),
      );
      // console.log('Submitted Employee IDs:', Array.from(submittedEmployeeIds));

      const reporteesWithoutSubmissions = userRole.reportees.filter(
        (reportee) => {
          const reporteeIdStr = reportee.id.toString();
          const hasSubmitted = submittedEmployeeIds.has(reporteeIdStr); // Now this works
          return !hasSubmitted;
        },
      );

      // console.log(
      //   'Reportees WITHOUT submissions:',
      //   reporteesWithoutSubmissions.map((r) => ({
      //     id: r.id,
      //     name: r.fullName,
      //   })),
      // );

      // Create placeholder submissions for missing reportees only
      const placeholderSubmissions = reporteesWithoutSubmissions.map(
        (reportee) => ({
          _id: `placeholder-${reportee.id}`,
          ippaId: `Pending`,
          employeeId: reportee.id,
          employeeName: reportee.fullName,
          submissionDepartment: reportee.department,
          submissionDepartmentCode: reportee.departmentCode,
          employeePosition: reportee.position,
          employeeGrade: reportee.employeeGrade,
          status: 'NOT_SUBMITTED',
          isDraft: false,
          isPlaceholder: true,
          submittedAt: null,
          lastModifiedAt: null,
          financialYear: filters?.financialYear || 'FY2025',
          reviewPeriod: 'YEAR-END REVIEW',
          objectives: [],
          developmentPlans: [],
          reviews: [],
          approvalWorkflow: [],
          attachmentsCount: 0,
          totalObjectives: 0,
          totalDevelopmentPlans: 0,
          performanceScore: 0,
          performanceRating: 'Not Submitted',
          supervisorOverallScore: null,
          employeeOverallScore: null,
        }),
      );

      // console.log(
      //   'Created placeholder submissions:',
      //   placeholderSubmissions.length,
      // );

      // Combine real submissions with placeholders
      const allSubmissionsWithPlaceholders = [
        ...allReporteeSubmissions,
        ...placeholderSubmissions,
      ];

      // console.log(
      //   'Total submissions (real + placeholders):',
      //   allSubmissionsWithPlaceholders.length,
      // );

      // Apply sorting
      allSubmissionsWithPlaceholders.sort((a, b) => {
        // Put NOT_SUBMITTED items at the end
        if (a.status === 'NOT_SUBMITTED' && b.status !== 'NOT_SUBMITTED')
          return 1;
        if (a.status !== 'NOT_SUBMITTED' && b.status === 'NOT_SUBMITTED')
          return -1;

        // Sort by submission date for real submissions
        if (a.submittedAt && b.submittedAt) {
          return (
            new Date(a.submittedAt).getTime() -
            new Date(b.submittedAt).getTime()
          );
        }

        // Sort by name for placeholders or when dates are missing
        return (a.employeeName || '').localeCompare(b.employeeName || '');
      });

      // Apply pagination to the combined list
      const skip = (page - 1) * limit;
      submissions = allSubmissionsWithPlaceholders.slice(skip, skip + limit);

      // Update total count to include placeholders
      totalCount = allSubmissionsWithPlaceholders.length;
    } else {
      // Regular pagination for other statuses
      totalCount = await this.ippaSubmissionModel.countDocuments(queryFilter);
      const skip = (page - 1) * limit;

      submissions = await this.ippaSubmissionModel
        .find(queryFilter)
        .populate('currentReviewerId', 'fullName email department')
        .populate('employeeId', 'fullName email department position grade')
        .populate({
          path: 'employeeId',
          populate: {
            path: 'lineManager',
            select: 'fullName email designation'
          }
        })
        .sort(sortCriteria)
        .skip(skip)
        .limit(limit)
        .lean()
        .exec();
    }

    // Get user information for unread message counts
    const user = await this.userModel
      .findOne({ email: employeeEmail.toLowerCase().trim() })
      .select('_id')
      .lean();

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const userObjectId = new Types.ObjectId(user._id);
    const userIdString = user._id.toString();

    // Get submission IDs for unread message count calculation
    const submissionIds = submissions
      .filter(sub => !sub.isPlaceholder) // Only real submissions, not placeholders
      .map(sub => sub._id);

    // Calculate unread message counts for each submission
    const unreadCountPromises = submissionIds.map(async (submissionId) => {
      const submissionIdString = submissionId.toString();

      // Count unread messages for this submission
      // Use both string form and ObjectId form in the $nin array to cover all bases
      const count = await this.messageModel // Assuming you have a message model for PPA
        .countDocuments({
          ippaSubmissionId: submissionIdString, // Adjust field name based on your schema
          readBy: { $nin: [userObjectId, userIdString] }, // Try both formats
          senderId: { $ne: userIdString }, // Exclude messages sent by the user
        })
        .exec();

      return { submissionId: submissionIdString, count };
    });

    // Wait for all count promises to resolve
    const unreadCountResults = await Promise.all(unreadCountPromises);

    // Convert to a lookup object for easy access
    const unreadCountMap = {};
    unreadCountResults.forEach((result) => {
      unreadCountMap[result.submissionId] = result.count;
    });


    // Format submissions for frontend
    let formattedSubmissions: SubmissionSummary[] = submissions.map(
      (submission): SubmissionSummary => {
        // Handle placeholder submissions for non-submitted reportees
        if (submission.isPlaceholder) {
          const reporteeInfo = userRole.reportees.find(
            (r) => r.id === submission.employeeId,
          );

          return {
            ippaId: submission.ippaId,
            submissionId: submission._id.toString(),
            employee: employee, // Use logged-in employee for type consistency
            employeeName: submission.employeeName,
            employeeId: submission.employeeId.toString(),
            department: submission.submissionDepartment,
            departmentCode: submission.submissionDepartmentCode,
            position: submission.employeePosition
              ? submission.employeePosition
              : 'N/A',
            grade: submission.employeeGrade ? submission.employeeGrade : 'N/A',
            status: IPPAStatus.NOT_SUBMITTED,
            statusLabel: 'Not Submitted',
            statusColor: '#dc3545', // Red color for not submitted
            performanceScore: 0,
            performanceRating: 'Not Submitted',
            totalObjectives: 0,
            totalDevelopmentPlans: 0,
            attachmentsCount: 0,
            submittedAt: null,
            lastModifiedAt: null,
            financialYear: submission.financialYear,
            reviewPeriod: submission.reviewPeriod,
            currentReviewer: null,
            approvalWorkflow: [],
            isDraft: false,
            daysSinceAction: 0,
            isOverdue: true, // Mark as overdue since not submitted
            canEdit: false,
            nextAction: 'Awaiting Submission',
            objectives: [],
            developmentPlans: [],
            reviews: [],
            workflowDetails: [],
            allAttachments: [],
            summaryStats: {
              totalWeightage: 0,
              completedObjectives: 0,
              pendingObjectives: 0,
              developmentPlansWithFeedback: 0,
              totalAttachments: 0,
              reviewsCompleted: 0,
              workflowStepsCompleted: 0,
              workflowStepsPending: 0,
            },
            isOwnSubmission: false,
            isForApproval: false,
            isPlaceholder: true,
            reporteeInfo: reporteeInfo,
            unreadMessageCount: 0, // Placeholders have no messages
          } as SubmissionSummary;
        }

        // Regular submission processing for actual submissions
        const isOwnSubmission =
          submission.employeeId.toString() === employee._id.toString();
        const isForApproval = !isOwnSubmission;

        // Calculate performance score (use supervisor score if available, otherwise employee score)
        const performanceScore =
          submission.supervisorOverallScore ||
          submission.employeeOverallScore ||
          0;
        const performanceRating =
          this.calculatePerformanceRating(performanceScore);

        // Get status display information
        const statusDisplay = this.getStatusDisplay(submission.status);

        // Count attachments from reviews
        const attachmentsCount =
          submission.reviews?.reduce((count, review) => {
            return count + (review.attachments?.length || 0);
          }, 0) || 0;

        // Get current reviewer information
        let currentReviewer;
        if (
          submission.currentReviewerId &&
          (submission as any).currentReviewerId?.fullName
        ) {
          currentReviewer = {
            name: (submission as any).currentReviewerId.fullName,
            email: (submission as any).currentReviewerId.email,
            department:
              (submission as any).currentReviewerId.department || 'N/A',
          };
        }

        // Calculate days since submission/last action
        const daysSinceAction = this.calculateDaysSinceAction(submission);

        // Check if submission is overdue
        const isOverdue = this.checkIfOverdue(submission);

        // Process objectives with detailed information
        const detailedObjectives = (submission.objectives || []).map(
          (obj, index) => ({
            index: index + 1,
            keyResultsArea: obj.keyResultsArea,
            workObjective: obj.workObjective,
            targetBase: obj.targetBase,
            targetExceed: obj.targetExceed,
            targetStretch: obj.targetStretch,
            weightage: obj.weightage,
            yearEndResults: obj.yearEndResults,
            performanceAssessment: obj.performanceAssessment,
            employeeRating: obj.employeeRating,
            employeeWeightedPoints: obj.employeeWeightedPoints || 0,
            supervisorRating: obj.supervisorRating || null,
            supervisorWeightedPoints: obj.supervisorWeightedPoints || null,
            supervisorComments: obj.supervisorComments || null,
            hasEmployeeRating: !!obj.employeeRating,
            hasSupervisorRating: !!obj.supervisorRating,
          }),
        );

        // Process development plans with detailed information
        const detailedDevelopmentPlans = (
          submission.developmentPlans || []
        ).map((plan, index) => ({
          index: index + 1,
          competencyType: plan.competencyType,
          areaForImprovement: plan.areaForImprovement,
          activity: plan.activity,
          timeline: plan.timeline,
          yearEndComments: plan.yearEndComments || '',
          supervisorFeedback: plan.supervisorFeedback || null,
          hodRemarks: plan.hodRemarks || null,
          hasYearEndComments: !!plan.yearEndComments,
          hasSupervisorFeedback: !!plan.supervisorFeedback,
          hasHodRemarks: !!plan.hodRemarks,
        }));

        // Process reviews with detailed attachments
        const detailedReviews = (submission.reviews || []).map((review) => ({
          reviewerType: review.reviewerType,
          reviewerId: review.reviewerId,
          reviewerName: review.reviewerName,
          remarks: review.remarks || '',
          attachments: (review.attachments || []).map((attachment) => ({
            fileName: attachment.fileName,
            filePath: attachment.filePath,
            fileSize: attachment.fileSize,
            mimeType: attachment.mimeType,
            uploadedAt: attachment.uploadedAt,
            description: attachment.description || '',
            fileSizeFormatted: this.formatFileSize(attachment.fileSize),
          })),
          overallScore: review.overallScore || null,
          reviewedAt: review.reviewedAt,
          department: review.department || '',
          attachmentCount: review.attachments?.length || 0,
          hasRemarks: !!review.remarks,
          hasAttachments: (review.attachments?.length || 0) > 0,
        }));

        // Process approval workflow with enhanced details
        const detailedApprovalWorkflow = (
          submission.approvalWorkflow || []
        ).map((step) => ({
          stepId: step.stepId,
          title: step.title,
          personId: step.personId,
          personName: step.personName,
          department: step.department || '',
          date: step.date,
          status: step.status,
          isCompleted: step.status === 'completed',
          isPending: step.status === 'pending',
          isUpcoming: step.status === 'upcoming',
          statusColor: this.getWorkflowStepColor(step.status),
          formattedDate: step.date !== '-' ? step.date : 'Pending',
        }));

        // Aggregate all attachments across reviews
        const allAttachmentsFlat = detailedReviews.reduce((acc, review) => {
          const reviewAttachments = review.attachments.map((att) => ({
            ...att,
            reviewerType: review.reviewerType,
            reviewerName: review.reviewerName,
            reviewedAt: review.reviewedAt,
          }));
          return [...acc, ...reviewAttachments];
        }, []);

        const submissionSummary: SubmissionSummary = {
          ippaId: submission.ippaId,
          submissionId: submission._id.toString(),
          employee: isOwnSubmission ? employee : (submission as any).employeeId, // Use actual submission employee data
          employeeName: submission.employeeName,
          employeeId: submission.employeeId.toString(),
          department: submission.submissionDepartment,
          departmentCode: submission.submissionDepartmentCode,
          position: submission.employeePosition,
          grade: submission.employeeGrade,
          status: submission.status,
          statusLabel: statusDisplay.label,
          statusColor: statusDisplay.color,
          performanceScore: parseFloat(performanceScore.toFixed(1)),
          performanceRating: performanceRating.rating,
          totalObjectives: submission.objectives?.length || 0,
          totalDevelopmentPlans: submission.developmentPlans?.length || 0,
          attachmentsCount: attachmentsCount,
          submittedAt: submission.submittedAt || null,
          lastModifiedAt: submission.lastModifiedAt || null,
          financialYear: submission.financialYear,
          reviewPeriod: submission.reviewPeriod,
          currentReviewer: currentReviewer,
          approvalWorkflow: submission.approvalWorkflow || [],
          isDraft: submission.isDraft || false,
          daysSinceAction: daysSinceAction,
          isOverdue: isOverdue,
          canEdit: isOwnSubmission ? this.canEditSubmission(submission) : false,
          nextAction: isOwnSubmission
            ? this.getNextAction(submission)
            : 'Review Required',

          // Detailed information
          objectives: detailedObjectives,
          developmentPlans: detailedDevelopmentPlans,
          reviews: detailedReviews,
          workflowDetails: detailedApprovalWorkflow,
          allAttachments: allAttachmentsFlat,

          // Summary statistics
          summaryStats: {
            totalWeightage: detailedObjectives.reduce(
              (sum, obj) => sum + obj.weightage,
              0,
            ),
            completedObjectives: detailedObjectives.filter(
              (obj) => obj.hasSupervisorRating,
            ).length,
            pendingObjectives: detailedObjectives.filter(
              (obj) => !obj.hasSupervisorRating,
            ).length,
            developmentPlansWithFeedback: detailedDevelopmentPlans.filter(
              (plan) => plan.hasSupervisorFeedback,
            ).length,
            totalAttachments: allAttachmentsFlat.length,
            reviewsCompleted: detailedReviews.length,
            workflowStepsCompleted: detailedApprovalWorkflow.filter(
              (step) => step.isCompleted,
            ).length,
            workflowStepsPending: detailedApprovalWorkflow.filter(
              (step) => step.isPending,
            ).length,
          },

          // Add flags to identify submission type
          isOwnSubmission: isOwnSubmission,
          isForApproval: isForApproval,
          isPlaceholder: false,
          unreadMessageCount: unreadCountMap[submission._id.toString()] || 0,
        };

        // Add approval-specific information for submissions requiring approval
        if (isForApproval && userRole) {
          const approverRole = this.getApproverRole(
            submission,
            userRole.user.id,
          );
          const canApprove = this.canApprove(submission, userRole.user.id);
          const daysPending = this.calculateDaysPending(submission);
          const nextActionForApprover = this.getNextActionForApprover(
            submission,
            approverRole,
          );

          submissionSummary['approverRole'] = approverRole;
          submissionSummary['canApprove'] = canApprove;
          submissionSummary['daysPending'] = daysPending;
          submissionSummary['nextActionForApprover'] = nextActionForApprover;
        }

        return submissionSummary;
      },
    );

    // Handle performance score sorting if requested
    if (filters?.sortBy === 'performanceScore') {
      formattedSubmissions.sort((a, b) => {
        const comparison = a.performanceScore - b.performanceScore;
        return filters.sortOrder === 'asc' ? comparison : -comparison;
      });
    }

    const response = {
      statusInfo: statusInfo,
      submissions: formattedSubmissions,
      totalCount: totalCount,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalCount / limit),
        pageSize: limit,
        hasNextPage: page < Math.ceil(totalCount / limit),
        hasPrevPage: page > 1,
      },
      filters: filters || {},
    } as any;

    // Add approver information for to-review and all requests
    if ((isToReviewRequest || isAllRequest) && userRole) {
      response['approverInfo'] = {
        name: userRole.user.fullName,
        email: userRole.user.email,
        department: userRole.user.department,
        roles: userRole.roles,
      };
    }

    return response;
  }

  async ownSubmission(
    employeeEmail: string,
    page: number = 1,
    limit: number = 10,
    status: IPPAStatus,
    filters?: {
      status?: IPPAStatus;
      financialYear?: string;
      sortBy?:
      | 'financialYear'
      | 'submittedAt'
      | 'lastModifiedAt'
      | 'performanceScore';
      sortOrder?: 'asc' | 'desc';
      search?: string;
    },
  ): Promise<
    MySubmissionsResponse & {
      filters: {
        status?: IPPAStatus | 'to-review' | 'all';
        financialYear?: string;
        sortBy?:
        | 'financialYear'
        | 'submittedAt'
        | 'lastModifiedAt'
        | 'performanceScore';
        sortOrder?: 'asc' | 'desc';
        search?: string;
      };
      approverInfo?: {
        name: string;
        email: string;
        department: string;
        roles: string[];
      };
    }
  > {
    const employee = await this.ippaUserModel
      .findOne({ email: employeeEmail.toLowerCase().trim() })
      .populate('lineManager', 'fullName email designation')
      .lean();

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    // Get user role information
    const userRole = await this.getUserIPPARole(employeeEmail);

    let queryFilter: any = {};
    let isToReviewRequest = false;
    let isAllRequest = false;

    // Specific status - only employee's own submissions
    queryFilter = { employeeId: employee._id };

    // if (status && status === IPPAStatus.SUBMITTED) {
    //   queryFilter.status = IPPAStatus.PENDING_SUPERVISOR_REVIEW;
    // } else if (status) {
    //   queryFilter.status = status;
    // }

    // Add common filters
    if (filters?.financialYear) {
      queryFilter.financialYear = filters.financialYear;
    }

    // Add search functionality
    if (filters?.search) {
      const searchFields: any[] = [
        { ippaId: { $regex: filters.search, $options: 'i' } },
        { reviewPeriod: { $regex: filters.search, $options: 'i' } },
        { financialYear: { $regex: filters.search, $options: 'i' } },
      ];

      // For to-review and all requests, also search by employee name and department
      if (isToReviewRequest || isAllRequest) {
        searchFields.push(
          { employeeName: { $regex: filters.search, $options: 'i' } },
          { submissionDepartment: { $regex: filters.search, $options: 'i' } },
        );
      }

      const searchQuery = { $or: searchFields };

      if (queryFilter.$and) {
        queryFilter.$and.push(searchQuery);
      } else {
        queryFilter.$and = [searchQuery];
      }
    }

    // Get all submissions for status counting - always get comprehensive counts
    const ownSubmissionsFilter = { employeeId: employee._id };
    const toReviewFilter = {
      $or: [
        {
          currentReviewerId: userRole.user.id,
          'approvalWorkflow.personId': userRole.user.id,
          'approvalWorkflow.stepType': 'supervisor',
          employeeId: { $ne: employee._id }, // Exclude own submissions from to-review count
        },
        {
          'approvalWorkflow.stepType': 'hod',
          'approvalWorkflow.personId': userRole.user.id,
          employeeId: { $ne: employee._id }, // Exclude own submissions from to-review count
        },
        {
          'approvalWorkflow.stepType': 'hr',
          'approvalWorkflow.personId': userRole.user.id,
          employeeId: { $ne: employee._id }, // Exclude own submissions from to-review count
        },
      ],
    };

    // Get own submissions for status counting
    const ownSubmissions = await this.ippaSubmissionModel
      .find(ownSubmissionsFilter)
      .lean()
      .exec();

    // Get submissions to review for status counting
    const toReviewSubmissions = await this.ippaSubmissionModel
      .find(toReviewFilter)
      .lean()
      .exec();

    // Filter to-review submissions to only active pending ones
    const activeToReviewSubmissions = toReviewSubmissions.filter(
      (submission) => {
        return (
          (submission.status === IPPAStatus.PENDING_SUPERVISOR_REVIEW &&
            submission.currentReviewerId?.toString() === userRole.user.id) ||
          (submission.status === IPPAStatus.PENDING_HOD_REVIEW &&
            submission.approvalWorkflow?.some(
              (step) =>
                step.stepType === 'hod' &&
                step.personId === userRole.user.id &&
                step.status === 'pending',
            )) ||
          (submission.status === IPPAStatus.PENDING_HR_REVIEW &&
            submission.approvalWorkflow?.some(
              (step) =>
                step.stepType === 'hr' &&
                step.personId === userRole.user.id &&
                step.status === 'pending',
            ))
        );
      },
    );

    // Combine all submissions for comprehensive status counting (avoiding duplicates)
    const allSubmissions = [
      ...ownSubmissions,
    ];
    // Calculate status information based on own submissions
    const statusInfo: SubmissionStatusInfo = {
      // totalSubmissions: allSubmissions.length,
      totalSubmissions: ownSubmissions.length,
      approved: ownSubmissions.filter(
        (s) => s.status === IPPAStatus.SUPERVISOR_REVIEWED,
      ).length,
      pendingReview: ownSubmissions.filter(
        (s) => s.status === IPPAStatus.PENDING_HR_REVIEW,
      ).length,
      completed: ownSubmissions.filter((s) => s.status === IPPAStatus.COMPLETED)
        .length,
      submitted: ownSubmissions.filter(
        (s) => s.status === IPPAStatus.PENDING_SUPERVISOR_REVIEW,
      ).length,
      draft: ownSubmissions.filter((s) => s.status === IPPAStatus.DRAFT).length,
    };

    // For to-review, we need to show ALL reportees, even those who haven't submitted
    const reporteeIds = userRole.reportees.map((reportee) => reportee.id); // Keep as ObjectIds for database query
    // Get all existing submissions from reportees (any status)
    const allReporteeSubmissions = await this.ippaSubmissionModel
      .find({
        employeeId: { $in: reporteeIds },
        // Add financial year filter if specified
        ...(filters?.financialYear
          ? { financialYear: filters.financialYear }
          : {}),
      })
      .populate('currentReviewerId', 'fullName email department')
      .populate({
        path: 'employeeId',
        select: 'fullName email department position grade employeeId',
        populate: {
          path: 'lineManager',
          select: 'fullName email designation'
        }
      })
      .lean()
      .exec();

    // Calculate status information based on own submissions + submitted reportees
    const submittedReportees = allReporteeSubmissions.filter(
      (sub) => sub.status === IPPAStatus.PENDING_SUPERVISOR_REVIEW,
    );

    statusInfo['totalSubmissions'] =
      // ownSubmissions.length + submittedReportees.length;
      ownSubmissions.length;
    statusInfo['toReview'] = reporteeIds.length;
    // Add special counts
    statusInfo['all'] = allSubmissions.length;

    // Build sort criteria
    let sortCriteria: any = {};
    const sortBy = filters?.sortBy || 'financialYear';
    const sortOrder = filters?.sortOrder === 'asc' ? 1 : -1;

    switch (sortBy) {
      case 'financialYear':
        sortCriteria.financialYear = sortOrder;
        sortCriteria.createdAt = -1; // Secondary sort
        break;
      case 'submittedAt':
        sortCriteria.submittedAt = sortOrder;
        break;
      case 'lastModifiedAt':
        sortCriteria.lastModifiedAt = sortOrder;
        break;
      case 'performanceScore':
        // This will be handled after data retrieval since it's a calculated field
        sortCriteria.financialYear = -1;
        break;
      default:
        sortCriteria.financialYear = -1;
        sortCriteria.createdAt = -1;
    }

    // For to-review requests, sort by submission date (oldest first)
    if (isToReviewRequest && !filters?.sortBy) {
      sortCriteria = { submittedAt: 1 };
    }
    let submissions;
    let totalCount = 0;

    // Get total count for pagination
    // totalCount is already set in the isToReviewRequest logic above
    if (!isToReviewRequest) {
      totalCount = await this.ippaSubmissionModel.countDocuments(queryFilter);
    }

    if (isToReviewRequest) {
      // Find reportees who have NOT submitted (no submission record at all)
      const submittedEmployeeIds = new Set(
        allReporteeSubmissions.map((sub) =>
          sub.employeeId._id
            ? sub.employeeId._id.toString()
            : sub.employeeId.toString(),
        ),
      );
      // console.log('Submitted Employee IDs:', Array.from(submittedEmployeeIds));

      const reporteesWithoutSubmissions = userRole.reportees.filter(
        (reportee) => {
          const reporteeIdStr = reportee.id.toString();
          const hasSubmitted = submittedEmployeeIds.has(reporteeIdStr); // Now this works
          return !hasSubmitted;
        },
      );

      // console.log(
      //   'Reportees WITHOUT submissions:',
      //   reporteesWithoutSubmissions.map((r) => ({
      //     id: r.id,
      //     name: r.fullName,
      //   })),
      // );

      // Create placeholder submissions for missing reportees only
      const placeholderSubmissions = reporteesWithoutSubmissions.map(
        (reportee) => ({
          _id: `placeholder-${reportee.id}`,
          ippaId: `Pending`,
          employeeId: reportee.id,
          employeeName: reportee.fullName,
          submissionDepartment: reportee.department,
          submissionDepartmentCode: reportee.departmentCode,
          employeePosition: reportee.position,
          employeeGrade: reportee.employeeGrade,
          status: 'NOT_SUBMITTED',
          isDraft: false,
          isPlaceholder: true,
          submittedAt: null,
          lastModifiedAt: null,
          financialYear: filters?.financialYear || 'FY2025',
          reviewPeriod: 'YEAR-END REVIEW',
          objectives: [],
          developmentPlans: [],
          reviews: [],
          approvalWorkflow: [],
          attachmentsCount: 0,
          totalObjectives: 0,
          totalDevelopmentPlans: 0,
          performanceScore: 0,
          performanceRating: 'Not Submitted',
          supervisorOverallScore: null,
          employeeOverallScore: null,
        }),
      );

      // console.log(
      //   'Created placeholder submissions:',
      //   placeholderSubmissions.length,
      // );

      // Combine real submissions with placeholders
      const allSubmissionsWithPlaceholders = [
        ...allReporteeSubmissions,
        ...placeholderSubmissions,
      ];

      // console.log(
      //   'Total submissions (real + placeholders):',
      //   allSubmissionsWithPlaceholders.length,
      // );

      // Apply sorting
      allSubmissionsWithPlaceholders.sort((a, b) => {
        // Put NOT_SUBMITTED items at the end
        if (a.status === 'NOT_SUBMITTED' && b.status !== 'NOT_SUBMITTED')
          return 1;
        if (a.status !== 'NOT_SUBMITTED' && b.status === 'NOT_SUBMITTED')
          return -1;

        // Sort by submission date for real submissions
        if (a.submittedAt && b.submittedAt) {
          return (
            new Date(a.submittedAt).getTime() -
            new Date(b.submittedAt).getTime()
          );
        }

        // Sort by name for placeholders or when dates are missing
        return (a.employeeName || '').localeCompare(b.employeeName || '');
      });

      // Apply pagination to the combined list
      const skip = (page - 1) * limit;
      submissions = allSubmissionsWithPlaceholders.slice(skip, skip + limit);

      // Update total count to include placeholders
      totalCount = allSubmissionsWithPlaceholders.length;
    } else {
      // Regular pagination for other statuses
      totalCount = await this.ippaSubmissionModel.countDocuments(queryFilter);
      const skip = (page - 1) * limit;

      submissions = await this.ippaSubmissionModel
        .find(queryFilter)
        .populate('currentReviewerId', 'fullName email department')
        .populate('employeeId', 'fullName email department position grade')
        .populate({
          path: 'employeeId',
          populate: {
            path: 'lineManager',
            select: 'fullName email designation'
          }
        })
        .sort(sortCriteria)
        .skip(skip)
        .limit(limit)
        .lean()
        .exec();
    }

    // Get user information for unread message counts
    const user = await this.userModel
      .findOne({ email: employeeEmail.toLowerCase().trim() })
      .select('_id')
      .lean();

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const userObjectId = new Types.ObjectId(user._id);
    const userIdString = user._id.toString();

    // Get submission IDs for unread message count calculation
    const submissionIds = submissions
      .filter(sub => !sub.isPlaceholder) // Only real submissions, not placeholders
      .map(sub => sub._id);

    // Calculate unread message counts for each submission
    const unreadCountPromises = submissionIds.map(async (submissionId) => {
      const submissionIdString = submissionId.toString();

      // Count unread messages for this submission
      // Use both string form and ObjectId form in the $nin array to cover all bases
      const count = await this.messageModel // Assuming you have a message model for PPA
        .countDocuments({
          ippaSubmissionId: submissionIdString, // Adjust field name based on your schema
          readBy: { $nin: [userObjectId, userIdString] }, // Try both formats
          senderId: { $ne: userIdString }, // Exclude messages sent by the user
        })
        .exec();

      return { submissionId: submissionIdString, count };
    });

    // Wait for all count promises to resolve
    const unreadCountResults = await Promise.all(unreadCountPromises);

    // Convert to a lookup object for easy access
    const unreadCountMap = {};
    unreadCountResults.forEach((result) => {
      unreadCountMap[result.submissionId] = result.count;
    });


    // Format submissions for frontend
    let formattedSubmissions: SubmissionSummary[] = submissions.map(
      (submission): SubmissionSummary => {
        // Handle placeholder submissions for non-submitted reportees
        if (submission.isPlaceholder) {
          const reporteeInfo = userRole.reportees.find(
            (r) => r.id === submission.employeeId,
          );

          return {
            ippaId: submission.ippaId,
            submissionId: submission._id.toString(),
            employee: employee, // Use logged-in employee for type consistency
            employeeName: submission.employeeName,
            employeeId: submission.employeeId.toString(),
            department: submission.submissionDepartment,
            departmentCode: submission.submissionDepartmentCode,
            position: submission.employeePosition
              ? submission.employeePosition
              : 'N/A',
            grade: submission.employeeGrade ? submission.employeeGrade : 'N/A',
            status: IPPAStatus.NOT_SUBMITTED,
            statusLabel: 'Not Submitted',
            statusColor: '#dc3545', // Red color for not submitted
            performanceScore: 0,
            performanceRating: 'Not Submitted',
            totalObjectives: 0,
            totalDevelopmentPlans: 0,
            attachmentsCount: 0,
            submittedAt: null,
            lastModifiedAt: null,
            financialYear: submission.financialYear,
            reviewPeriod: submission.reviewPeriod,
            currentReviewer: null,
            approvalWorkflow: [],
            isDraft: false,
            daysSinceAction: 0,
            isOverdue: true, // Mark as overdue since not submitted
            canEdit: false,
            nextAction: 'Awaiting Submission',
            objectives: [],
            developmentPlans: [],
            reviews: [],
            workflowDetails: [],
            allAttachments: [],
            summaryStats: {
              totalWeightage: 0,
              completedObjectives: 0,
              pendingObjectives: 0,
              developmentPlansWithFeedback: 0,
              totalAttachments: 0,
              reviewsCompleted: 0,
              workflowStepsCompleted: 0,
              workflowStepsPending: 0,
            },
            isOwnSubmission: false,
            isForApproval: false,
            isPlaceholder: true,
            reporteeInfo: reporteeInfo,
            unreadMessageCount: 0, // Placeholders have no messages
          } as SubmissionSummary;
        }

        // Regular submission processing for actual submissions
        const isOwnSubmission =
          submission.employeeId.toString() === employee._id.toString();
        const isForApproval = !isOwnSubmission;

        // Calculate performance score (use supervisor score if available, otherwise employee score)
        const performanceScore =
          submission.supervisorOverallScore ||
          submission.employeeOverallScore ||
          0;
        const performanceRating =
          this.calculatePerformanceRating(performanceScore);

        // Get status display information
        const statusDisplay = this.getStatusDisplay(submission.status);

        // Count attachments from reviews
        const attachmentsCount =
          submission.reviews?.reduce((count, review) => {
            return count + (review.attachments?.length || 0);
          }, 0) || 0;

        // Get current reviewer information
        let currentReviewer;
        if (
          submission.currentReviewerId &&
          (submission as any).currentReviewerId?.fullName
        ) {
          currentReviewer = {
            name: (submission as any).currentReviewerId.fullName,
            email: (submission as any).currentReviewerId.email,
            department:
              (submission as any).currentReviewerId.department || 'N/A',
          };
        }

        // Calculate days since submission/last action
        const daysSinceAction = this.calculateDaysSinceAction(submission);

        // Check if submission is overdue
        const isOverdue = this.checkIfOverdue(submission);

        // Process objectives with detailed information
        const detailedObjectives = (submission.objectives || []).map(
          (obj, index) => ({
            index: index + 1,
            keyResultsArea: obj.keyResultsArea,
            workObjective: obj.workObjective,
            targetBase: obj.targetBase,
            targetExceed: obj.targetExceed,
            targetStretch: obj.targetStretch,
            weightage: obj.weightage,
            yearEndResults: obj.yearEndResults,
            performanceAssessment: obj.performanceAssessment,
            employeeRating: obj.employeeRating,
            employeeWeightedPoints: obj.employeeWeightedPoints || 0,
            supervisorRating: obj.supervisorRating || null,
            supervisorWeightedPoints: obj.supervisorWeightedPoints || null,
            supervisorComments: obj.supervisorComments || null,
            hasEmployeeRating: !!obj.employeeRating,
            hasSupervisorRating: !!obj.supervisorRating,
          }),
        );

        // Process development plans with detailed information
        const detailedDevelopmentPlans = (
          submission.developmentPlans || []
        ).map((plan, index) => ({
          index: index + 1,
          competencyType: plan.competencyType,
          areaForImprovement: plan.areaForImprovement,
          activity: plan.activity,
          timeline: plan.timeline,
          yearEndComments: plan.yearEndComments || '',
          supervisorFeedback: plan.supervisorFeedback || null,
          hodRemarks: plan.hodRemarks || null,
          hasYearEndComments: !!plan.yearEndComments,
          hasSupervisorFeedback: !!plan.supervisorFeedback,
          hasHodRemarks: !!plan.hodRemarks,
        }));

        // Process reviews with detailed attachments
        const detailedReviews = (submission.reviews || []).map((review) => ({
          reviewerType: review.reviewerType,
          reviewerId: review.reviewerId,
          reviewerName: review.reviewerName,
          remarks: review.remarks || '',
          attachments: (review.attachments || []).map((attachment) => ({
            fileName: attachment.fileName,
            filePath: attachment.filePath,
            fileSize: attachment.fileSize,
            mimeType: attachment.mimeType,
            uploadedAt: attachment.uploadedAt,
            description: attachment.description || '',
            fileSizeFormatted: this.formatFileSize(attachment.fileSize),
          })),
          overallScore: review.overallScore || null,
          reviewedAt: review.reviewedAt,
          department: review.department || '',
          attachmentCount: review.attachments?.length || 0,
          hasRemarks: !!review.remarks,
          hasAttachments: (review.attachments?.length || 0) > 0,
        }));

        // Process approval workflow with enhanced details
        const detailedApprovalWorkflow = (
          submission.approvalWorkflow || []
        ).map((step) => ({
          stepId: step.stepId,
          title: step.title,
          personId: step.personId,
          personName: step.personName,
          department: step.department || '',
          date: step.date,
          status: step.status,
          isCompleted: step.status === 'completed',
          isPending: step.status === 'pending',
          isUpcoming: step.status === 'upcoming',
          statusColor: this.getWorkflowStepColor(step.status),
          formattedDate: step.date !== '-' ? step.date : 'Pending',
        }));

        // Aggregate all attachments across reviews
        const allAttachmentsFlat = detailedReviews.reduce((acc, review) => {
          const reviewAttachments = review.attachments.map((att) => ({
            ...att,
            reviewerType: review.reviewerType,
            reviewerName: review.reviewerName,
            reviewedAt: review.reviewedAt,
          }));
          return [...acc, ...reviewAttachments];
        }, []);

        const submissionSummary: SubmissionSummary = {
          ippaId: submission.ippaId,
          submissionId: submission._id.toString(),
          employee: isOwnSubmission ? employee : (submission as any).employeeId, // Use actual submission employee data
          employeeName: submission.employeeName,
          employeeId: submission.employeeId.toString(),
          department: submission.submissionDepartment,
          departmentCode: submission.submissionDepartmentCode,
          position: submission.employeePosition,
          grade: submission.employeeGrade,
          status: submission.status,
          statusLabel: statusDisplay.label,
          statusColor: statusDisplay.color,
          performanceScore: parseFloat(performanceScore.toFixed(1)),
          performanceRating: performanceRating.rating,
          totalObjectives: submission.objectives?.length || 0,
          totalDevelopmentPlans: submission.developmentPlans?.length || 0,
          attachmentsCount: attachmentsCount,
          submittedAt: submission.submittedAt || null,
          lastModifiedAt: submission.lastModifiedAt || null,
          financialYear: submission.financialYear,
          reviewPeriod: submission.reviewPeriod,
          currentReviewer: currentReviewer,
          approvalWorkflow: submission.approvalWorkflow || [],
          isDraft: submission.isDraft || false,
          daysSinceAction: daysSinceAction,
          isOverdue: isOverdue,
          canEdit: isOwnSubmission ? this.canEditSubmission(submission) : false,
          nextAction: isOwnSubmission
            ? this.getNextAction(submission)
            : 'Review Required',

          // Detailed information
          objectives: detailedObjectives,
          developmentPlans: detailedDevelopmentPlans,
          reviews: detailedReviews,
          workflowDetails: detailedApprovalWorkflow,
          allAttachments: allAttachmentsFlat,

          // Summary statistics
          summaryStats: {
            totalWeightage: detailedObjectives.reduce(
              (sum, obj) => sum + obj.weightage,
              0,
            ),
            completedObjectives: detailedObjectives.filter(
              (obj) => obj.hasSupervisorRating,
            ).length,
            pendingObjectives: detailedObjectives.filter(
              (obj) => !obj.hasSupervisorRating,
            ).length,
            developmentPlansWithFeedback: detailedDevelopmentPlans.filter(
              (plan) => plan.hasSupervisorFeedback,
            ).length,
            totalAttachments: allAttachmentsFlat.length,
            reviewsCompleted: detailedReviews.length,
            workflowStepsCompleted: detailedApprovalWorkflow.filter(
              (step) => step.isCompleted,
            ).length,
            workflowStepsPending: detailedApprovalWorkflow.filter(
              (step) => step.isPending,
            ).length,
          },

          // Add flags to identify submission type
          isOwnSubmission: isOwnSubmission,
          isForApproval: isForApproval,
          isPlaceholder: false,
          unreadMessageCount: unreadCountMap[submission._id.toString()] || 0,
        };

        // Add approval-specific information for submissions requiring approval
        if (isForApproval && userRole) {
          const approverRole = this.getApproverRole(
            submission,
            userRole.user.id,
          );
          const canApprove = this.canApprove(submission, userRole.user.id);
          const daysPending = this.calculateDaysPending(submission);
          const nextActionForApprover = this.getNextActionForApprover(
            submission,
            approverRole,
          );

          submissionSummary['approverRole'] = approverRole;
          submissionSummary['canApprove'] = canApprove;
          submissionSummary['daysPending'] = daysPending;
          submissionSummary['nextActionForApprover'] = nextActionForApprover;
        }

        return submissionSummary;
      },
    );

    // Handle performance score sorting if requested
    if (filters?.sortBy === 'performanceScore') {
      formattedSubmissions.sort((a, b) => {
        const comparison = a.performanceScore - b.performanceScore;
        return filters.sortOrder === 'asc' ? comparison : -comparison;
      });
    }

    const response = {
      statusInfo: statusInfo,
      submissions: formattedSubmissions,
      totalCount: totalCount,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalCount / limit),
        pageSize: limit,
        hasNextPage: page < Math.ceil(totalCount / limit),
        hasPrevPage: page > 1,
      },
      filters: filters || {},
    } as any;

    // Add approver information for to-review and all requests
    if ((isToReviewRequest || isAllRequest) && userRole) {
      response['approverInfo'] = {
        name: userRole.user.fullName,
        email: userRole.user.email,
        department: userRole.user.department,
        roles: userRole.roles,
      };
    }

    return response;
  }

  private getApproverRole(submission: any, approverId: Types.ObjectId): string {
    if (submission.currentReviewerId?.toString() === approverId.toString()) {
      switch (submission.status) {
        case IPPAStatus.PENDING_SUPERVISOR_REVIEW:
          return 'Line Manager';
        case IPPAStatus.PENDING_HOD_REVIEW:
          return 'HOD';
        case IPPAStatus.PENDING_HR_REVIEW:
          return 'HR';
        default:
          return 'Reviewer';
      }
    }

    const workflowStep = submission.approvalWorkflow?.find(
      (step) => step.personId.toString() === approverId.toString(),
    );
    return workflowStep?.title || 'Unknown';
  }

  private canApprove(submission: any, approverId: Types.ObjectId): boolean {
    return (
      submission.currentReviewerId?.toString() === approverId.toString() &&
      submission.status !== IPPAStatus.COMPLETED &&
      submission.status !== IPPAStatus.DRAFT
    );
  }

  private calculateDaysPending(submission: any): number {
    const now = new Date();
    const startDate = submission.submittedAt || submission.createdAt;
    const diffTime = Math.abs(now.getTime() - startDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  private isSubmissionOverdue(submission: any): boolean {
    const daysPending = this.calculateDaysPending(submission);

    switch (submission.status) {
      case IPPAStatus.PENDING_SUPERVISOR_REVIEW:
        return daysPending > 14;
      case IPPAStatus.PENDING_HOD_REVIEW:
        return daysPending > 10;
      case IPPAStatus.PENDING_HR_REVIEW:
        return daysPending > 7;
      default:
        return false;
    }
  }

  private getNextActionForApprover(
    submission: any,
    approverRole: string,
  ): string {
    switch (approverRole) {
      case 'Line Manager':
        return 'Review objectives and provide ratings';
      case 'HOD':
        return 'Review and approve (optional ratings)';
      case 'HR':
        return 'Final review and complete';
      default:
        return 'Review required';
    }
  }

  async getUserIPPARole(userEmail: string): Promise<any> {
    // Find the user
    const ppaUser = await this.ippaUserModel
      .findOne({ email: userEmail })
      .populate('department lineManager')
      .lean();

    const mainUser = await this.userModel
      .findOne({ email: userEmail })
      .populate('department')
      .lean();

    if (!ppaUser || !mainUser) {
      throw new Error('User not found');
    }

    // Check if user is a line manager (has reportees)
    const reportees = await this.ippaUserModel
      .find({ lineManager: mainUser!._id })
      .select('fullName email department departmentCode employeeGrade position')
      .lean();

    // Check if user is HOD for any department
    const hodDepartments = await this.departmentModel
      .find({
        hodPersonId: new Types.ObjectId(mainUser!._id),
        isActive: true,
      })
      .select('code description')
      .lean();

    // Get unique department codes from reportees
    const reporteeDepartmentCodes =
      reportees.length > 0
        ? [
          ...new Set(
            reportees
              .filter((r) => r.departmentCode)
              .map((r) => r.departmentCode),
          ),
        ]
        : [];

    // console.log('reporteeDepartmentCodes:', reporteeDepartmentCodes); // Debug log

    // Fetch department details for reportee departments using department codes
    const lineManagerDepartments =
      reporteeDepartmentCodes.length > 0
        ? await this.departmentModel
          .find({
            code: { $in: reporteeDepartmentCodes },
          })
          .select('code description')
          .lean()
        : [];

    // console.log('lineManagerDepartments:', lineManagerDepartments); // Debug log

    // Combine HOD departments and line manager departments (remove duplicates)
    const allManagedDepartments = [...hodDepartments];

    // Add line manager departments that are not already in HOD departments
    lineManagerDepartments.forEach((lmDept) => {
      if (
        !hodDepartments.some(
          (hodDept) => hodDept._id.toString() === lmDept._id.toString(),
        )
      ) {
        allManagedDepartments.push(lmDept);
      }
    });

    // Check if user is HR
    const isHR = mainUser!._id.toString() === '674829e0f3d796aa36ff1e0d';

    // Determine user roles
    const roles = ['employee']; // Everyone is an employee

    if (reportees.length > 0) {
      roles.push('supervisor');
    }

    if (hodDepartments.length > 0) {
      roles.push('hod');
    }

    if (isHR) {
      roles.push('hr');
    }

    // Define permissions based on roles
    const permissions = {
      canSubmitIPPA: true,
      canApproveAsLineManager: reportees.length > 0,
      canApproveAsHOD: hodDepartments.length > 0,
      canReviewAsHR: isHR,
      canViewAllSubmissions: isHR,
      canViewDepartmentSubmissions: allManagedDepartments.length > 0, // Updated to check combined departments
      canViewOwnSubmissions: true,
      canViewReporteeSubmissions: reportees.length > 0,
    };

    return {
      user: {
        id: mainUser._id,
        email: mainUser.email,
        fullName: mainUser.fullName,
        department: ppaUser.department,
        position: ppaUser.position,
        employeeGrade: ppaUser.employeeGrade,
      },
      roles,
      permissions,
      isLineManager: reportees.length > 0,
      isHOD: hodDepartments.length > 0,
      isHR,
      managedDepartments: allManagedDepartments, // Now includes both HOD and line manager departments
      reportees: reportees.map((r) => ({
        id: r._id,
        fullName: r.fullName,
        email: r.email,
        department: r.department,
        departmentCode: r.departmentCode,
        position: r.position,
        employeeGrade: r.employeeGrade,
      })),
      hodDepartmentCodes: hodDepartments.map((d) => d.code),
      lineManagerDepartmentCodes: lineManagerDepartments.map((d) => d.code), // Added for clarity
      allManagedDepartmentCodes: allManagedDepartments.map((d) => d.code), // All department codes managed
    };
  }

  /**
   * Calculate days since last action on submission
   */
  private calculateDaysSinceAction(submission: any): number {
    const now = new Date();
    let lastActionDate: Date;

    switch (submission.status) {
      case IPPAStatus.DRAFT:
        lastActionDate = submission.lastModifiedAt || submission.createdAt;
        break;
      case IPPAStatus.PENDING_SUPERVISOR_REVIEW:
        lastActionDate = submission.submittedAt || submission.createdAt;
        break;
      case IPPAStatus.PENDING_HR_REVIEW:
        lastActionDate =
          submission.supervisorReviewedAt || submission.submittedAt;
        break;
      default:
        lastActionDate = submission.lastModifiedAt || submission.createdAt;
    }

    const diffTime = Math.abs(now.getTime() - lastActionDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Check if submission is overdue based on business rules
   */
  private checkIfOverdue(submission: any): boolean {
    const daysSinceAction = this.calculateDaysSinceAction(submission);

    switch (submission.status) {
      case IPPAStatus.DRAFT:
        return daysSinceAction > 30; // 30 days for draft
      case IPPAStatus.PENDING_SUPERVISOR_REVIEW:
        return daysSinceAction > 14; // 14 days for supervisor review
      case IPPAStatus.PENDING_HR_REVIEW:
        return daysSinceAction > 7; // 7 days for HR review
      default:
        return false;
    }
  }

  /**
   * Check if submission can be edited by employee
   */
  private canEditSubmission(submission: any): boolean {
    return submission.status === IPPAStatus.DRAFT;
  }

  /**
   * Get next action required for the submission
   */
  private getNextAction(submission: any): string {
    switch (submission.status) {
      case IPPAStatus.DRAFT:
        return 'Complete and submit IPPA form';
      case IPPAStatus.PENDING_SUPERVISOR_REVIEW:
        return 'Waiting for line manager review';
      case IPPAStatus.SUPERVISOR_REVIEWED:
        return 'Reviewed by line manager';
      case IPPAStatus.PENDING_HR_REVIEW:
        return 'Waiting for HR review';
      case IPPAStatus.COMPLETED:
        return 'IPPA process completed';
      default:
        return 'No action required';
    }
  }

  /**
   * Get available financial years for filtering
   */
  async getAvailableFinancialYears(employeeEmail: string): Promise<string[]> {
    const employee = await this.ippaUserModel
      .findOne({ email: employeeEmail.toLowerCase().trim() })
      .lean();

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    const years = await this.ippaSubmissionModel
      .distinct('financialYear', { employeeId: employee._id })
      .exec();

    return years.sort((a, b) => b.localeCompare(a)); // Sort descending
  }

  /**
   * Get status display information for UI
   */
  private getStatusDisplay(status: IPPAStatus): {
    label: string;
    color: string;
  } {
    switch (status) {
      case IPPAStatus.DRAFT:
        return { label: 'Draft', color: '#6c757d' }; // Gray
      case IPPAStatus.SUBMITTED:
        return { label: 'Submitted', color: '#17a2b8' }; // Info blue
      case IPPAStatus.PENDING_SUPERVISOR_REVIEW:
        return { label: 'Pending Review', color: '#fd7e14' }; // Orange
      case IPPAStatus.SUPERVISOR_REVIEWED:
        return { label: 'Approved', color: '#28a745' }; // Green
      case IPPAStatus.PENDING_HR_REVIEW:
        return { label: 'HR Review', color: '#ffc107' }; // Yellow
      case IPPAStatus.COMPLETED:
        return { label: 'Completed', color: '#20c997' }; // Teal
      default:
        return { label: 'Unknown', color: '#6c757d' }; // Gray
    }
  }

  /**
   * Get dashboard summary for employee
   */
  async getEmployeeDashboard(employeeEmail: string): Promise<{
    statusInfo: SubmissionStatusInfo;
    recentSubmissions: SubmissionSummary[];
    pendingActions: any[];
  }> {
    const employee = await this.ippaUserModel
      .findOne({ email: employeeEmail.toLowerCase().trim() })
      .lean();

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    // Get all submissions for status counting
    const allSubmissions = await this.ippaSubmissionModel
      .find({ employeeId: employee._id })
      .lean()
      .exec();

    // Calculate status information
    const statusInfo: SubmissionStatusInfo = {
      totalSubmissions: allSubmissions.length,
      approved: allSubmissions.filter(
        (s) => s.status === IPPAStatus.SUPERVISOR_REVIEWED,
      ).length,
      pendingReview: allSubmissions.filter(
        (s) => s.status === IPPAStatus.PENDING_HR_REVIEW,
      ).length,
      completed: allSubmissions.filter((s) => s.status === IPPAStatus.COMPLETED)
        .length,
      submitted: allSubmissions.filter(
        (s) => s.status === IPPAStatus.PENDING_SUPERVISOR_REVIEW,
      ).length,
      draft: allSubmissions.filter((s) => s.status === IPPAStatus.DRAFT).length,
    };

    // Get recent submissions (last 5)
    const recentSubmissions = await this.ippaSubmissionModel
      .find({ employeeId: employee._id })
      .populate('currentReviewerId', 'fullName email department')
      .sort({ lastModifiedAt: -1 })
      .limit(5)
      .lean()
      .exec();

    const formattedRecent: SubmissionSummary[] = recentSubmissions.map(
      (submission): SubmissionSummary => {
        const performanceScore =
          submission.supervisorOverallScore ||
          submission.employeeOverallScore ||
          0;
        const performanceRating =
          this.calculatePerformanceRating(performanceScore);
        const statusDisplay = this.getStatusDisplay(submission.status);

        // Get current reviewer information
        let currentReviewer:
          | { name: string; email: string; department: string }
          | undefined = undefined;
        if (
          submission.currentReviewerId &&
          (submission as any).currentReviewerId.fullName
        ) {
          currentReviewer = {
            name: (submission as any).currentReviewerId.fullName,
            email: (submission as any).currentReviewerId.email,
            department:
              (submission as any).currentReviewerId.department || 'N/A',
          };
        }

        return {
          ippaId: submission.ippaId,
          submissionId: submission._id.toString(),
          employeeName: submission.employeeName,
          employeeId: submission.employeeId.toString(),
          department: submission.submissionDepartment,
          position: submission.employeePosition,
          grade: submission.employeeGrade,
          status: submission.status,
          statusLabel: statusDisplay.label,
          statusColor: statusDisplay.color,
          performanceScore: parseFloat(performanceScore.toFixed(1)),
          performanceRating: performanceRating.rating,
          totalObjectives: submission.objectives?.length || 0,
          totalDevelopmentPlans: submission.developmentPlans?.length || 0,
          attachmentsCount: 0, // Calculate if needed
          submittedAt: submission.submittedAt || null,
          lastModifiedAt: submission.lastModifiedAt || null,
          financialYear: submission.financialYear,
          reviewPeriod: submission.reviewPeriod,
          currentReviewer: currentReviewer,
          approvalWorkflow: submission.approvalWorkflow || [],
          isDraft: submission.isDraft || false,
        } as SubmissionSummary;
      },
    );

    // Get pending actions (drafts and submissions awaiting action)
    const pendingActions = allSubmissions.filter(
      (s) =>
        s.status === IPPAStatus.DRAFT ||
        s.status === IPPAStatus.PENDING_SUPERVISOR_REVIEW,
    );

    return {
      statusInfo: statusInfo,
      recentSubmissions: formattedRecent,
      pendingActions: pendingActions,
    };
  }

  /**
   * Get submissions by status for filtering
   */
  async getSubmissionsByStatus(
    employeeEmail: string,
    status?: IPPAStatus,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    submissions: SubmissionSummary[];
    totalCount: number;
    statusInfo: SubmissionStatusInfo;
  }> {
    const employee = await this.ippaUserModel
      .findOne({ email: employeeEmail.toLowerCase().trim() })
      .lean();

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    if (status === IPPAStatus.SUBMITTED) {
      status = IPPAStatus.PENDING_SUPERVISOR_REVIEW;
    }
    // Build query filter
    const filter: any = { employeeId: employee._id };
    if (status) {
      filter.status = status;
    }

    // Get total count for pagination
    const totalCount = await this.ippaSubmissionModel.countDocuments(filter);

    // Get filtered submissions
    const skip = (page - 1) * limit;
    const submissions = await this.ippaSubmissionModel
      .find(filter)
      .populate('currentReviewerId', 'fullName email department')
      .sort({ financialYear: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean()
      .exec();

    // Get status info for all submissions
    const allSubmissions = await this.ippaSubmissionModel
      .find({ employeeId: employee._id })
      .lean()
      .exec();

    const statusInfo: SubmissionStatusInfo = {
      totalSubmissions: allSubmissions.length,
      approved: allSubmissions.filter(
        (s) => s.status === IPPAStatus.SUPERVISOR_REVIEWED,
      ).length,
      pendingReview: allSubmissions.filter(
        (s) => s.status === IPPAStatus.PENDING_HR_REVIEW,
      ).length,
      completed: allSubmissions.filter((s) => s.status === IPPAStatus.COMPLETED)
        .length,
      submitted: allSubmissions.filter(
        (s) => s.status === IPPAStatus.PENDING_SUPERVISOR_REVIEW,
      ).length,
      draft: allSubmissions.filter((s) => s.status === IPPAStatus.DRAFT).length,
    };

    // Format submissions
    const formattedSubmissions: SubmissionSummary[] = submissions.map(
      (submission): SubmissionSummary => {
        const performanceScore =
          submission.supervisorOverallScore ||
          submission.employeeOverallScore ||
          0;
        const performanceRating =
          this.calculatePerformanceRating(performanceScore);
        const statusDisplay = this.getStatusDisplay(submission.status);

        const attachmentsCount =
          submission.reviews?.reduce((count, review) => {
            return count + (review.attachments?.length || 0);
          }, 0) || 0;

        // Get current reviewer information
        let currentReviewer:
          | { name: string; email: string; department: string }
          | undefined = undefined;
        if (
          submission.currentReviewerId &&
          (submission as any).currentReviewerId.fullName
        ) {
          currentReviewer = {
            name: (submission as any).currentReviewerId.fullName,
            email: (submission as any).currentReviewerId.email,
            department:
              (submission as any).currentReviewerId.department || 'N/A',
          };
        }

        // Calculate days since submission/last action
        const daysSinceAction = this.calculateDaysSinceAction(submission);

        // Check if submission is overdue
        const isOverdue = this.checkIfOverdue(submission);

        // Process objectives with detailed information
        const detailedObjectives = (submission.objectives || []).map(
          (obj, index) => ({
            index: index + 1,
            keyResultsArea: obj.keyResultsArea,
            workObjective: obj.workObjective,
            targetBase: obj.targetBase,
            targetExceed: obj.targetExceed,
            targetStretch: obj.targetStretch,
            weightage: obj.weightage,
            yearEndResults: obj.yearEndResults,
            performanceAssessment: obj.performanceAssessment,
            employeeRating: obj.employeeRating,
            employeeWeightedPoints: obj.employeeWeightedPoints || 0,
            supervisorRating: obj.supervisorRating || null,
            supervisorWeightedPoints: obj.supervisorWeightedPoints || null,
            supervisorComments: obj.supervisorComments || null,
            hasEmployeeRating: !!obj.employeeRating,
            hasSupervisorRating: !!obj.supervisorRating,
          }),
        );

        // Process development plans with detailed information
        const detailedDevelopmentPlans = (
          submission.developmentPlans || []
        ).map((plan, index) => ({
          index: index + 1,
          competencyType: plan.competencyType,
          areaForImprovement: plan.areaForImprovement,
          activity: plan.activity,
          timeline: plan.timeline,
          yearEndComments: plan.yearEndComments || '',
          supervisorFeedback: plan.supervisorFeedback || null,
          hodRemarks: plan.hodRemarks || null,
          hasYearEndComments: !!plan.yearEndComments,
          hasSupervisorFeedback: !!plan.supervisorFeedback,
          hasHodRemarks: !!plan.hodRemarks,
        }));

        // Process reviews with detailed attachments
        const detailedReviews = (submission.reviews || []).map((review) => ({
          reviewerType: review.reviewerType,
          reviewerId: review.reviewerId,
          reviewerName: review.reviewerName,
          remarks: review.remarks || '',
          attachments: (review.attachments || []).map((attachment) => ({
            fileName: attachment.fileName,
            filePath: attachment.filePath,
            fileSize: attachment.fileSize,
            mimeType: attachment.mimeType,
            uploadedAt: attachment.uploadedAt,
            description: attachment.description || '',
            fileSizeFormatted: this.formatFileSize(attachment.fileSize),
          })),
          overallScore: review.overallScore || null,
          reviewedAt: review.reviewedAt,
          department: review.department || '',
          attachmentCount: review.attachments?.length || 0,
          hasRemarks: !!review.remarks,
          hasAttachments: (review.attachments?.length || 0) > 0,
        }));

        // Process approval workflow with enhanced details
        const detailedApprovalWorkflow = (
          submission.approvalWorkflow || []
        ).map((step) => ({
          stepId: step.stepId,
          title: step.title,
          personId: step.personId,
          personName: step.personName,
          department: step.department || '',
          date: step.date,
          status: step.status,
          isCompleted: step.status === 'completed',
          isPending: step.status === 'pending',
          isUpcoming: step.status === 'upcoming',
          statusColor: this.getWorkflowStepColor(step.status),
          formattedDate: step.date !== '-' ? step.date : 'Pending',
        }));

        // Aggregate all attachments across reviews
        const allAttachmentsFlat = detailedReviews.reduce((acc, review) => {
          const reviewAttachments = review.attachments.map((att) => ({
            ...att,
            reviewerType: review.reviewerType,
            reviewerName: review.reviewerName,
            reviewedAt: review.reviewedAt,
          }));
          return [...acc, ...reviewAttachments];
        }, []);

        return {
          ippaId: submission.ippaId,
          submissionId: submission._id.toString(),
          employeeName: submission.employeeName,
          employeeId: submission.employeeId.toString(),
          department: submission.submissionDepartment,
          departmentCode: submission.submissionDepartmentCode,
          position: submission.employeePosition,
          grade: submission.employeeGrade,
          status: submission.status,
          statusLabel: statusDisplay.label,
          statusColor: statusDisplay.color,
          performanceScore: parseFloat(performanceScore.toFixed(1)),
          performanceRating: performanceRating.rating,
          totalObjectives: submission.objectives?.length || 0,
          totalDevelopmentPlans: submission.developmentPlans?.length || 0,
          attachmentsCount: attachmentsCount,
          submittedAt: submission.submittedAt || null,
          lastModifiedAt: submission.lastModifiedAt || null,
          financialYear: submission.financialYear,
          reviewPeriod: submission.reviewPeriod,
          currentReviewer: currentReviewer,
          approvalWorkflow: submission.approvalWorkflow || [],
          isDraft: submission.isDraft || false,
        } as SubmissionSummary;
      },
    );

    return {
      submissions: formattedSubmissions,
      totalCount: totalCount,
      statusInfo: statusInfo,
    };
  }

  /**
   * Format file size to human readable format
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get color for workflow step status
   */
  private getWorkflowStepColor(status: string): string {
    switch (status) {
      case 'completed':
        return '#28a745'; // Green
      case 'pending':
        return '#fd7e14'; // Orange
      case 'upcoming':
        return '#6c757d'; // Gray
      default:
        return '#6c757d'; // Gray
    }
  }
}
