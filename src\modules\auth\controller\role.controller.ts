import { Controller, Post, Get, Body } from '@nestjs/common';
import { CreateRoleDTO } from '../dto/create-role.dto';
import { RoleService } from '../service/role.service';

@Controller('roles')
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @Post('create')
  async createRole(@Body() createRoleDto: CreateRoleDTO) {
    return this.roleService.create(createRoleDto);
  }

  @Get()
  async findAll() {
    return this.roleService.findAll();
  }
}
