import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type PJMDocument = PJM & Document;

@Schema({ timestamps: true, collection: 'finance_pjm' })
export class PJM {

  @Prop()
  serialNo: number;

  @Prop({ type: Types.ObjectId, ref: 'Department', required: true })
  departmentId: Types.ObjectId;

  @Prop()
  client: string;

  @Prop()
  contractNo: string;

  @Prop()
  projectCode: string;

  @Prop()
  projectName: string;

  @Prop()
  service: string;

  @Prop()
  accountManager: string;

  @Prop()
  startDate: string;

  @Prop()
  endDate: string;

  @Prop()
  extendedDate: string;

  @Prop()
  createdBy: string;

  @Prop()
  status: string; // 'launced', 'expired', 'extended', 'completed', 'closed', 'cancelled'

  @Prop({attachments: [
    {
      remark: { type: String },
      type: { type: String },
      s3Url: { type: String },
    }
  ]})
    attachments: Array<{
      remark: string;
      type: string;
      s3Url: string;
    }>;
}

export const PJMSchema = SchemaFactory.createForClass(PJM);
PJMSchema.index({ client: 1 });
PJMSchema.index({ startDate: 1 });
PJMSchema.index({ endDate: 1 });
PJMSchema.index({ projectCode: 1 });
