# UzmaCloud API Documentation

## 📋 Table of Contents
- [Project Overview](#project-overview)
- [Tech Stack](#tech-stack)
- [Architecture](#architecture)
- [Getting Started](#getting-started)
- [API Modules](#api-modules)
- [Database Schema](#database-schema)
- [Authentication](#authentication)
- [Deployment](#deployment)

## 🚀 Project Overview

UzmaCloud API is a comprehensive enterprise management system built with NestJS and TypeScript. It provides automated billing workflows, payment advice management, face recognition, and document processing capabilities.

### Key Features
- **Auto-Billing System** - Automated billing workflow with RPA integration
- **Payment Advice Management** - Complete payment processing workflow
- **Face Recognition** - AWS Rekognition integration for user verification
- **Document Management** - S3-based file storage and processing
- **E-Signature** - Digital document signing capabilities
- **Multi-level Approval Workflows** - Configurable approval chains
- **Real-time Notifications** - Email and system notifications

## 🛠️ Tech Stack

### Backend Framework
- **NestJS** v11.0.1 - Progressive Node.js framework
- **TypeScript** v5.7.3 - Type-safe JavaScript
- **Node.js** v18+ - Runtime environment

### Database
- **MongoDB** v7.6.3 - NoSQL document database
- **Mongoose** v7.6.3 - MongoDB object modeling
- **AWS DocumentDB** - Production database (MongoDB-compatible)

### Cloud Services
- **AWS S3** - File storage and management
- **AWS Rekognition** - Face detection and recognition
- **AWS SES** - Email service integration

### Additional Technologies
- **JWT** - Authentication and authorization
- **Multer** - File upload handling
- **ExcelJS** - Excel file processing
- **Crypto-JS** - Data encryption
- **Moment.js** - Date/time manipulation
- **EJS** - Email template rendering

## 🏗️ Architecture

### Project Structure
```
uzmacloud-api/
├── src/
│   ├── modules/           # Feature modules
│   │   ├── auto-billing/  # Automated billing system
│   │   ├── pa/           # Payment advice management
│   │   ├── user/         # User management
│   │   ├── auth/         # Authentication
│   │   ├── face/         # Face recognition
│   │   ├── esign/        # Electronic signatures
│   │   ├── finance/      # Financial operations
│   │   ├── seah/         # Project management
│   │   ├── health/       # Health checks
│   │   └── common/       # Shared utilities
│   ├── config/           # Configuration files
│   ├── main.ts          # Application entry point
│   └── app.module.ts    # Root module
├── test/                # End-to-end tests
├── dist/               # Compiled output
├── scripts/            # Utility scripts
└── docs/              # Documentation
```

### Module Architecture
Each module follows NestJS best practices:
- **Controllers** - Handle HTTP requests and responses
- **Services** - Business logic implementation
- **Schemas** - MongoDB data models
- **DTOs** - Data transfer objects for validation
- **Guards** - Authentication and authorization

## 🚀 Getting Started

### Prerequisites
- Node.js v18 or higher
- MongoDB (local) or AWS DocumentDB (production)
- AWS Account (for S3, Rekognition, SES)

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd uzmacloud-api

# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Configure environment variables
# Edit .env file with your settings
```

### Environment Configuration
```bash
# Application
NODE_ENV=development
PORT=3000

# Database
DATABASE_URL=mongodb://localhost:27017/uzmacloud-dev

# AWS Services
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=ap-southeast-1
AWS_S3_BUCKET=your-s3-bucket
REKOGNITION_COLLECTION_ID=your-collection-id

# Authentication
JWT_SECRET=your-jwt-secret
ENCRYPTION_SECRET=your-encryption-secret

# External APIs
RPA_ENDPOINT=your-rpa-endpoint
RPA_API_TOKEN=your-rpa-token
```

### Running the Application
```bash
# Development mode
npm run start:dev

# Production mode
npm run build
npm run start:prod

# Debug mode
npm run start:debug
```

### API Access
- **Base URL**: `http://localhost:3000`
- **API Prefix**: `/api`
- **Health Check**: `http://localhost:3000/health`
- **API Documentation**: `http://localhost:3000/api/docs` (when enabled)

## 📡 API Modules

### Core Modules Overview
1. **Auto-Billing** (`/api/billing/*`) - Automated billing workflows
2. **Payment Advice** (`/api/payment-advice/*`) - Payment processing
3. **User Management** (`/api/users/*`) - User operations
4. **Authentication** (`/api/auth/*`) - Login/logout/tokens
5. **Face Recognition** (`/api/face/*`) - Biometric verification
6. **E-Signature** (`/api/esign/*`) - Document signing
7. **Finance** (`/api/finance/*`) - Financial operations
8. **Health** (`/health`) - System monitoring

### API Response Format
```json
{
  "success": true,
  "data": {},
  "message": "Operation successful",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": []
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 🗄️ Database Schema

### Collections Overview
- `users` - User accounts and profiles
- `departments` - Organizational departments
- `billing_main` - Billing advice records
- `pa_main` - Payment advice records
- `approval_master` - Approval flow configurations
- `rpa_schedule` - RPA task scheduling
- `face_collections` - Face recognition data
- `email_scheduler` - Email queue management

### Key Relationships
- Users belong to Departments
- Billing/Payment Advice have Approval Workflows
- Documents are stored in S3 with metadata in MongoDB
- RPA tasks are linked to Billing records

## 🔐 Authentication

### JWT-based Authentication
- Login endpoint: `POST /api/auth/login`
- Token refresh: `POST /api/auth/refresh`
- Protected routes require `Authorization: Bearer <token>` header

### Face Recognition Authentication
- Face registration: `POST /api/face/register`
- Face verification: `POST /api/face/verify`
- Integrated with AWS Rekognition service

## 🚀 Deployment

### Docker Deployment
```bash
# Build image
docker build -t uzmacloud-api .

# Run container
docker run -p 3000:3000 uzmacloud-api
```

### AWS ECS Deployment
- Automated via GitLab CI/CD
- Uses AWS ECR for container registry
- Deployed to AWS ECS Fargate
- Health checks and auto-scaling configured

### Environment-specific Configurations
- **Development**: Local MongoDB, local file storage
- **Staging**: AWS DocumentDB, S3 storage
- **Production**: Full AWS stack with monitoring

---

*For detailed API endpoints and usage examples, see the individual module documentation files.*
