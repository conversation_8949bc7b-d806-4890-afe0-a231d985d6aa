import { IsString, IsNotEmpty, IsArray, ValidateNested, IsEnum, IsBoolean, IsOptional, IsNumber, IsEmail } from 'class-validator';
import { Type } from 'class-transformer';

export class ApproverDto {
  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsString()
  @IsNotEmpty()
  role: string;

  @IsNumber()
  @IsNotEmpty()
  order: number;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export enum ApprovalType {
  SEQUENTIAL = 'sequential',
  PARALLEL = 'parallel',
}

export class CreateApprovalFlowDto {
  @IsString()
  @IsNotEmpty()
  department: string;

  @IsString()
  @IsNotEmpty()
  project: string;

  @IsString()
  @IsNotEmpty()
  documentType: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ApproverDto)
  approvers: ApproverDto[];

  @IsEnum(ApprovalType)
  @IsOptional()
  approvalType?: ApprovalType;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export class UpdateApprovalFlowDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ApproverDto)
  @IsOptional()
  approvers?: ApproverDto[];

  @IsEnum(ApprovalType)
  @IsOptional()
  approvalType?: ApprovalType;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export class GetApprovalFlowQueryDto {
  @IsString()
  @IsOptional()
  department?: string;

  @IsString()
  @IsOptional()
  project?: string;

  @IsString()
  @IsOptional()
  documentType?: string;
}