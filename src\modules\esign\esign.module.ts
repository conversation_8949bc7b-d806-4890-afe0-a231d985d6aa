import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ESign, ESignSchema } from './schema/document.schema';
import { ESignSerialNumber, ESignSerialNumberSchema } from './schema/esign-serial-no.schema';
import { Signature, SignatureSchema } from './schema/signature.schema';
import { DocumentsController } from './controller/document.controller';
import { SignaturesController } from './controller/signature.controller';
import { DocumentsService } from './service/document.service';
import { ESignSerialNumberService } from './service/esign-serial-no.service';
import { SignaturesService } from './service/signature.service';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ESign.name, schema: ESignSchema },
      { name: ESignSerialNumber.name, schema: ESignSerialNumberSchema },
      { name: Signature.name, schema: SignatureSchema },
    ]),
    CommonModule
  ],
  controllers: [DocumentsController, SignaturesController],
  providers: [DocumentsService, ESignSerialNumberService, SignaturesService],
  exports: [],
})
export class ESignModule {}