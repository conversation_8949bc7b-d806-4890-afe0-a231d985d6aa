import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { DocumentStatus, ESign, ESignDocument } from '../schema/document.schema';
import { Signature, SignatureDocument } from '../schema/signature.schema';
import { ESignSerialNumberService } from './esign-serial-no.service';
import moment from 'moment';
import { S3Service } from 'src/modules/common/services/s3.service';

export interface UploadDocumentDto {
  title: string;
  documentType: string;
  departmentId: string;
  priority?: string;
  dueDate?: Date;
  notes?: string;
  uploadedBy: string;
}

export interface ApproveDocumentDto {
  id: string;
}

@Injectable()
export class DocumentsService {
  constructor(
    @InjectModel(ESign.name) private documentModel: Model<ESignDocument>,
    @InjectModel(Signature.name) private signatureModel: Model<SignatureDocument>,
    private s3Service: S3Service,
    private readonly esignSerialNumberService: ESignSerialNumberService
  ) { }

  async uploadDocument(
    file: Express.Multer.File,
    uploadDto: UploadDocumentDto,
  ): Promise<ESignDocument> {

    const uploadResult = await this.s3Service.uploadESignDoc(file);

    const prefix = 'ESIGN';
    const serialNo = await this.esignSerialNumberService.getNextSerialNumber(prefix);

    // const formattedDate = moment(uploadDto.dueDate, 'DD/MM/YYYY', true); // strict mode
    // if (!formattedDate.isValid()) throw new Error('Invalid due date');
    // const dueDate = formattedDate.toDate(); // Use this when saving to DB

    // Create document record
    const document = new this.documentModel({
      serialNumber: serialNo,
      title: uploadDto.title,
      filename: file.originalname,
      originalFilename: file.originalname,
      mimeType: file.mimetype,
      fileSize: file.size,
      documentType: uploadDto.documentType,
      originalUrl: uploadResult.value,
      originalKey: uploadResult.key,
      uploadedBy: new Types.ObjectId(uploadDto.uploadedBy),
      departmentId: new Types.ObjectId(uploadDto.departmentId),
      status: DocumentStatus.PENDING,
      priority: uploadDto.priority || 'medium',
      dueDate: new Date,
      notes: uploadDto.notes,
    });

    //EMAIL NOTIFY TO BOSS
    return document.save();
  }

  async updateDocumentVersion(
    documentId: string,
    file: Express.Multer.File,
    uploadedBy: string,
    notes?: string,
  ): Promise<ESignDocument> {
    const document = await this.getDocumentById(documentId);

    if (document.status !== DocumentStatus.PENDING) {
      throw new BadRequestException('Can only update pending documents');
    }

    // const documentType = await this.documentTypeModel.findById(document.documentType);

    // // Validate file type and size
    // if (!documentType.allowedMimeTypes.includes(file.mimetype)) {
    //   throw new BadRequestException(`File type ${file.mimetype} not allowed`);
    // }

    // const fileSizeMB = file.size / (1024 * 1024);
    // if (fileSizeMB > documentType.maxFileSize) {
    //   throw new BadRequestException(`File size exceeds maximum allowed size`);
    // }

    const uploadResult = await this.s3Service.uploadESignDoc(file);

    // Update document
    document.currentVersion += 1;
    document.originalUrl = uploadResult.value;
    document.originalKey = uploadResult.key;
    document.filename = file.originalname;
    document.fileSize = file.size;

    return document.save();
  }

  async getDocumentsByDepartment(
    departmentId: string,
    status?: DocumentStatus,
    priority?: string,
  ): Promise<ESignDocument[]> {
    var filter: any;

    if(departmentId) {
      filter = { departmentId: new Types.ObjectId(departmentId) };
    }

    if (status) {
      filter.status = status;
    }

    if (priority) {
      filter.priority = priority;
    }

    return this.documentModel
      .find(filter)
      .populate('uploadedBy', 'name email')
      .populate('departmentId', 'name')
      .sort({ priority: -1, dueDate: 1, createdAt: -1 })
      .exec();
  }

  async getDocumentById(id: string): Promise<ESignDocument> {
    const document = await this.documentModel
      .findById(id)
      .populate('uploadedBy', 'name email')
      .populate('departmentId', 'name')
      .populate('signedBy', 'name email')
      .exec();

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    return document;
  }


  async approveDocument(documentId: string, file: Express.Multer.File, notes?: string): Promise<ESignDocument> {
    const document = await this.getDocumentById(documentId);

    const uploadResult = await this.s3Service.uploadESignDoc(file);

    document.signedKey = uploadResult.key;
    document.signedUrl = uploadResult.value;
    document.status = DocumentStatus.APPROVED;
    document.approvedAt = new Date();
    if (notes) {
      document.notes = notes;
    }

    return document.save();
  }

  async rejectDocument(documentId: string, notes: string): Promise<ESignDocument> {
    const document = await this.getDocumentById(documentId);

    document.status = DocumentStatus.REJECTED;
    document.notes = notes;

    return document.save();
  }

  async getDocumentStats(departmentId?: string): Promise<any> {
    const matchStage: any = {};
    if (departmentId) {
      matchStage.departmentId = new Types.ObjectId(departmentId);
    }

    const stats = await this.documentModel.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalDocuments: { $sum: 1 },
          pendingCount: {
            $sum: { $cond: [{ $eq: ['$status', DocumentStatus.PENDING] }, 1, 0] }
          },
          signedCount: {
            $sum: { $cond: [{ $eq: ['$status', DocumentStatus.SIGNED] }, 1, 0] }
          },
          approvedCount: {
            $sum: { $cond: [{ $eq: ['$status', DocumentStatus.APPROVED] }, 1, 0] }
          },
          rejectedCount: {
            $sum: { $cond: [{ $eq: ['$status', DocumentStatus.REJECTED] }, 1, 0] }
          },
          urgentCount: {
            $sum: { $cond: [{ $eq: ['$priority', 'urgent'] }, 1, 0] }
          },
          overdueCount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $ne: ['$dueDate', null] },
                    { $lt: ['$dueDate', new Date()] },
                    { $ne: ['$status', DocumentStatus.APPROVED] }
                  ]
                },
                1,
                0
              ]
            }
          },
        }
      }
    ]);

    return stats[0] || {
      totalDocuments: 0,
      pendingCount: 0,
      signedCount: 0,
      approvedCount: 0,
      rejectedCount: 0,
      urgentCount: 0,
      overdueCount: 0,
    };
  }

  async getDocumentsByType(): Promise<any[]> {
    return this.documentModel.aggregate([
      {
        $lookup: {
          from: 'documenttypes',
          localField: 'documentTypeId',
          foreignField: '_id',
          as: 'documentType'
        }
      },
      { $unwind: '$documentType' },
      {
        $group: {
          _id: '$documentType._id',
          name: { $first: '$documentType.name' },
          code: { $first: '$documentType.code' },
          color: { $first: '$documentType.color' },
          icon: { $first: '$documentType.icon' },
          count: { $sum: 1 },
          pendingCount: {
            $sum: { $cond: [{ $eq: ['$status', DocumentStatus.PENDING] }, 1, 0] }
          },
          approvedCount: {
            $sum: { $cond: [{ $eq: ['$status', DocumentStatus.APPROVED] }, 1, 0] }
          }
        }
      },
      { $sort: { name: 1 } }
    ]);
  }

  async getPendingDocuments(): Promise<ESignDocument[]> {
    return this.documentModel
      .find({ status: DocumentStatus.PENDING })
      .populate('uploadedBy', 'name email')
      .populate('departmentId', 'name')
      .sort({ createdAt: -1 })
      .exec();
  }

  async getSubmittedDocuments(userId: string): Promise<ESignDocument[]> {
    return this.documentModel
      .find({ status: DocumentStatus.PENDING, uploadedBy: new Types.ObjectId(userId) })
      .populate('uploadedBy', 'name email')
      .populate('departmentId', 'name')
      .sort({ createdAt: -1 })
      .exec();
  }
}