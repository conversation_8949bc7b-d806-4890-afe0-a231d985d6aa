import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Signature, SignatureDocument } from '../schema/signature.schema';
import { S3Service } from 'src/modules/common/services/s3.service';

export interface CreateSignatureDto {
  name: string;
  defaultWidth?: number;
  defaultHeight?: number;
  defaultOpacity?: number;
  userId: string;
}

@Injectable()
export class SignaturesService {
  constructor(
    @InjectModel(Signature.name) private signatureModel: Model<SignatureDocument>,
    private s3Service: S3Service,
  ) {}

  async createSignature(
    file: Express.Multer.File,
    createDto: CreateSignatureDto,
  ): Promise<SignatureDocument> {
    // Validate file type
    const allowedMimeTypes = ['image/png', 'image/jpeg', 'image/jpg'];
    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException('Only PNG and JPEG images are allowed for signatures');
    }

    // Validate file size (max 10MB)
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > 10) {
      throw new BadRequestException('Signature file size cannot exceed 10MB');
    }

    // Generate S3 key and upload
    const s3 = await this.s3Service.uploadESign(file);

    // Deactivate all existing signatures for this user
    await this.signatureModel.updateMany(
      { userId: new Types.ObjectId(createDto.userId) },
      { isActive: false }
    );

    const signature = new this.signatureModel({
      userId: new Types.ObjectId(createDto.userId),
      name: createDto.name,
      filename: file.originalname,
      key: s3.key,
      url: s3.value,
      mimeType: file.mimetype,
      isActive: true,
      isEnabled: true,
      defaultWidth: createDto.defaultWidth || 150,
      defaultHeight: createDto.defaultHeight || 75,
      defaultOpacity: createDto.defaultOpacity || 1.0,
    });

    return signature.save();
  }

  async getUserSignatures(userId: string): Promise<SignatureDocument[]> {
    return this.signatureModel
      .find({ userId: new Types.ObjectId(userId), isEnabled: true })
      .sort({ createdAt: -1 })
      .exec();
  }

  async getActiveSignature(userId: string): Promise<SignatureDocument | null> {
    return this.signatureModel
      .findOne({ 
        userId: new Types.ObjectId(userId), 
        isActive: true, 
        isEnabled: true 
      })
      .exec();
  }

  async getSignatureById(signatureId: string, userId: string): Promise<SignatureDocument> {
    const signature = await this.signatureModel.findOne({
      _id: new Types.ObjectId(signatureId),
      userId: new Types.ObjectId(userId),
      isEnabled: true
    });

    if (!signature) {
      throw new NotFoundException('Signature not found');
    }

    return signature;
  }

  async setActiveSignature(signatureId: string, userId: string): Promise<SignatureDocument> {
    // First verify the signature belongs to the user
    const signature = await this.getSignatureById(signatureId, userId);

    // Deactivate all other signatures for this user
    await this.signatureModel.updateMany(
      { 
        userId: new Types.ObjectId(userId),
        _id: { $ne: new Types.ObjectId(signatureId) }
      },
      { isActive: false }
    );

    // Activate the selected signature
    signature.isActive = true;
    return signature.save();
  }

  async updateSignature(
    signatureId: string,
    userId: string,
    updateDto: Partial<CreateSignatureDto>
  ): Promise<SignatureDocument> {
    const signature = await this.getSignatureById(signatureId, userId);

    if (updateDto.name) signature.name = updateDto.name;
    if (updateDto.defaultWidth) signature.defaultWidth = updateDto.defaultWidth;
    if (updateDto.defaultHeight) signature.defaultHeight = updateDto.defaultHeight;
    if (updateDto.defaultOpacity !== undefined) signature.defaultOpacity = updateDto.defaultOpacity;

    return signature.save();
  }

  async deleteSignature(signatureId: string, userId: string): Promise<void> {
    const signature = await this.getSignatureById(signatureId, userId);

    // Delete the file from S3
    await this.s3Service.deleteESign(signature.key);

    // Soft delete (disable) the signature
    signature.isEnabled = false;
    signature.isActive = false;
    await signature.save();
  }

  async replaceSignatureImage(
    signatureId: string,
    userId: string,
    file: Express.Multer.File,
  ): Promise<SignatureDocument> {
    const signature = await this.getSignatureById(signatureId, userId);

    // Validate file type
    const allowedMimeTypes = ['image/png', 'image/jpeg', 'image/jpg'];
    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException('Only PNG and JPEG images are allowed for signatures');
    }

    // Delete old file from S3
    await this.s3Service.deleteESign(signature.key);

    // Upload new file
    // Generate S3 key and upload
    const s3 = await this.s3Service.uploadESign(file);

    // Update signature record
    signature.filename = file.originalname;
    signature.url = s3.url;
    signature.key = s3.key;
    signature.mimeType = file.mimetype;

    return signature.save();
  }
}