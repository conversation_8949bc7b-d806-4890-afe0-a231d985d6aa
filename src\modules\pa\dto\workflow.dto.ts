import { Optional } from '@nestjs/common';
import { applyIsOptionalDecorator } from '@nestjs/mapped-types';
import { IsString, IsMongoId, IsEnum, IsOptional } from 'class-validator';

export class ApproveRejectWorkflowDto {
  @IsMongoId()
  paymentAdviceId: string;

  @IsMongoId()
  userId: string;  // The user performing the approval/rejection

  @IsEnum(['approved', 'rejected', 'cancelled'])
  status: string;  // "approved" or "rejected"

  @IsString()
  comments: string;  // Optional comments by the user

  @IsOptional()
  @IsString()
  cancelReason?: string;
}
