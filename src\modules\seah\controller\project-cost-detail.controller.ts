import { Controller, Get, Post, Put, Delete, Body, Param } from '@nestjs/common';
import { ProjectCostDetail } from '../schema/project-cost-detail.schema';
import { ProjectCostDetailService } from '../service/project-cost-detail.service';
import { CreateProjectCostDetailDto, UpdateProjectCostDetailDto } from '../dto/project-cost-detail.dto';

@Controller('project-cost-detail')
export class ProjectCostDetailController {
  constructor(private readonly projectCostDetailService: ProjectCostDetailService) {}

  @Put('/all')
  async updateAllProjectCostDetails(
    @Body() updateCostDetailsDto: ProjectCostDetail[]
  ): Promise<any> {
    return this.projectCostDetailService.updateAllProjectCostDetails(updateCostDetailsDto);
  }

  // Create a new Project Cost Detail
  @Post()
  async createProjectCostDetail(@Body() createDto: CreateProjectCostDetailDto): Promise<ProjectCostDetail> {
    return this.projectCostDetailService.createProjectCostDetail(createDto);
  }

  // Get all Project Cost Details
  @Get()
  async getAllProjectCostDetails(): Promise<ProjectCostDetail[]> {
    return this.projectCostDetailService.getAllProjectCostDetails();
  }

  // Get a Project Cost Detail by Title
  @Get(':title')
  async getProjectCostDetailByTitle(@Param('title') title: string): Promise<ProjectCostDetail> {
    return this.projectCostDetailService.getProjectCostDetailByTitle(title);
  }

  // Update a Project Cost Detail by Title
  @Put(':title')
  async updateProjectCostDetailByTitle(
    @Param('title') title: string,
    @Body() updateDto: UpdateProjectCostDetailDto,
  ): Promise<ProjectCostDetail> {
    return this.projectCostDetailService.updateProjectCostDetailByTitle(title, updateDto);
  }

  // Delete a Project Cost Detail by Title
  @Delete(':title')
  async deleteProjectCostDetailByTitle(@Param('title') title: string): Promise<ProjectCostDetail> {
    return this.projectCostDetailService.deleteProjectCostDetailByTitle(title);
  }
}
