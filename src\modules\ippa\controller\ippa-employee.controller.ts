import {
  Controller,
  Post,
  Get,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  BadRequestException,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { IppaEmployeeService } from '../service/ippa-employee.service';
import { CreateIPPADto, SaveDraftDto } from '../dto/ippa-employee.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { S3Service } from 'src/modules/common/services/s3.service';
import { IPPAStatus } from '../schema/ippa-submission.schema';
import { SecurityService } from 'src/modules/user/service/security.service';

@ApiTags('IPPA Employee')
@ApiBearerAuth()
@Controller('ippa/employee')
export class IppaEmployeeController {
  constructor(
    private readonly ippaEmployeeService: IppaEmployeeService,
    private readonly s3service: S3Service,
    private readonly securityService: SecurityService
  ) {}

  @Post('submit')
  @ApiOperation({ summary: 'Submit IPPA form' })
  @ApiResponse({ status: 201, description: 'IPPA submitted successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async submitIPPA(
    // @Body() createIPPADto: CreateIPPADto
    @Body() encryptedData: any
  ) {
    try {
      //   const userEmail = req.user?.email; // From JWT token
      const submission =
        await this.ippaEmployeeService.submitIPPA(encryptedData);

      const responseData = {
        success: true,
        message: 'IPPA submitted successfully',
        data: {
          ippaId: submission.ippaId,
          submissionId: submission._id,
          status: submission.status,
          approvalWorkflow: submission.approvalWorkflow,
        }
      };

      return { data: this.securityService.encrypt(responseData) };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Put('draft')
  @ApiOperation({ summary: 'Save IPPA as draft' })
  @ApiResponse({ status: 200, description: 'Draft saved successfully' })
  async saveDraft(@Body() saveDraftDto: SaveDraftDto, @Request() req: any) {
    try {
      const userEmail = req.user?.email;
      const draft = await this.ippaEmployeeService.saveDraft(
        userEmail,
        saveDraftDto,
      );

      return {
        success: true,
        message: 'Draft saved successfully',
        data: {
          submissionId: draft._id,
          lastModified: draft.lastModifiedAt,
        },
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('own-submission')
    async ownSubmission(
     @Body() encryptedData: any
  ) {
    try {

       const requestBody = this.securityService.decrypt(encryptedData.data);

      const email = requestBody.email;

      if (!email) {
        throw new BadRequestException('Email is required');
      }

      // Parse pagination parameters
      const pageNumber = parseInt(requestBody.page) || 1;
      const pageLimit = parseInt(requestBody.limit) || 10;

      // Validate pagination
      if (pageNumber < 1) {
        throw new BadRequestException('Page number must be greater than 0');
      }
      if (pageLimit < 1 || pageLimit > 1000) {
        throw new BadRequestException('Limit must be between 1 and 100');
      }

      // Validate status if provided
      if (requestBody.status && !Object.values(IPPAStatus).includes(requestBody.status as IPPAStatus)) {
        throw new BadRequestException(
          `Invalid status. Valid values are: ${Object.values(IPPAStatus).join(', ')}`,
        );
      }

      let result;

        result = await this.ippaEmployeeService.ownSubmission(
          email,
          pageNumber,
          pageLimit,
          requestBody.status as IPPAStatus
        );

      // Apply financial year filter if provided (client-side filtering)
      let filteredSubmissions = result.submissions;
      let filteredCount = result.totalCount;

      if (requestBody.financialYear) {
        filteredSubmissions = result.submissions.filter(
          (submission) => submission.financialYear === requestBody.financialYear,
        );
        filteredCount = filteredSubmissions.length;
      }

      // Calculate pagination info
      const totalPages = Math.ceil(filteredCount / pageLimit);

      const responseData = {
        success: true,
        message: 'Submissions retrieved successfully',
        data: {
          statusInfo: result.statusInfo,
          submissions: filteredSubmissions,
          totalCount: filteredCount,
          currentPage: pageNumber,
          totalPages: totalPages,
          pageSize: pageLimit,
          hasNextPage: pageNumber < totalPages,
          hasPreviousPage: pageNumber > 1,
        },
      };
      return { data: this.securityService.encrypt(responseData) };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }


  @Post('my-submissions')
  @ApiOperation({ summary: 'Get my IPPA submissions with status info' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Filter by status',
  })
  @ApiQuery({
    name: 'financialYear',
    required: false,
    type: String,
    description: 'Filter by financial year',
  })
  @ApiQuery({
    name: 'email',
    required: true,
    type: String,
    description: 'Employee email',
  })
  @ApiResponse({
    status: 200,
    description: 'Submissions retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            statusInfo: {
              type: 'object',
              properties: {
                totalSubmissions: { type: 'number' },
                approved: { type: 'number' },
                pendingReview: { type: 'number' },
                completed: { type: 'number' },
                submitted: { type: 'number' },
                draft: { type: 'number' },
              },
            },
            submissions: {
              type: 'array',
              items: { type: 'object' },
            },
            totalCount: { type: 'number' },
            currentPage: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  async getMySubmissions(
    // @Request() req: any,
    // @Query('page') page: string = '1',
    // @Query('limit') limit: string = '10',
    // @Query('status') status?: string,
    // @Query('financialYear') financialYear?: string,
    // @Query('email') userEmail?: string,
     @Body() encryptedData: any
  ) {
    try {

       const requestBody = this.securityService.decrypt(encryptedData.data);
    //  console.log("request body ", requestBody)
      // Get email from request user or query parameter
      const email = requestBody.email;

      if (!email) {
        throw new BadRequestException('Email is required');
      }

      // Parse pagination parameters
      const pageNumber = parseInt(requestBody.page) || 1;
      const pageLimit = parseInt(requestBody.limit) || 10;

      // Validate pagination
      if (pageNumber < 1) {
        throw new BadRequestException('Page number must be greater than 0');
      }
      if (pageLimit < 1 || pageLimit > 1000) {
        throw new BadRequestException('Limit must be between 1 and 100');
      }

      // Validate status if provided
      if (requestBody.status && !Object.values(IPPAStatus).includes(requestBody.status as IPPAStatus)) {
        throw new BadRequestException(
          `Invalid status. Valid values are: ${Object.values(IPPAStatus).join(', ')}`,
        );
      }

      let result;

        result = await this.ippaEmployeeService.getMySubmissions(
          email,
          pageNumber,
          pageLimit,
          requestBody.status as IPPAStatus
        );

      // Apply financial year filter if provided (client-side filtering)
      let filteredSubmissions = result.submissions;
      let filteredCount = result.totalCount;

      if (requestBody.financialYear) {
        filteredSubmissions = result.submissions.filter(
          (submission) => submission.financialYear === requestBody.financialYear,
        );
        filteredCount = filteredSubmissions.length;
      }

      // Calculate pagination info
      const totalPages = Math.ceil(filteredCount / pageLimit);

      const responseData = {
        success: true,
        message: 'Submissions retrieved successfully',
        data: {
          statusInfo: result.statusInfo,
          submissions: filteredSubmissions,
          totalCount: filteredCount,
          currentPage: pageNumber,
          totalPages: totalPages,
          pageSize: pageLimit,
          hasNextPage: pageNumber < totalPages,
          hasPreviousPage: pageNumber > 1,
        },
      };
      return { data: this.securityService.encrypt(responseData) };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  // ==================== ADDITIONAL CONTROLLER METHOD ====================

  // @Get('dashboard')
  // @ApiOperation({ summary: 'Get employee dashboard data' })
  // @ApiQuery({
  //   name: 'email',
  //   required: true,
  //   type: String,
  //   description: 'Employee email',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Dashboard data retrieved successfully',
  // })
  // async getEmployeeDashboard(
  //   @Request() req: any,
  //   @Query('email') userEmail?: string,
  // ) {
  //   try {
  //     // Get email from request user or query parameter
  //     const email = userEmail || req.user?.email;

  //     if (!email) {
  //       throw new BadRequestException('Email is required');
  //     }

  //     const dashboardData =
  //       await this.ippaEmployeeService.getEmployeeDashboard(email);

  //     return {
  //       success: true,
  //       message: 'Dashboard data retrieved successfully',
  //       data: dashboardData,
  //     };
  //   } catch (error) {
  //     throw new BadRequestException(error.message);
  //   }
  // }
  // @Get('my-submissions')
  // @ApiOperation({ summary: 'Get my IPPA submissions' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Submissions retrieved successfully',
  // })
  // async getMySubmissions(
  //   @Request() req: any,
  //   @Query('financialYear') financialYear: string,
  //   @Query('email') userEmail: string,
  // ) {
  //   try {
  //     // const userEmail = req.user?.email;
  //     const submissions =
  //       await this.ippaEmployeeService.getMySubmissions(userEmail);

  //     let filteredSubmissions = submissions;
  //     // if (financialYear) {
  //     //   filteredSubmissions = submissions.filter(
  //     //     (s) => s.financialYear === financialYear,
  //     //   );
  //     // }

  //     return {
  //       success: true,
  //       message: 'Submissions retrieved successfully',
  //       data: {
  //         submissions: filteredSubmissions,
  //         total: filteredSubmissions.totalCount,
  //       },
  //     };
  //   } catch (error) {
  //     throw new BadRequestException(error.message);
  //   }
  // }

  @Get(':id')
  @ApiOperation({ summary: 'Get specific IPPA submission' })
  @ApiResponse({
    status: 200,
    description: 'Submission retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Submission not found' })
  async getSubmission(@Param('id') id: string, @Request() req: any) {
    try {
      const userEmail = req.user?.email;
      const submission = await this.ippaEmployeeService.getSubmission(
        id,
        userEmail,
      );

      return {
        success: true,
        message: 'Submission retrieved successfully',
        data: submission,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('attachment')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    return await this.s3service.uploadIppaAttachment(file);
  }
}
