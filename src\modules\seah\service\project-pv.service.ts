import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as XLSX from 'xlsx';
import { ProjectPV, ProjectPVDocument } from '../schema/project-pv.schema';
import { ProjectPO, ProjectPODocument } from '../schema/project-po.schema';
import { SageReportRPAService } from './sage-report-rpa.service';

@Injectable()
export class ProjectPVService {
  constructor(
    @InjectModel(ProjectPV.name) private paymentModel: Model<ProjectPVDocument>,
    @InjectModel(ProjectPO.name) private poModel: Model<ProjectPODocument>,
    private readonly sageReportRPAService: SageReportRPAService,
  ) { }

  async importPaymentsFromExcel(buffer: Buffer): Promise<string> {
    // throw new ForbiddenException('Failed to process PV.');

    // Parse the uploaded Excel file
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const sheet = workbook.Sheets[workbook.SheetNames[0]];

    // Read data from the Excel file starting from row 4 and include headers from row 3
    const excelData = XLSX.utils.sheet_to_json(sheet, { header: 1, range: 1 });

    // Get the headers from the 4th row (index 0 of excelData)
    const headers: any = excelData[0];
    const rows: any[] = excelData.slice(1);

    // console.log("headers: ", headers);
    // console.log("rows: ", rows);
    // Store new records to be inserted
    const newPayments: any[] = [];

    for (const row of rows) {
      if (this.isRowEmpty(row)) {
        // console.log("Skipping empty row:", row);
        continue;
      }
      const paymentData = {};

      // Map each header to the corresponding value in the row
      headers.forEach((header, index) => {
        paymentData[header] = row[index];
      });

      // Log each row's `PV No.` to debug the issue
      // console.log("PV No. Value: ", paymentData['PV No.']);

      // console.log("data: ", paymentData);


      // Check if this payment already exists in the database using a unique key (e.g., `PV No.` or `Reference No.`)
      // const existingPayment = await this.paymentModel.findOne({ PVNo: paymentData['PV No.'] }).exec();
      // console.log("Existing : ", existingPayment);
      let itemType = '';
      let isExcluded = false;
      if (row) {

        const bpCode = paymentData['BP'] || '';

        if (bpCode.includes('EMP') || bpCode.includes('CT') || paymentData['Description'].toLowerCase().includes('consultant') || paymentData['Description'].toLowerCase().includes('consultancy') || bpCode == 'BP000606') {
          itemType = 'Consultant';
        } else if (bpCode.includes('CT')) {
          itemType = 'Interco';
        } else {
          itemType = 'PO';
        }

        if (itemType == 'Consultant' || itemType == 'Interco' || bpCode == 'S-TRA001367' || bpCode == 'BP000606' || paymentData['Description'].toLowerCase().includes('consultant') || paymentData['Description'].toLowerCase().includes('consultancy')) {
          isExcluded = true;
        } else {
          isExcluded = false;
        }

        newPayments.push({
          PVNo: paymentData['PV No.'],
          date: paymentData['Date'],
          BP: paymentData['BP'],
          BPName: paymentData['BP Name'],
          country: paymentData['Country'],
          controlAccount: paymentData['Control A/C'],
          bank: paymentData['Bank'],
          description: paymentData['Description'],
          referenceNo: paymentData['Reference No.'],
          invoiceNo: paymentData['Invoice No.'],
          department: paymentData['Department'],
          project: paymentData['Project'],
          currency: paymentData['Currency'],
          docAmount: paymentData['Doc Amount'],
          rate: paymentData['Rate'],
          converted: paymentData['Converted'],
          status: paymentData['Status'],
          PIC: paymentData['PIC'],
          PONumber: paymentData['PO Number'],
          isExcluded: isExcluded,
          remark: '',
          type: itemType
        });
      }
      // return `${newPayments.length} new payments imported successfully`;
      // if (existingPayment == null || undefined) { // Ensure PVNo is valid
      // Manually map the values to the MongoDB schema fields
      //}
    }
    await this.paymentModel.insertMany(newPayments);

    // Calculate total amount
    const totalAmount = newPayments.reduce((sum, payment) => sum + (payment.converted || 0), 0);

    // Log the RPA activity
    await this.sageReportRPAService.logRPAActivity(
      'PV_IMPORT',
      newPayments.length.toString(),
      totalAmount,
      true,
      'Successfully imported payments from Excel'
    );

    return `${newPayments.length} new payments imported successfully`;

    // Insert only the new records into the database
    // if (newPayments.length > 0) {
    // }
  }

  // Example NestJS Mongo Service method
  async getPaymentsGroupedByBP(): Promise<any> {
    return this.paymentModel.aggregate([
      {
        $group: {
          _id: "$BPName",
          count: { $sum: 1 },
          totalAmount: { $sum: "$docAmount" }
        }
      },
      {
        $sort: { _id: 1 }
      },
      {
        $project: {
          bpName: "$_id",
          count: 1,
          totalAmount: 1,
          _id: 0
        }
      }
    ]);
  }

  // async getOutstandingBalances(): Promise<any[]> {
  //   const pos = await this.poModel.aggregate([
  //     {
  //       $group: {
  //         _id: "$BPName",
  //         totalPOAmount: { $sum: "$TotalRM" }
  //       }
  //     }
  //   ]);

  //   const pvs = await this.paymentModel.aggregate([
  //     {
  //       $group: {
  //         _id: "$BPName",
  //         totalPaidAmount: { $sum: "$converted" }
  //       }
  //     }
  //   ]);

  //   // Merge the PO and PV arrays
  //   const map = new Map<string, { po?: number; paid?: number }>();

  //   for (const { _id, totalPOAmount } of pos) {
  //     if (_id) map.set(_id, { po: totalPOAmount });
  //   }

  //   for (const { _id, totalPaidAmount } of pvs) {
  //     if (_id) {
  //       const existing = map.get(_id) || {};
  //       map.set(_id, { ...existing, paid: totalPaidAmount });
  //     }
  //   }

  //   // Final result: compute outstanding and sort by bpName
  //   const result = Array.from(map.entries())
  //     .map(([bpName, { po = 0, paid = 0 }]) => ({
  //       bpName,
  //       totalPO: po,
  //       totalPaid: paid,
  //       outstanding: po - paid
  //     }))
  //     .filter(r => r.outstanding !== 0)
  //     .sort((a, b) => a.bpName.localeCompare(b.bpName)); // sort alphabetically

  //   return result;
  // }

  // async getOutstandingBalances(): Promise<any> {
  //   const pos = await this.poModel.aggregate([
  //     {
  //       $group: {
  //         _id: "$BPName",
  //         totalPOAmount: { $sum: "$TotalRM" }
  //       }
  //     }
  //   ]);

  //   const pvs = await this.paymentModel.aggregate([
  //     {
  //       $group: {
  //         _id: "$BPName",
  //         totalPaidAmount: { $sum: "$converted" }
  //       }
  //     }
  //   ]);

  //   // Merge PO and PV totals
  //   const map = new Map<string, { po?: number; paid?: number }>();

  //   for (const { _id, totalPOAmount } of pos) {
  //     if (_id) map.set(_id, { po: totalPOAmount });
  //   }

  //   for (const { _id, totalPaidAmount } of pvs) {
  //     if (_id) {
  //       const existing = map.get(_id) || {};
  //       map.set(_id, { ...existing, paid: totalPaidAmount });
  //     }
  //   }

  //   // Build final result
  //   const bpNames = Array.from(map.keys());
  //   const result: any[] = [];

  //   for (const bpName of bpNames) {
  //     const { po = 0, paid = 0 } = map.get(bpName) || {};

  //     if (po !== paid) {
  //       // Pull PO and PV detail arrays
  //       const poDetails = await this.poModel.find({ BPName: bpName }).lean();
  //       const paymentDetails = await this.paymentModel.find({ BPName: bpName }).lean();

  //       result.push({
  //         bpName,
  //         totalPO: po,
  //         totalPaid: paid,
  //         outstanding: po - paid,
  //         poDetails,
  //         paymentDetails
  //       });
  //     }
  //   }

  //   // Sort alphabetically
  //   result.sort((a, b) => a.bpName.localeCompare(b.bpName));
  //   return { success: true, data: result };
  // }

  // async getOutstandingBalances(): Promise<any> {
  //   const pos = await this.poModel.aggregate([
  //     {
  //       $group: {
  //         _id: "$BPName",
  //         totalPOAmount: { $sum: "$TotalRM" }
  //       }
  //     }
  //   ]);

  //   const pvs = await this.paymentModel.aggregate([
  //     {
  //       $group: {
  //         _id: "$BPName",
  //         totalPaidAmount: { $sum: "$converted" }
  //       }
  //     }
  //   ]);

  //   // Merge PO and PV totals
  //   const map = new Map<string, { po?: number; paid?: number }>();

  //   for (const { _id, totalPOAmount } of pos) {
  //     if (_id) map.set(_id, { po: totalPOAmount });
  //   }

  //   for (const { _id, totalPaidAmount } of pvs) {
  //     if (_id) {
  //       const existing = map.get(_id) || {};
  //       map.set(_id, { ...existing, paid: totalPaidAmount });
  //     }
  //   }

  //   // Build final result
  //   const bpNames = Array.from(map.keys());
  //   const result: any[] = [];

  //   let totalCost = 0;
  //   let totalPayment = 0;
  //   let totalOutstanding = 0;

  //   for (const bpName of bpNames) {
  //     const { po = 0, paid = 0 } = map.get(bpName) || {};
  //     const outstanding = po - paid;

  //     // Update summary totals
  //     totalCost += (paid === 0) ? po : po;
  //     totalPayment += paid;
  //     totalOutstanding += (outstanding > 0) ? outstanding : 0;

  //     if (po !== paid) {
  //       // Pull PO and PV detail arrays
  //       const poDetails = await this.poModel.find({ BPName: bpName }).lean();
  //       const paymentDetails = await this.paymentModel.find({ BPName: bpName }).lean();

  //       result.push({
  //         bpName,
  //         totalPO: po,
  //         totalPaid: paid,
  //         outstanding,
  //         poDetails,
  //         paymentDetails
  //       });
  //     }
  //   }

  //   // Sort alphabetically
  //   result.sort((a, b) => a.bpName.localeCompare(b.bpName));

  //   return { 
  //     success: true, 
  //     data: result,
  //     totalCost,
  //     totalPayment,
  //     totalOutstanding
  //   };
  // }

  // GOOD ONE
  // async getOutstandingBalances(): Promise<any> {
  //   // Only include POs where isExcluded is false
  //   const pos = await this.poModel.aggregate([
  //     {
  //       $match: { isExcluded: false }
  //     },
  //     {
  //       $group: {
  //         _id: "$BPName",
  //         totalPOAmount: { $sum: "$TotalRM" }
  //       }
  //     }
  //   ]);

  //   // Only include payments where isExcluded is false
  //   const pvs = await this.paymentModel.aggregate([
  //     {
  //       $match: { isExcluded: false }
  //     },
  //     {
  //       $group: {
  //         _id: "$BPName",
  //         totalPaidAmount: { $sum: "$converted" }
  //       }
  //     }
  //   ]);

  //   // Merge PO and PV totals
  //   const map = new Map<string, { po?: number; paid?: number }>();

  //   for (const { _id, totalPOAmount } of pos) {
  //     if (_id) map.set(_id, { po: totalPOAmount });
  //   }

  //   for (const { _id, totalPaidAmount } of pvs) {
  //     if (_id) {
  //       const existing = map.get(_id) || {};
  //       map.set(_id, { ...existing, paid: totalPaidAmount });
  //     }
  //   }

  //   // Build final result
  //   const bpNames = Array.from(map.keys());
  //   const result: any[] = [];

  //   let totalCost = 0;
  //   let totalPayment = 0;
  //   let totalOutstanding = 0;

  //   for (const bpName of bpNames) {
  //     const { po = 0, paid = 0 } = map.get(bpName) || {};
  //     const outstanding = po - paid;

  //     // Update summary totals
  //     totalCost += (paid === 0) ? po : po;
  //     totalPayment += paid;
  //     totalOutstanding += (outstanding > 0) ? outstanding : 0;

  //     if (po !== paid) {
  //       // Pull PO and PV detail arrays - only include non-excluded records
  //       const poDetails = await this.poModel.find({ 
  //         BPName: bpName,
  //         isExcluded: false 
  //       }).lean();

  //       const paymentDetails = await this.paymentModel.find({ 
  //         BPName: bpName,
  //         isExcluded: false 
  //       }).lean();

  //       result.push({
  //         bpName,
  //         totalPO: po,
  //         totalPaid: paid,
  //         outstanding,
  //         poDetails,
  //         paymentDetails
  //       });
  //     }
  //   }

  //   // Sort alphabetically
  //   result.sort((a, b) => a.bpName.localeCompare(b.bpName));

  //   return {
  //     success: true,
  //     data: result,
  //     totalCost,
  //     totalPayment,
  //     totalOutstanding
  //   };
  // }

  //22/04/2025 9:48pm
  // async getOutstandingBalances(): Promise<any> {
  //   // For POs, include BPCode in the group by 
  //   const pos = await this.poModel.aggregate([
  //     {
  //       $match: { isExcluded: false }
  //     },
  //     {
  //       $group: {
  //         _id: {
  //           name: "$BPName",
  //           code: "$BPCode"
  //         },
  //         totalPOAmount: { $sum: "$TotalRM" }
  //       }
  //     }
  //   ]);

  //   // For payments, include BP in the group by
  //   const pvs = await this.paymentModel.aggregate([
  //     {
  //       $match: { isExcluded: false }
  //     },
  //     {
  //       $group: {
  //         _id: {
  //           name: "$BPName",
  //           code: "$BP"
  //         },
  //         totalPaidAmount: { $sum: "$converted" }
  //       }
  //     }
  //   ]);

  //   // Track BP names and codes together
  //   const allBPs = new Map<string, string>();

  //   for (const { _id } of pos) {
  //     if (_id && _id.name) {
  //       allBPs.set(_id.name, _id.code || '');
  //     }
  //   }

  //   for (const { _id } of pvs) {
  //     if (_id && _id.name) {
  //       // If we haven't seen this BP yet or it doesn't have a code, add it
  //       if (!allBPs.has(_id.name) || !allBPs.get(_id.name)) {
  //         allBPs.set(_id.name, _id.code || '');
  //       }
  //     }
  //   }

  //   // Merge PO and PV totals
  //   const map = new Map<string, { po?: number; paid?: number }>();

  //   for (const { _id, totalPOAmount } of pos) {
  //     if (_id && _id.name) {
  //       map.set(_id.name, { po: totalPOAmount, paid: 0 });
  //     }
  //   }

  //   for (const { _id, totalPaidAmount } of pvs) {
  //     if (_id && _id.name) {
  //       const existing = map.get(_id.name) || { po: 0 };
  //       map.set(_id.name, { ...existing, paid: totalPaidAmount });
  //     }
  //   }

  //   // Build final result
  //   const result: any[] = [];

  //   let totalCost = 0;
  //   let totalPayment = 0;
  //   let totalOutstanding = 0;

  //   // Use all unique business partners
  //   for (const [bpName, bpCode] of allBPs.entries()) {
  //     const { po = 0, paid = 0 } = map.get(bpName) || { po: 0, paid: 0 };
  //     const outstanding = po - paid;

  //     // Update summary totals
  //     totalCost += po;
  //     totalPayment += paid;
  //     totalOutstanding += (outstanding > 0) ? outstanding : 0;

  //     // Include all business partners
  //     const poDetails = await this.poModel.find({
  //       BPName: bpName,
  //       isExcluded: false
  //     }).lean();

  //     const paymentDetails = await this.paymentModel.find({
  //       BPName: bpName,
  //       isExcluded: false
  //     }).lean();

  //     result.push({
  //       bpName,
  //       bpCode,
  //       totalPO: po,
  //       totalPaid: paid,
  //       outstanding,
  //       poDetails,
  //       paymentDetails
  //     });
  //   }

  //   // Sort alphabetically
  //   result.sort((a, b) => a.bpName.localeCompare(b.bpName));

  //   return {
  //     success: true,
  //     data: result,
  //     totalCost,
  //     totalPayment,
  //     totalOutstanding
  //   };
  // }
  //22/04/2025 12:00pm
  // async getOutstandingBalances(): Promise<any> {
  //   // For POs, include BPCode and type in the group by and calculate discount amounts
  //   const pos = await this.poModel.aggregate([
  //     {
  //       $match: { isExcluded: false }
  //     },
  //     {
  //       $group: {
  //         _id: {
  //           name: "$BPName",
  //           code: "$BPCode",
  //           type: "$type" // Include type for PO/VO breakdown
  //         },
  //         totalPOAmount: { $sum: "$TotalRM" },
  //         totalDiscountAmount: { $sum: "$discountAmount" }
  //       }
  //     }
  //   ]);

  //   // For payments, include BP in the group by
  //   const pvs = await this.paymentModel.aggregate([
  //     {
  //       $match: { isExcluded: false }
  //     },
  //     {
  //       $group: {
  //         _id: {
  //           name: "$BPName",
  //           code: "$BP"
  //         },
  //         totalPaidAmount: { $sum: "$converted" }
  //       }
  //     }
  //   ]);

  //   // Track BP names and codes together
  //   const allBPs = new Map<string, string>();

  //   for (const { _id } of pos) {
  //     if (_id && _id.name) {
  //       allBPs.set(_id.name, _id.code || '');
  //     }
  //   }

  //   for (const { _id } of pvs) {
  //     if (_id && _id.name) {
  //       // If we haven't seen this BP yet or it doesn't have a code, add it
  //       if (!allBPs.has(_id.name) || !allBPs.get(_id.name)) {
  //         allBPs.set(_id.name, _id.code || '');
  //       }
  //     }
  //   }

  //   // Merge PO and PV totals, now with type differentiation
  //   const map = new Map<string, { 
  //     po?: number; 
  //     vo?: number; 
  //     paid?: number; 
  //     discount?: number;
  //     poType?: { [type: string]: number };
  //   }>();

  //   for (const { _id, totalPOAmount, totalDiscountAmount } of pos) {
  //     if (_id && _id.name) {
  //       const existing = map.get(_id.name) || { 
  //         po: 0, 
  //         vo: 0, 
  //         paid: 0, 
  //         discount: 0,
  //         poType: {}
  //       };

  //       // Update based on type
  //       if (_id.type === "PO") {
  //         existing.po = (existing.po || 0) + totalPOAmount;
  //       } else if (_id.type === "VO") {
  //         existing.vo = (existing.vo || 0) + totalPOAmount;
  //       }

  //       // Update poType tracking
  //       existing.poType = existing.poType || {};
  //       existing.poType[_id.type || "UNKNOWN"] = (existing.poType[_id.type || "UNKNOWN"] || 0) + totalPOAmount;

  //       // Update discount
  //       existing.discount = (existing.discount || 0) + (totalDiscountAmount || 0);

  //       map.set(_id.name, existing);
  //     }
  //   }

  //   for (const { _id, totalPaidAmount } of pvs) {
  //     if (_id && _id.name) {
  //       const existing = map.get(_id.name) || { po: 0, vo: 0, discount: 0, poType: {} };
  //       existing.paid = (existing.paid || 0) + totalPaidAmount;
  //       map.set(_id.name, existing);
  //     }
  //   }

  //   // Build final result
  //   const result: any[] = [];

  //   let totalCost = 0;
  //   let totalCostPO = 0;
  //   let totalCostVO = 0;
  //   let totalPayment = 0;
  //   let totalOutstanding = 0;
  //   let totalDiscountAmount = 0;

  //   // Use all unique business partners
  //   for (const [bpName, bpCode] of allBPs.entries()) {
  //     const { po = 0, vo = 0, paid = 0, discount = 0, poType = {} } = 
  //       map.get(bpName) || { po: 0, vo: 0, paid: 0, discount: 0, poType: {} };

  //     const totalForBP = po + vo;
  //     const outstanding = totalForBP - discount - paid;

  //     // Update summary totals
  //     totalCost += totalForBP;
  //     totalCostPO += po;
  //     totalCostVO += vo;
  //     totalPayment += paid;
  //     totalOutstanding += (outstanding > 0) ? outstanding : 0;
  //     totalDiscountAmount += discount;

  //     // Include all business partners
  //     const poDetails = await this.poModel.find({
  //       BPName: bpName,
  //       isExcluded: false
  //     }).lean();

  //     const paymentDetails = await this.paymentModel.find({
  //       BPName: bpName,
  //       isExcluded: false
  //     }).lean();

  //     // Get discount remarks for this BP
  //     const discountRemarks = poDetails
  //       .filter(po => po.discountAmount && po.discountAmount > 0 && po.discountRemark)
  //       .map(po => po.discountRemark);

  //     // Get unique remarks only
  //     const uniqueDiscountRemarks = [...new Set(discountRemarks)];

  //     result.push({
  //       bpName,
  //       bpCode,
  //       totalPO: po,
  //       totalVO: vo,
  //       totalCost: totalForBP,
  //       totalPaid: paid,
  //       outstanding,
  //       totalDiscount: discount,
  //       discountRemarks: uniqueDiscountRemarks,
  //       poBreakdown: poType, // Include breakdown by PO type
  //       poDetails,
  //       paymentDetails
  //     });
  //   }

  //   // Sort alphabetically
  //   result.sort((a, b) => a.bpName.localeCompare(b.bpName));

  //   return {
  //     success: true,
  //     data: result,
  //     totalCost,
  //     totalCostPO,
  //     totalCostVO,
  //     totalPayment,
  //     totalOutstanding,
  //     totalDiscountAmount
  //   };
  // }

  // GOOD ONE
  // async getBusinessPartnerByCode(bpCode: string): Promise<any> {
  //   // Validate input
  //   if (!bpCode) {
  //     return {
  //       success: false,
  //       message: 'Business partner code is required',
  //     };
  //   }

  //   try {
  //     // Find PO details for the specific BP code
  //     const poDetails = await this.poModel.find({
  //       BPCode: bpCode
  //     }).lean();

  //     // Find payment details for the specific BP code
  //     // Note: In ProjectPV schema it's named "BP" not "BPCode"
  //     const paymentDetails = await this.paymentModel.find({
  //       BP: bpCode
  //     }).lean();

  //     // Calculate totals
  //     const totalPO = poDetails.reduce((sum, po) => sum + (po.TotalRM || 0), 0);
  //     const totalPaid = paymentDetails.reduce((sum, pv) => sum + (pv.converted || 0), 0);
  //     const outstanding = totalPO - totalPaid;

  //     // If no BP found with this code
  //     if (poDetails.length === 0 && paymentDetails.length === 0) {
  //       return {
  //         success: false,
  //         message: 'No business partner found with the provided code',
  //       };
  //     }

  //     // Get BP name from the details (using the first record found)
  //     const bpName = poDetails.length > 0 ? poDetails[0].BPName :
  //       (paymentDetails.length > 0 ? paymentDetails[0].BPName : '');

  //     return {
  //       success: true,
  //       data: {
  //         bpCode,
  //         bpName,
  //         totalPO,
  //         totalPaid,
  //         outstanding,
  //         poDetails,
  //         paymentDetails,
  //       }
  //     };
  //   } catch (error) {
  //     return {
  //       success: false,
  //       message: 'Error searching for business partner by code',
  //       error: error.message,
  //     };
  //   }
  // }

  //22/04/2025 12:00pm
  // async getBusinessPartnerByCode(bpCode: string): Promise<any> {
  //   // Validate input
  //   if (!bpCode) {
  //     return {
  //       success: false,
  //       message: 'Business partner code is required',
  //     };
  //   }

  //   try {
  //     // Find PO details for the specific BP code
  //     const poDetails = await this.poModel.find({
  //       BPCode: bpCode
  //     }).lean();

  //     // Find payment details for the specific BP code
  //     // Note: In ProjectPV schema it's named "BP" not "BPCode"
  //     const paymentDetails = await this.paymentModel.find({
  //       BP: bpCode
  //     }).lean();

  //     // Calculate totals with PO/VO breakdown
  //     let totalPO = 0;
  //     let totalVO = 0;
  //     let totalDiscount = 0;

  //     // Group POs by type and calculate totals
  //     poDetails.forEach(po => {
  //       if (po.type === "PO") {
  //         totalPO += (po.TotalRM || 0);
  //       } else if (po.type === "VO") {
  //         totalVO += (po.TotalRM || 0);
  //       }

  //       // Add discount amount
  //       totalDiscount += (po.discountAmount || 0);
  //     });

  //     const totalCost = totalPO + totalVO;
  //     const totalPaid = paymentDetails.reduce((sum, pv) => sum + (pv.converted || 0), 0);
  //     const outstanding = totalCost - totalPaid;

  //     // If no BP found with this code
  //     if (poDetails.length === 0 && paymentDetails.length === 0) {
  //       return {
  //         success: false,
  //         message: 'No business partner found with the provided code',
  //       };
  //     }

  //     // Get BP name from the details (using the first record found)
  //     const bpName = poDetails.length > 0 ? poDetails[0].BPName :
  //       (paymentDetails.length > 0 ? paymentDetails[0].BPName : '');

  //     // Get discount remarks
  //     const discountRemarks = poDetails
  //       .filter(po => po.discountAmount && po.discountAmount > 0 && po.discountRemark)
  //       .map(po => po.discountRemark);

  //     // Get unique remarks only
  //     const uniqueDiscountRemarks = [...new Set(discountRemarks)];

  //     // Create PO breakdown by type for additional details
  //     const poBreakdown = poDetails.reduce((acc, po) => {
  //       const type = po.type || "UNKNOWN";
  //       acc[type] = (acc[type] || 0) + (po.TotalRM || 0);
  //       return acc;
  //     }, {});

  //     return {
  //       success: true,
  //       data: {
  //         bpCode,
  //         bpName,
  //         totalPO,
  //         totalVO,
  //         totalCost,
  //         totalPaid,
  //         outstanding,
  //         totalDiscount,
  //         discountRemarks: uniqueDiscountRemarks,
  //         poBreakdown,
  //         poDetails,
  //         paymentDetails,
  //       }
  //     };
  //   } catch (error) {
  //     return {
  //       success: false,
  //       message: 'Error searching for business partner by code',
  //       error: error.message,
  //     };
  //   }
  // }

  //22/04/2025 16:14pm
  // async getOutstandingBalances(): Promise<any> {
  //   // For POs, include BPCode and type in the group by and calculate discount and default amounts
  //   const pos = await this.poModel.aggregate([
  //     {
  //       $match: { isExcluded: false }
  //     },
  //     {
  //       $group: {
  //         _id: {
  //           name: "$BPName",
  //           code: "$BPCode",
  //           type: "$type" // Include type for PO/VO breakdown
  //         },
  //         totalPOAmount: { $sum: "$TotalRM" },
  //         totalDiscountAmount: { $sum: "$discountAmount" },
  //         totalDefaultAmount: { 
  //           $sum: { 
  //             $cond: [
  //               { $eq: ["$isDefault", true] }, 
  //               { $ifNull: ["$defaultAmount", 0] }, 
  //               0
  //             ] 
  //           }
  //         }
  //       }
  //     }
  //   ]);

  //   // For payments, include BP in the group by
  //   const pvs = await this.paymentModel.aggregate([
  //     {
  //       $match: { isExcluded: false }
  //     },
  //     {
  //       $group: {
  //         _id: {
  //           name: "$BPName",
  //           code: "$BP"
  //         },
  //         totalPaidAmount: { $sum: "$converted" }
  //       }
  //     }
  //   ]);

  //   // Track BP names and codes together
  //   const allBPs = new Map<string, string>();

  //   for (const { _id } of pos) {
  //     if (_id && _id.name) {
  //       allBPs.set(_id.name, _id.code || '');
  //     }
  //   }

  //   for (const { _id } of pvs) {
  //     if (_id && _id.name) {
  //       // If we haven't seen this BP yet or it doesn't have a code, add it
  //       if (!allBPs.has(_id.name) || !allBPs.get(_id.name)) {
  //         allBPs.set(_id.name, _id.code || '');
  //       }
  //     }
  //   }

  //   // Merge PO and PV totals, now with type differentiation and default amount
  //   const map = new Map<string, { 
  //     po?: number; 
  //     vo?: number; 
  //     paid?: number; 
  //     discount?: number;
  //     defaultAmount?: number;
  //     poType?: { [type: string]: number };
  //   }>();

  //   for (const { _id, totalPOAmount, totalDiscountAmount, totalDefaultAmount } of pos) {
  //     if (_id && _id.name) {
  //       const existing = map.get(_id.name) || { 
  //         po: 0, 
  //         vo: 0, 
  //         paid: 0, 
  //         discount: 0,
  //         defaultAmount: 0,
  //         poType: {}
  //       };

  //       // Update based on type
  //       if (_id.type === "PO") {
  //         existing.po = (existing.po || 0) + totalPOAmount;
  //       } else if (_id.type === "VO") {
  //         existing.vo = (existing.vo || 0) + totalPOAmount;
  //       }

  //       // Update poType tracking
  //       existing.poType = existing.poType || {};
  //       existing.poType[_id.type || "UNKNOWN"] = (existing.poType[_id.type || "UNKNOWN"] || 0) + totalPOAmount;

  //       // Update discount and default amounts
  //       existing.discount = (existing.discount || 0) + (totalDiscountAmount || 0);
  //       existing.defaultAmount = (existing.defaultAmount || 0) + (totalDefaultAmount || 0);

  //       map.set(_id.name, existing);
  //     }
  //   }

  //   for (const { _id, totalPaidAmount } of pvs) {
  //     if (_id && _id.name) {
  //       const existing = map.get(_id.name) || { po: 0, vo: 0, discount: 0, defaultAmount: 0, poType: {} };
  //       existing.paid = (existing.paid || 0) + totalPaidAmount;
  //       map.set(_id.name, existing);
  //     }
  //   }

  //   // Build final result
  //   const result: any[] = [];

  //   let totalCost = 0;
  //   let totalCostPO = 0;
  //   let totalCostVO = 0;
  //   let totalPayment = 0;
  //   let totalOutstanding = 0;
  //   let totalDiscountAmount = 0;
  //   let totalDefaultAmount = 0;

  //   // Use all unique business partners
  //   for (const [bpName, bpCode] of allBPs.entries()) {
  //     const { po = 0, vo = 0, paid = 0, discount = 0, defaultAmount = 0, poType = {} } = 
  //       map.get(bpName) || { po: 0, vo: 0, paid: 0, discount: 0, defaultAmount: 0, poType: {} };

  //     const totalForBP = po + vo;
  //     const outstanding = totalForBP - discount - defaultAmount - paid;

  //     // Update summary totals
  //     totalCost += totalForBP;
  //     totalCostPO += po;
  //     totalCostVO += vo;
  //     totalPayment += paid;
  //     totalOutstanding += (outstanding > 0) ? outstanding : 0;
  //     totalDiscountAmount += discount;
  //     totalDefaultAmount += defaultAmount;

  //     // Include all business partners
  //     const poDetails = await this.poModel.find({
  //       BPName: bpName,
  //       isExcluded: false
  //     }).lean();

  //     const paymentDetails = await this.paymentModel.find({
  //       BPName: bpName,
  //       isExcluded: false
  //     }).lean();

  //     // Get discount remarks for this BP
  //     const discountRemarks = poDetails
  //       .filter(po => po.discountAmount && po.discountAmount > 0 && po.discountRemark)
  //       .map(po => po.discountRemark);

  //     // Get unique remarks only
  //     const uniqueDiscountRemarks = [...new Set(discountRemarks)];

  //     result.push({
  //       bpName,
  //       bpCode,
  //       totalPO: po,
  //       totalVO: vo,
  //       totalCost: totalForBP,
  //       totalPaid: paid,
  //       outstanding,
  //       totalDiscount: discount,
  //       totalDefaultAmount: defaultAmount,
  //       discountRemarks: uniqueDiscountRemarks,
  //       poBreakdown: poType, // Include breakdown by PO type
  //       poDetails,
  //       paymentDetails
  //     });
  //   }

  //   // Sort alphabetically
  //   result.sort((a, b) => a.bpName.localeCompare(b.bpName));

  //   return {
  //     success: true,
  //     data: result,
  //     totalCost,
  //     totalCostPO,
  //     totalCostVO,
  //     totalPayment,
  //     totalOutstanding,
  //     totalDiscountAmount,
  //     totalDefaultAmount
  //   };
  // }

  // async getOutstandingBalances(): Promise<any> {
  //   // For POs, include BPCode and type in the group by and calculate discount and default amounts
  //   const pos = await this.poModel.aggregate([
  //     {
  //       $match: { isExcluded: false }
  //     },
  //     {
  //       $group: {
  //         _id: {
  //           name: "$BPName",
  //           code: "$BPCode",
  //           type: "$type" // Include type for PO/VO breakdown
  //         },
  //         totalPOAmount: { $sum: "$TotalRM" },
  //         totalDiscountAmount: { $sum: "$discountAmount" },
  //         totalDefaultAmount: { 
  //           $sum: { 
  //             $cond: [
  //               { $eq: ["$isDefault", true] }, 
  //               { $ifNull: ["$defaultAmount", 0] }, 
  //               0
  //             ] 
  //           }
  //         }
  //       }
  //     }
  //   ]);

  //   // For payments, include BP in the group by and differentiate between PO and VO payments
  //   const pvs = await this.paymentModel.aggregate([
  //     {
  //       $match: { isExcluded: false }
  //     },
  //     {
  //       $group: {
  //         _id: {
  //           name: "$BPName",
  //           code: "$BP"
  //         },
  //         // For PO payments (with invoiceNo)
  //         totalPOPaidAmount: { 
  //           $sum: { 
  //             $cond: [
  //               { $ne: [{ $ifNull: ["$invoiceNo", ""] }, ""] }, 
  //               "$converted", 
  //               0
  //             ] 
  //           }
  //         },
  //         // For VO payments (without invoiceNo)
  //         totalVOPaidAmount: { 
  //           $sum: { 
  //             $cond: [
  //               { $eq: [{ $ifNull: ["$invoiceNo", ""] }, ""] }, 
  //               "$converted", 
  //               0
  //             ] 
  //           }
  //         },
  //         // Keep the total for backward compatibility
  //         totalPaidAmount: { $sum: "$converted" }
  //       }
  //     }
  //   ]);

  //   // Track BP names and codes together
  //   const allBPs = new Map<string, string>();

  //   for (const { _id } of pos) {
  //     if (_id && _id.name) {
  //       allBPs.set(_id.name, _id.code || '');
  //     }
  //   }

  //   for (const { _id } of pvs) {
  //     if (_id && _id.name) {
  //       // If we haven't seen this BP yet or it doesn't have a code, add it
  //       if (!allBPs.has(_id.name) || !allBPs.get(_id.name)) {
  //         allBPs.set(_id.name, _id.code || '');
  //       }
  //     }
  //   }

  //   // Merge PO and PV totals, now with type differentiation and default amount
  //   const map = new Map<string, { 
  //     po?: number; 
  //     vo?: number; 
  //     poPaid?: number;
  //     voPaid?: number;
  //     paid?: number; 
  //     discount?: number;
  //     defaultAmount?: number;
  //     poType?: { [type: string]: number };
  //   }>();

  //   for (const { _id, totalPOAmount, totalDiscountAmount, totalDefaultAmount } of pos) {
  //     if (_id && _id.name) {
  //       const existing = map.get(_id.name) || { 
  //         po: 0, 
  //         vo: 0, 
  //         poPaid: 0,
  //         voPaid: 0,
  //         paid: 0, 
  //         discount: 0,
  //         defaultAmount: 0,
  //         poType: {}
  //       };

  //       // Update based on type
  //       if (_id.type === "PO") {
  //         existing.po = (existing.po || 0) + totalPOAmount;
  //       } else if (_id.type === "VO") {
  //         existing.vo = (existing.vo || 0) + totalPOAmount;
  //       }

  //       // Update poType tracking
  //       existing.poType = existing.poType || {};
  //       existing.poType[_id.type || "UNKNOWN"] = (existing.poType[_id.type || "UNKNOWN"] || 0) + totalPOAmount;

  //       // Update discount and default amounts
  //       existing.discount = (existing.discount || 0) + (totalDiscountAmount || 0);
  //       existing.defaultAmount = (existing.defaultAmount || 0) + (totalDefaultAmount || 0);

  //       map.set(_id.name, existing);
  //     }
  //   }

  //   for (const { _id, totalPaidAmount, totalPOPaidAmount, totalVOPaidAmount } of pvs) {
  //     if (_id && _id.name) {
  //       const existing = map.get(_id.name) || { 
  //         po: 0, 
  //         vo: 0, 
  //         poPaid: 0,
  //         voPaid: 0,
  //         paid: 0, 
  //         discount: 0, 
  //         defaultAmount: 0, 
  //         poType: {} 
  //       };

  //       existing.paid = (existing.paid || 0) + totalPaidAmount;
  //       existing.poPaid = (existing.poPaid || 0) + (totalPOPaidAmount || 0);
  //       existing.voPaid = (existing.voPaid || 0) + (totalVOPaidAmount || 0);

  //       map.set(_id.name, existing);
  //     }
  //   }

  //   // Build final result
  //   const result: any[] = [];

  //   let totalCost = 0;
  //   let totalCostPO = 0;
  //   let totalCostVO = 0;
  //   let totalPayment = 0;
  //   let totalPOPayment = 0;
  //   let totalVOPayment = 0;
  //   let totalOutstanding = 0;
  //   let totalDiscountAmount = 0;
  //   let totalDefaultAmount = 0;

  //   // Use all unique business partners
  //   for (const [bpName, bpCode] of allBPs.entries()) {
  //     const { 
  //       po = 0, 
  //       vo = 0, 
  //       paid = 0, 
  //       poPaid = 0,
  //       voPaid = 0,
  //       discount = 0, 
  //       defaultAmount = 0, 
  //       poType = {} 
  //     } = map.get(bpName) || { 
  //       po: 0, 
  //       vo: 0, 
  //       paid: 0, 
  //       poPaid: 0,
  //       voPaid: 0,
  //       discount: 0, 
  //       defaultAmount: 0, 
  //       poType: {} 
  //     };

  //     const totalForBP = po + vo;
  //     const outstanding = totalForBP - discount - defaultAmount - paid;

  //     // Update summary totals
  //     totalCost += totalForBP;
  //     totalCostPO += po;
  //     totalCostVO += vo;
  //     totalPayment += paid;
  //     totalPOPayment += poPaid;
  //     totalVOPayment += voPaid;
  //     totalOutstanding += (outstanding > 0) ? outstanding : 0;
  //     totalDiscountAmount += discount;
  //     totalDefaultAmount += defaultAmount;

  //     // Include all business partners
  //     const poDetails = await this.poModel.find({
  //       BPName: bpName,
  //       isExcluded: false
  //     }).lean();

  //     const paymentDetails = await this.paymentModel.find({
  //       BPName: bpName,
  //       isExcluded: false
  //     }).lean();

  //     // Get discount remarks for this BP
  //     const discountRemarks = poDetails
  //       .filter(po => po.discountAmount && po.discountAmount > 0 && po.discountRemark)
  //       .map(po => po.discountRemark);

  //     // Get unique remarks only
  //     const uniqueDiscountRemarks = [...new Set(discountRemarks)];

  //     result.push({
  //       bpName,
  //       bpCode,
  //       totalPO: po,
  //       totalVO: vo,
  //       totalCost: totalForBP,
  //       totalPaid: paid,
  //       totalPOPaid: poPaid,
  //       totalVOPaid: voPaid,
  //       outstanding,
  //       totalDiscount: discount,
  //       totalDefaultAmount: defaultAmount,
  //       discountRemarks: uniqueDiscountRemarks,
  //       poBreakdown: poType, // Include breakdown by PO type
  //       poDetails,
  //       paymentDetails
  //     });
  //   }

  //   // Sort alphabetically
  //   result.sort((a, b) => a.bpName.localeCompare(b.bpName));

  //   return {
  //     success: true,
  //     data: result,
  //     totalCost,
  //     totalCostPO,
  //     totalCostVO,
  //     totalPayment,
  //     totalPOPayment,
  //     totalVOPayment,
  //     totalOutstanding,
  //     totalDiscountAmount,
  //     totalDefaultAmount
  //   };
  // }

  async getOutstandingBalances(): Promise<any> {
    // For POs, include BPCode and type in the group by and calculate discount and default amounts
    const pos = await this.poModel.aggregate([
      {
        $match: { isExcluded: false }
      },
      {
        $group: {
          _id: {
            name: "$BPName",
            code: "$BPCode",
            type: "$type" // Include type for PO/VO breakdown
          },
          totalPOAmount: { $sum: "$TotalRM" },
          totalDiscountAmount: { $sum: "$discountAmount" },
          totalDefaultAmount: {
            $sum: {
              $cond: [
                { $eq: ["$isDefault", true] },
                { $ifNull: ["$defaultAmount", 0] },
                0
              ]
            }
          }
        }
      }
    ]);

    // Get all payment records for detailed breakdown
    const allPayments = await this.paymentModel.find({ isExcluded: false }).lean();

    // Create detailed payment breakdowns
    const paymentBreakdowns = {
      poPayments: allPayments.filter(payment => payment.invoiceNo && payment.invoiceNo !== ""),
      voPayments: allPayments.filter(payment => payment.type == 'VO')
    };

    // For payments, include BP in the group by and differentiate between PO and VO payments
    const pvs = await this.paymentModel.aggregate([
      {
        $match: { isExcluded: false }
      },
      {
        $group: {
          _id: {
            name: "$BPName",
            code: "$BP"
          },
          // For PO payments (with invoiceNo)
          totalPOPaidAmount: {
            $sum: {
              $cond: [
                { $eq: ["$type", "PO"] },
                "$converted",
                0
              ]
            }
          },
          // For VO payments (type is 'VO')
          totalVOPaidAmount: {
            $sum: {
              $cond: [
                { $eq: ["$type", "VO"] },
                "$converted",
                0
              ]
            }
          },
          // Keep the total for backward compatibility
          totalPaidAmount: { $sum: "$converted" }
        }
      }
    ]);

    // Track BP names and codes together
    const allBPs = new Map<string, string>();

    for (const { _id } of pos) {
      if (_id && _id.name) {
        allBPs.set(_id.name, _id.code || '');
      }
    }

    for (const { _id } of pvs) {
      if (_id && _id.name) {
        // If we haven't seen this BP yet or it doesn't have a code, add it
        if (!allBPs.has(_id.name) || !allBPs.get(_id.name)) {
          allBPs.set(_id.name, _id.code || '');
        }
      }
    }

    // Merge PO and PV totals, now with type differentiation and default amount
    const map = new Map<string, {
      po?: number;
      vo?: number;
      poPaid?: number;
      voPaid?: number;
      paid?: number;
      discount?: number;
      defaultAmount?: number;
      poType?: { [type: string]: number };
    }>();

    for (const { _id, totalPOAmount, totalDiscountAmount, totalDefaultAmount } of pos) {
      if (_id && _id.name) {
        const existing = map.get(_id.name) || {
          po: 0,
          vo: 0,
          poPaid: 0,
          voPaid: 0,
          paid: 0,
          discount: 0,
          defaultAmount: 0,
          poType: {}
        };

        // Update based on type
        if (_id.type === "PO") {
          existing.po = (existing.po || 0) + totalPOAmount;
        } else if (_id.type === "VO") {
          existing.vo = (existing.vo || 0) + totalPOAmount;
        }

        // Update poType tracking
        existing.poType = existing.poType || {};
        existing.poType[_id.type || "UNKNOWN"] = (existing.poType[_id.type || "UNKNOWN"] || 0) + totalPOAmount;

        // Update discount and default amounts
        existing.discount = (existing.discount || 0) + (totalDiscountAmount || 0);
        existing.defaultAmount = (existing.defaultAmount || 0) + (totalDefaultAmount || 0);

        map.set(_id.name, existing);
      }
    }

    for (const { _id, totalPaidAmount, totalPOPaidAmount, totalVOPaidAmount } of pvs) {
      if (_id && _id.name) {
        const existing = map.get(_id.name) || {
          po: 0,
          vo: 0,
          poPaid: 0,
          voPaid: 0,
          paid: 0,
          discount: 0,
          defaultAmount: 0,
          poType: {}
        };

        existing.paid = (existing.paid || 0) + totalPaidAmount;
        existing.poPaid = (existing.poPaid || 0) + (totalPOPaidAmount || 0);
        existing.voPaid = (existing.voPaid || 0) + (totalVOPaidAmount || 0);

        map.set(_id.name, existing);
      }
    }

    // Build final result
    const result: any[] = [];

    let totalCost = 0;
    let totalCostPO = 0;
    let totalCostVO = 0;
    let totalPayment = 0;
    let totalPOPayment = 0;
    let totalVOPayment = 0;
    let totalOutstanding = 0;
    let totalDiscountAmount = 0;
    let totalDefaultAmount = 0;

    // Use all unique business partners
    // Create BP-specific payment records for global breakdown
    const bpPaymentBreakdowns = {};

    for (const [bpName, bpCode] of allBPs.entries()) {
      const {
        po = 0,
        vo = 0,
        paid = 0,
        poPaid = 0,
        voPaid = 0,
        discount = 0,
        defaultAmount = 0,
        poType = {}
      } = map.get(bpName) || {
        po: 0,
        vo: 0,
        paid: 0,
        poPaid: 0,
        voPaid: 0,
        discount: 0,
        defaultAmount: 0,
        poType: {}
      };

      const totalForBP = po + vo;
      const outstanding = totalForBP - discount - defaultAmount - paid;

      // Update summary totals
      totalCost += totalForBP;
      totalCostPO += po;
      totalCostVO += vo;
      totalPayment += paid;
      totalPOPayment += poPaid;
      totalVOPayment += voPaid;
      totalOutstanding += (outstanding > 0) ? outstanding : 0;
      totalDiscountAmount += discount;
      totalDefaultAmount += defaultAmount;

      // Include all business partners
      const poDetails = await this.poModel.find({
        BPName: bpName,
        isExcluded: false
      }).lean();

      const paymentDetails = await this.paymentModel.find({
        BPName: bpName,
        isExcluded: false
      }).lean();

      // Create detailed payment breakdowns for this BP (for global breakdown)
      const bpPOPayments = paymentDetails.filter(payment => payment.invoiceNo && payment.invoiceNo !== "");
      const bpVOPayments = paymentDetails.filter(payment => !payment.invoiceNo || payment.invoiceNo === "");

      // Store BP-specific payment breakdowns in global object
      bpPaymentBreakdowns[bpName] = {
        poPayments: bpPOPayments,
        voPayments: bpVOPayments
      };

      // Get discount remarks for this BP
      const discountRemarks = poDetails
        .filter(po => po.discountAmount && po.discountAmount > 0 && po.discountRemark)
        .map(po => po.discountRemark);

      // Get unique remarks only
      const uniqueDiscountRemarks = [...new Set(discountRemarks)];

      result.push({
        bpName,
        bpCode,
        totalPO: po,
        totalVO: vo,
        totalCost: totalForBP,
        totalPaid: paid,
        totalPOPaid: poPaid,
        totalVOPaid: voPaid, 
        outstanding,
        totalDiscount: discount,
        totalDefaultAmount: defaultAmount,
        discountRemarks: uniqueDiscountRemarks,
        poBreakdown: poType, // Include breakdown by PO type
        poDetails,
        paymentDetails
      });
    }

    // Sort alphabetically
    result.sort((a, b) => a.bpName.localeCompare(b.bpName));

    return {
      success: true,
      data: result,
      totalCost,
      totalCostPO,
      totalCostVO,
      totalPayment,
      totalPOPayment,
      totalVOPayment,
      totalOutstanding,
      totalDiscountAmount,
      totalDefaultAmount,
      // Add global payment breakdowns for debugging
      paymentBreakdowns: {
        all: paymentBreakdowns,
        byBP: bpPaymentBreakdowns
      }
    };
  }

  async getBusinessPartnerByCode(bpCode: string): Promise<any> {
    // Validate input
    if (!bpCode) {
      return {
        success: false,
        message: 'Business partner code is required',
      };
    }

    try {
      // Find PO details for the specific BP code
      const poDetails = await this.poModel.find({
        BPCode: bpCode
      }).lean();

      // Find payment details for the specific BP code
      // Note: In ProjectPV schema it's named "BP" not "BPCode"
      const paymentDetails = await this.paymentModel.find({
        BP: bpCode
      }).lean();

      // Calculate totals with PO/VO breakdown
      let totalPO = 0;
      let totalVO = 0;
      let totalDiscount = 0;
      let totalDefaultAmount = 0;

      // Group POs by type and calculate totals
      poDetails.forEach(po => {
        if (po.type === "PO") {
          totalPO += (po.TotalRM || 0);
        } else if (po.type === "VO") {
          totalVO += (po.TotalRM || 0);
        }

        // Add discount amount
        totalDiscount += (po.discountAmount || 0);

        // Add default amount if isDefault is true
        if (po.isDefault) {
          totalDefaultAmount += (po.defaultAmount || 0);
        }
      });

      const totalCost = totalPO + totalVO;
      const totalPaid = paymentDetails.reduce((sum, pv) => sum + (pv.converted || 0), 0);
      const outstanding = totalCost - totalDiscount - totalPaid;

      // If no BP found with this code
      if (poDetails.length === 0 && paymentDetails.length === 0) {
        return {
          success: false,
          message: 'No business partner found with the provided code',
        };
      }

      // Get BP name from the details (using the first record found)
      const bpName = poDetails.length > 0 ? poDetails[0].BPName :
        (paymentDetails.length > 0 ? paymentDetails[0].BPName : '');

      // Get discount remarks
      const discountRemarks = poDetails
        // .filter(po => po.discountAmount && po.discountAmount > 0 && po.discountRemark)
        .filter(po => po.discountRemark)
        .map(po => po.discountRemark);

      // Get unique remarks only
      const uniqueDiscountRemarks = [...new Set(discountRemarks)];

      // Create PO breakdown by type for additional details
      const poBreakdown = poDetails.reduce((acc, po) => {
        const type = po.type || "UNKNOWN";
        acc[type] = (acc[type] || 0) + (po.TotalRM || 0);
        return acc;
      }, {});

      return {
        success: true,
        data: {
          bpCode,
          bpName,
          totalPO,
          totalVO,
          totalCost,
          totalPaid,
          outstanding,
          totalDiscount,
          totalDefaultAmount,
          discountRemarks: uniqueDiscountRemarks,
          poBreakdown,
          poDetails,
          paymentDetails,
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error searching for business partner by code',
        error: error.message,
      };
    }
  }

  async getBusinessPartnerByName(bpName: string): Promise<any> {
    // Validate input
    if (!bpName) {
      return {
        success: false,
        message: 'Business partner name is required',
      };
    }

    try {
      // Find PO details for the specific BP
      const poDetails = await this.poModel.find({
        BPName: { $regex: new RegExp(bpName, 'i') }
      }).lean();

      // Find payment details for the specific BP
      const paymentDetails = await this.paymentModel.find({
        BPName: { $regex: new RegExp(bpName, 'i') }
      }).lean();

      // Calculate totals
      const totalPO = poDetails.reduce((sum, po) => sum + (po.TotalRM || 0), 0);
      const totalPaid = paymentDetails.reduce((sum, pv) => sum + (pv.converted || 0), 0);
      const outstanding = totalPO - totalPaid;

      // Get unique BP names that matched the search
      const bpNamesFound = new Set([
        ...poDetails.map(po => po.BPName),
        ...paymentDetails.map(pv => pv.BPName)
      ]);

      // If no BP found
      if (bpNamesFound.size === 0) {
        return {
          success: false,
          message: 'No business partner found with the provided name',
        };
      }

      // Group results by BP name (in case the regex matched multiple BPs)
      const resultsByBp = Array.from(bpNamesFound).map(name => {
        const bpPoDetails = poDetails.filter(po => po.BPName === name);
        const bpPaymentDetails = paymentDetails.filter(pv => pv.BPName === name);

        const bpTotalPO = bpPoDetails.reduce((sum, po) => sum + (po.TotalRM || 0), 0);
        const bpTotalPaid = bpPaymentDetails.reduce((sum, pv) => sum + (pv.converted || 0), 0);
        const bpOutstanding = bpTotalPO - bpTotalPaid;

        return {
          bpName: name,
          totalPO: bpTotalPO,
          totalPaid: bpTotalPaid,
          outstanding: bpOutstanding,
          poDetails: bpPoDetails,
          paymentDetails: bpPaymentDetails,
        };
      });

      return {
        success: true,
        data: resultsByBp,
        totalMatches: bpNamesFound.size
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error searching for business partner',
        error: error.message,
      };
    }
  }

  convertToDate(dateString: string): Date {
    // Check if dateString is valid and follows `DD/MM/YYYY` format
    if (!dateString || typeof dateString !== 'string') return new Date();

    // Split the dateString into [DD, MM, YYYY]
    const parts = dateString.split('/');

    // Ensure there are exactly 3 parts (day, month, year)
    if (parts.length !== 3) return new Date();

    // Rearrange to `YYYY-MM-DD` format and create a new Date object
    const formattedDate = `${parts[2]}-${parts[1]}-${parts[0]}`;
    return new Date(formattedDate);
  }

  isRowEmpty(row: any[]): boolean {
    const nonEmptyCells = row.filter(cell => cell !== null && cell !== undefined && cell !== '');
    return nonEmptyCells.length === 0;  // Only process rows with at least one meaningful value
  }

  async findAll(): Promise<any> {
    const payments = await this.paymentModel.aggregate([
      {
        $addFields: {
          parsedDate: {
            $dateFromString: {
              dateString: "$date",
              format: "%d/%m/%Y"  // Specify the format for conversion
            }
          }
        }
      },
      {
        $sort: { parsedDate: -1 }  // Sort by the converted date in descending order
      },
      {
        $project: {
          parsedDate: 0  // Remove the temporary `parsedDate` field from the result
        }
      }
    ]).exec();

    return { success: true, data: payments };
  }


  // NEW METHOD: Include or Exclude a payment by toggling the `isExcluded` status
  async toggleIsExcludedStatus(id: string, isExcluded: boolean): Promise<ProjectPV | any> {
    const updatedPayment = await this.paymentModel.findByIdAndUpdate(
      id,
      { isExcluded },
      { new: true }, // Return the updated document
    );
    return updatedPayment;
  }

  async getTotalNonExcludedPayments(): Promise<any> {
    // Use `$facet` to handle multiple aggregations within a single query
    const result = await this.paymentModel.aggregate([
      { $match: { isExcluded: false } }, // Match only non-excluded payments
      {
        $facet: {
          // Facet 1: Total non-excluded payments
          totalNonExcludedPayments: [{ $group: { _id: null, total: { $sum: '$converted' } } }],
          // Facet 2: Total payments grouped by `status`
          totalPaymentsByStatus: [{ $group: { _id: '$status', total: { $sum: '$converted' } } }],
          // Facet 3: Get the latest `date` in the collection (assuming date format is DD/MM/YYYY)
          latestUpdatedAt: [
            {
              $project: {
                // Convert date string to a Date object using a custom format, then return the original date string
                dateObject: {
                  $dateFromString: { dateString: "$date", format: "%d/%m/%Y" }
                },
                date: 1
              }
            },
            { $sort: { dateObject: -1 } }, // Sort by the dateObject (descending)
            { $limit: 1 }, // Get the latest date only
          ]
        }
      }
    ]);

    // Extract results from the facets
    const totalNonExcluded = result[0].totalNonExcludedPayments.length > 0 ? result[0].totalNonExcludedPayments[0].total : 0;

    // Format `totalsByStatus` to use snake_case for the keys
    const totalsByStatus = result[0].totalPaymentsByStatus.reduce((acc, item) => {
      const formattedStatus = item._id ? item._id.toLowerCase().replace(/\s+/g, '_') : '';
      acc[formattedStatus] = item.total;
      return acc;
    }, {});

    // Get the latest updatedAt date from the result
    const latestUpdatedAt = result[0].latestUpdatedAt.length > 0 ? result[0].latestUpdatedAt[0].date : null;

    // Return a combined response object with formatted keys and latestUpdatedAt
    return {
      totalNonExcluded,
      totalsByStatus,
      updatedAt: latestUpdatedAt // This is the latest 'date' from the collection
    };
  }

  async getTotalPaymentsByStatus(): Promise<any> {
    // Aggregate and group by the `status` field to calculate totals for each status
    const result = await this.paymentModel.aggregate([
      { $match: { isExcluded: false } }, // Optional: Match non-excluded payments if needed
      {
        $group: {
          _id: '$status', // Group by `status` field
          total: { $sum: '$converted' }, // Sum up the `converted` field for each status group
        },
      },
    ]);

    // Format the result into a more readable object
    const totalsByStatus = result.reduce((acc, item) => {
      acc[item._id] = item.total;
      return acc;
    }, {});

    return totalsByStatus;
  }

  async getTotalExcludedPayments(): Promise<any> {
    // Use aggregation to sum up the `docAmount` field where `isExcluded` is false
    const result = await this.paymentModel.aggregate([
      { $match: { isExcluded: true } }, // Match documents where `isExcluded` is false
      { $group: { _id: null, total: { $sum: '$converted' } } }, // Sum up the `docAmount` field
    ]);

    // Return the total sum, or 0 if no matching documents
    return {
      total: result.length > 0 ? result[0].total : 0
    }
  }
}
