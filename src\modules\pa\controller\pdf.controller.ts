// import { Controller, Post, UploadedFile, UseInterceptors } from '@nestjs/common';
// import { FileInterceptor } from '@nestjs/platform-express';
// import * as path from 'path';
// // import { PdfService } from '../service/pdf.service';
// import * as fs from 'fs';

// @Controller('pdf')
// export class PdfController {
//   constructor(private readonly pdfService: PdfService) {}

//   @Post('convert')
//   @UseInterceptors(FileInterceptor('file'))
//   async convertPdf(@UploadedFile() file: Express.Multer.File): Promise<{ outputPath: string }> {
//     const inputPath = path.join(__dirname, `../uploads/${file.originalname}`);
//     const outputPath = path.join(__dirname, `../outputs/image-only-${file.originalname}`);

//     // Save the uploaded file
//     fs.writeFileSync(inputPath, file.buffer);

//     // Convert the PDF
//     await this.pdfService.convertPdfToImageAndBack(inputPath, outputPath);

//     return { outputPath };
//   }
// }
