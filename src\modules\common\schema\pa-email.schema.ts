import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true, collection: 'email_scheduler' })
export class EmailScheduler {
  @Prop({ required: true })
  to: string;

  @Prop({ required: true })
  subject: string;

  @Prop({ required: true })
  body: string;

  @Prop({ default: 'pending' })  // pending, sent, failed
  status: string;

  @Prop({ type: Date })
  scheduledAt?: Date; // Optional scheduled time
}

export type EmailSchedulerDocument = EmailScheduler & Document;
export const EmailSchedulerSchema = SchemaFactory.createForClass(EmailScheduler);
