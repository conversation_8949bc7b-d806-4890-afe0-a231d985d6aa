import { Types } from 'mongoose';

export interface PopulatedLineManager {
  _id: Types.ObjectId;
  fullName: string;
  email: string;
  designation?: string;
  department?: string;
}

export interface PopulatedDepartment {
  _id: Types.ObjectId;
  code: string;
  description: string;
  manager?: PopulatedLineManager;
  hod?: PopulatedLineManager;
}

export interface PopulatedIppaUser {
  _id: Types.ObjectId;
  username: string;
  fullName: string;
  email: string;
  company: string;
  group: string;
  businessUnit: string;
  employeeId: string;
  employeeGrade: string;
  position: string;
  positionLevel: string;
  division: string;
  department: string;
  departmentRef?: Types.ObjectId | PopulatedDepartment;
  departmentCode: string;
  departmentId: string;
  section: string;
  lineManager?: Types.ObjectId | PopulatedLineManager;
  workLocation: string;
  joinedDate: Date;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

// Type guard functions
export function isPopulatedLineManager(manager: any): manager is PopulatedLineManager {
  return manager && typeof manager === 'object' && 'fullName' in manager && 'email' in manager;
}

export function isPopulatedDepartment(dept: any): dept is PopulatedDepartment {
  return dept && typeof dept === 'object' && 'code' in dept && 'description' in dept;
}
