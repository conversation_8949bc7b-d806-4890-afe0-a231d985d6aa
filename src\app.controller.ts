import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  getQuickHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'uzmacloud-api',
      message: 'API is running. Use /health/detailed for comprehensive status.'
    };
  }
}
