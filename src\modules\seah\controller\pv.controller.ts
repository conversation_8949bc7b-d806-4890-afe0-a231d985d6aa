import { Body, Controller, ForbiddenException, Get, Param, Patch, Post, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { SeahPVService } from '../service/pv.service';
import { Payment } from '../schema/pv.schema';

@Controller('payments')
export class SeahPVController {
  constructor(private readonly paymentService: SeahPVService) {}

  @Post('import')
  @UseInterceptors(FileInterceptor('file'))
  async importPayments(@UploadedFile() file: Express.Multer.File): Promise<string> {
    // return "Failed to process payment"
    throw new ForbiddenException('Failed to process PV.');
    return await this.paymentService.importPaymentsFromExcel(file.buffer);
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadPayments(@UploadedFile() file: Express.Multer.File): Promise<string> {
    return await this.paymentService.importPaymentsFromExcel(file.buffer);
  }

  @Get()
  async getAllPayments(): Promise<any> {
    return await this.paymentService.findAll();
  }

  // New API endpoint to get the sum of payments where `isExcluded` is false
  @Get('total')
  async getTotalNonExcludedPayments(): Promise<any> {
    return await this.paymentService.getTotalNonExcludedPayments();
  // return await this.paymentService.getTotalPaymentsByStatus();
  }

  // New API endpoint to get the sum of payments where `isExcluded` is false
  @Get('total-exclude')
  async getTotalExcludedPayments(): Promise<any> {
    return await this.paymentService.getTotalExcludedPayments();
  }

  // New endpoint to update the `isExcluded` status
  // New endpoint to toggle the `isExcluded` status (Include/Exclude)
  @Patch(':id/toggle-exclusion')
  async toggleIsExcluded(
    @Param('id') id: string,
    @Body('isExcluded') isExcluded: boolean,
  ): Promise<{ success: boolean; data: Payment; message: string }> {
    const updatedPayment = await this.paymentService.toggleIsExcludedStatus(id, isExcluded);
    return {
      success: true,
      data: updatedPayment,
      message: `Record with ID ${id} successfully updated to isExcluded: ${isExcluded}`,
    };
  }
}
