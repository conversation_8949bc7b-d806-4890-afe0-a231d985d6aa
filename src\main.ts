import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: ['log', 'error', 'warn', 'debug', 'verbose'],
  });

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));

  // CORS configuration
  app.enableCors({
    // origin: process.env.FRONTEND_URL || '*',
    origin: ['http://localhost', 'http://localhost:4200', 'https://uzmacloud.com', 'https://www.uzmacloud.com'],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    allowedHeaders: 'Content-Type, Authorization',
    credentials: true,
  });

  // API prefix
  app.setGlobalPrefix('api', {
    exclude: ['health', 'health/detailed'],
  });

  // Swagger API documentation
  // const config = new DocumentBuilder()
  //   .setTitle('UzmaCloud API')
  //   .setDescription('UzmaCloud platform API with DocumentDB integration')
  //   .setVersion('1.0')
  //   .addTag('uzmacloud')
  //   .addTag('health')
  //   .addTag('users')
  //   .addTag('auth')
  //   .build();
  
  // const document = SwaggerModule.createDocument(app, config);
  // SwaggerModule.setup('api/docs', app, document);

  const port = process.env.PORT || 3000;
  await app.listen(port, '0.0.0.0');
  
  console.log(`🚀 UzmaCloud API running on port ${port}`);
  console.log(`📚 API docs: http://localhost:${port}/api/docs`);
  console.log(`💚 Health check: http://localhost:${port}/health`);
  console.log(`🔗 DocumentDB integration: ${process.env.DATABASE_URL ? 'Enabled' : 'Disabled'}`);
}

bootstrap();
