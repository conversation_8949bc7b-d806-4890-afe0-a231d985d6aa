import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type FinanceBGDocument = FinanceBG & Document;

@Schema({ timestamps: true, collection: 'finance_bg' })
export class FinanceBG {

  @Prop()
  serialNo: string;

  @Prop()
  accountHolderName: string;

  @Prop()
  issuedOnBehalf: string;

  @Prop()
  bankName: string;

  @Prop()
  bankAccountNo: string;

  @Prop()
  bankGuaranteeNo: string;

  @Prop()
  bankGuaranteeType: string;

  @Prop()
  issuedDate: string;

  @Prop()
  expiredDate: string;

  @Prop()
  bankGuaranteeStatus: string;

  @Prop()
  currency: string;

  @Prop()
  bankGuaranteeAmountOrg: number;

  @Prop()
  bankGuaranteeAmountMyr: number;

  @Prop()
  depositPledgedMyr: number;

  @Prop()
  bankCommission: number;

  @Prop()
  stampDutyMyr: number;

  @Prop()
  bankHandlingFeeMyr: number;

  @Prop()
  beneficiaryName: string;

  @Prop()
  contractName: string;

  @Prop()
  contractNo: string;

  @Prop()
  pic: string;

  @Prop()
  remark: string;

  @Prop()
  createdBy: string;

  @Prop({attachments: [
    {
      remark: { type: String },
      type: { type: String },
      s3Url: { type: String },
    }
  ]})
    attachments: Array<{
      remark: string;
      type: string;
      s3Url: string;
    }>;
}
export const FinanceBGchema = SchemaFactory.createForClass(FinanceBG);

// Define indexes separately after schema creation
FinanceBGchema.index({ accountHolderName: 1, bankName: 1 });
FinanceBGchema.index({ bankName: 1 });
FinanceBGchema.index({ currency: 1 });
FinanceBGchema.index({ contractNo: 1 });
FinanceBGchema.index({ accountHolderName: 1, Currency: 1 });
