import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type PJMAccrualDocument = PJMAccrual & Document;

@Schema({ timestamps: true, collection: 'finance_pjm_accrual' })
export class PJMAccrual {

  @Prop({ type: Types.ObjectId, ref: 'PJM', required: true })
  pjmId: Types.ObjectId;

  @Prop()
  monthYear: string;

  @Prop()
  location: string;

  @Prop()
  jobDescription: string;

  @Prop()
  serviceStartDate: string;

  @Prop()
  serviceEndDate: string;

  @Prop()
  jctNo: string;

  @Prop()
  revenue: number;

  @Prop()
  costDescription: string;

  @Prop()
  cost: number;

  @Prop()
  remark: string;

  @Prop()
  createdBy: string;

  @Prop({attachments: [
    {
      remark: { type: String },
      type: { type: String },
      s3Url: { type: String },
    }
  ]})
    attachments: Array<{
      remark: string;
      type: string;
      s3Url: string;
    }>;
}

export const PJMAccrualSchema = SchemaFactory.createForClass(PJMAccrual);
PJMAccrualSchema.index({ client: 1 });
PJMAccrualSchema.index({ startDate: 1 });
PJMAccrualSchema.index({ endDate: 1 });
PJMAccrualSchema.index({ projectCode: 1 });
