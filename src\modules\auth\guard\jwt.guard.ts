import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(private readonly jwtService: JwtService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('No token provided');  // Token is missing
    }

    try {
      const payload = this.jwtService.verify(token);  // Verify the JWT token
      request.user = payload.user;  // Attach the decoded user to the request object
      return true;  // Allow access
    } catch (error) {
      throw new UnauthorizedException('Invalid token');  // Token is invalid or expired
    }
  }

  private extractTokenFromHeader(request: any): string | null {
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.split(' ')[1];  // Extract the token from the header
  }
}