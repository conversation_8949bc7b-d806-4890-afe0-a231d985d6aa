import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User, UserDocument } from 'src/modules/user/schema/user.schema';

@Injectable()
export class MigrationService {
  private readonly logger = new Logger(MigrationService.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>
  ) {}

  async migrateUserRolesToArray(): Promise<{ success: number; errors: number }> {
    this.logger.log('Starting migration from single role to multiple roles...');
    
    let successCount = 0;
    let errorCount = 0;

    try {
      // Find all users with the old 'role' field (single ObjectId)
      const usersWithSingleRole = await this.userModel.find({
        role: { $exists: true, $ne: null }
      }).exec();

      this.logger.log(`Found ${usersWithSingleRole.length} users to migrate`);

      for (const user of usersWithSingleRole) {
        try {
          // Convert single role to array of roles
          const currentRole = user.role;
          
          await this.userModel.updateOne(
            { _id: user._id },
            {
              $set: { roles: [currentRole] },
              $unset: { role: '' }
            }
          );

          successCount++;
          this.logger.log(`✓ Migrated user: ${user.email}`);
        } catch (error) {
          errorCount++;
          this.logger.error(`✗ Failed to migrate user ${user.email}:`, error);
        }
      }

      this.logger.log(`Migration completed. Success: ${successCount}, Errors: ${errorCount}`);
      return { success: successCount, errors: errorCount };

    } catch (error) {
      this.logger.error('Migration failed:', error);
      throw error;
    }
  }

  async verifyMigration(): Promise<{
    usersWithOldRole: number;
    usersWithNewRoles: number;
    usersWithoutRoles: number;
  }> {
    const usersWithOldRole = await this.userModel.countDocuments({
      role: { $exists: true }
    });

    const usersWithNewRoles = await this.userModel.countDocuments({
      roles: { $exists: true }
    });

    const usersWithoutRoles = await this.userModel.countDocuments({
      roles: { $exists: false },
      role: { $exists: false }
    });

    this.logger.log(`Migration Verification:`);
    this.logger.log(`- Users with old 'role' field: ${usersWithOldRole}`);
    this.logger.log(`- Users with new 'roles' field: ${usersWithNewRoles}`);
    this.logger.log(`- Users without any roles: ${usersWithoutRoles}`);

    return {
      usersWithOldRole,
      usersWithNewRoles,
      usersWithoutRoles
    };
  }

  async rollbackMigration(): Promise<{ success: number; errors: number }> {
    this.logger.warn('Starting rollback migration...');
    
    let successCount = 0;
    let errorCount = 0;

    try {
      // Find users with exactly one role in the roles array
      const usersToRollback = await this.userModel.find({
        roles: { $exists: true, $size: 1 }
      }).exec();

      for (const user of usersToRollback) {
        try {
          const singleRole = user.roles[0];
          
          await this.userModel.updateOne(
            { _id: user._id },
            {
              $set: { role: singleRole },
              $unset: { roles: '' }
            }
          );

          successCount++;
          this.logger.log(`✓ Rolled back user: ${user.email}`);
        } catch (error) {
          errorCount++;
          this.logger.error(`✗ Failed to rollback user ${user.email}:`, error);
        }
      }

      this.logger.warn(`Rollback completed. Success: ${successCount}, Errors: ${errorCount}`);
      return { success: successCount, errors: errorCount };

    } catch (error) {
      this.logger.error('Rollback failed:', error);
      throw error;
    }
  }
}