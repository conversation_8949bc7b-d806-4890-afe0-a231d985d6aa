import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type SerialNumberDocument = PASerialNumber & Document;

@Schema({ collection: 'pa_serial_no' })
export class PASerialNumber {
  @Prop({ required: true })
  prefix: string; // e.g., "UZMA-EPA"

  @Prop({ required: true })
  currentNumber: number; // Last used serial number
}

export const PASerialNumberSchema = SchemaFactory.createForClass(PASerialNumber);
