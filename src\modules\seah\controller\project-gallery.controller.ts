import {
  Controller,
  Post,
  UploadedFiles,
  UseInterceptors,
  Body,
  BadRequestException,
  Req,
  UseGuards,
  Delete,
  Get,
  Param,
  Put,
  Query,
} from '@nestjs/common';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { ProjectGalleryService } from '../service/project-gallery.service';
import { JwtAuthGuard } from 'src/modules/auth/guard/jwt.guard';

@Controller('project-gallery')
export class ProjectGalleryController {
  constructor(private readonly projectGalleryService: ProjectGalleryService) { }

  @Post('upload')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(AnyFilesInterceptor()) // Handle multiple file uploads
  async uploadProjectGallery(
    @UploadedFiles() files: Express.MulterS3.File[],
    @Body('segment') segment: string,
    @Body('title') title: string,
    @Body('descriptions') descriptions: string[],
    @Body('order') order: number,
    @Req() req: any // Assuming the user information is attached in the request (like a JWT token)
  ) {
    // Ensure required fields are provided
    if (!segment || !title) {
      throw new BadRequestException('Segment, title, date, and order are required.');
    }

    const userId = req.user.userId; // Assuming the user ID is available via the request (e.g., using JWT)

    // Save the gallery metadata and file information to MongoDB
    const gallery = await this.projectGalleryService.saveGallery(
      segment,
      title,
      new Date(),
      files,
      descriptions,
      order,
      userId,
    );

    return {
      message: 'Project gallery uploaded successfully!',
      gallery,
    };
  }

  // Read galleries by segment or all
  @Get()
  async getGalleries(@Query('segment') segment?: string) {
    const galleries = await this.projectGalleryService.getGalleries(segment);
    return { galleries };
  }

  @Get('getById')
  async getGalleryById(@Query('id') id?: string) {
    const galleries = await this.projectGalleryService.getGalleriesById(id);
    return { galleries };
  }

  // Update an existing gallery
  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(AnyFilesInterceptor()) // Handle multiple file uploads
  async updateGallery(
    @Param('id') id: string,
    @UploadedFiles() files: Express.MulterS3.File[],
    @Body('segment') segment: string,
    @Body('title') title: string,
    @Body('date') date: string,
    @Body('descriptions') descriptions: string[],
    @Body('order') order: number,
    @Req() req: any,
  ) {
    const userId = req.user.userId;

    const updatedGallery = await this.projectGalleryService.updateGallery(
      id,
      segment,
      title,
      new Date(date),
      files,
      descriptions,
      order,
      userId,
    );

    return {
      message: 'Project gallery updated successfully!',
      updatedGallery,
    };
  }

  // Delete a gallery by ID
  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  async deleteGallery(@Param('id') id: string) {
    await this.projectGalleryService.deleteGallery(id);
    return { message: 'Gallery deleted successfully!' };
  }
}