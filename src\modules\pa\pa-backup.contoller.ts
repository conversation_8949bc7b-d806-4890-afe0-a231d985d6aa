// import { Controller, Get, Post, Param, Body, Put, Delete, NotFoundException, Req, UseGuards, Query } from '@nestjs/common';
// import { CreatePaymentAdviceDTO } from './dto/pa-main.dto'; // Import the DTO
// import { PaymentAdviceService } from './pa-main-backup.service';
// import { JwtAuthGuard } from 'src/auth/jwt.guard';

// @Controller('payment-advice')  // The route base URL would be /payment-advice
// export class PaController {
//   constructor(private readonly paymentAdviceService: PaymentAdviceService) { }

//   @UseGuards(JwtAuthGuard)  // Apply the JWT Guard
//   @Get('me')
//   async getPaymentAdvicesByUserId(@Req() req, @Query('page') page = '1',   // Get the `page` parameter from the query string (default to '1')
//     @Query('limit') limit = '10') {
//     // console.log("Req User: ", req.user);  // This should print the user object
//     const userId = req.user.userId;  // Assuming the user ID is stored in the request (from JWT or session)
//     // Convert query parameters to numbers
//     const pageNumber = parseInt(page, 10) || 1;
//     const limitNumber = parseInt(limit, 10) || 10;
//     return this.paymentAdviceService.findByParticipant(userId, pageNumber, limitNumber);
//   }

//   @UseGuards(JwtAuthGuard)  // Apply the JWT Guard
//   @Get('createdBy')
//   async getPaymentAdvicesByCreatedUserId(@Req() req, @Query('page') page = '1',   // Get the `page` parameter from the query string (default to '1')
//     @Query('limit') limit = '10') { // Get the `limit` parameter from the query string (default to '10')) {
//     // console.log("Req User: ", req.user);  // This should print the user object
//     const userId = req.user.userId;  // Assuming the user ID is stored in the request (from JWT or session)
//     // Convert query parameters to numbers
//     const pageNumber = parseInt(page, 10) || 1;
//     const limitNumber = parseInt(limit, 10) || 10;
//     return this.paymentAdviceService.findByCreatedBy(userId, pageNumber, limitNumber);
//   }

//   // Endpoint to create a new Payment Advice
//   @Post('create')
//   @UseGuards(JwtAuthGuard)  // Apply the JWT Guard
//   async createPaymentAdvice(@Body() createPaymentAdviceDto: CreatePaymentAdviceDTO, @Req() req) {
//     const userId = req.user.userId;  // Extract userId from the JWT token
//     return this.paymentAdviceService.createPaymentAdvice(createPaymentAdviceDto, userId);
//   }

//   @Get('all')
//   @UseGuards(JwtAuthGuard)  // Apply the JWT Guard
//   async listSubmittedPaymentAdvices() {
//     return this.paymentAdviceService.listSubmittedPaymentAdvices();
//   }

//   // Endpoint to fetch assigned Payment Advices for the logged-in user
//   @Get('assigned')
//   @UseGuards(JwtAuthGuard)  // Apply the JWT Guard
//   async getAssignedPaymentAdvices(@Req() req) {
//     const userId = req.user.userId;  // Extract userId from the JWT token
//     return this.paymentAdviceService.getAssignedPaymentAdvices(userId);
//   }

//   // Retrieve all Payment Advices
//   @Get()
//   @UseGuards(JwtAuthGuard)  // Apply the JWT Guard
//   async findAll() {
//     return this.paymentAdviceService.findAll();
//   }

//   // Retrieve a Payment Advice by ID
//   @Get(':id')
//   async findById(@Param('id') id: string) {
//     const paymentAdvice = await this.paymentAdviceService.findById(id);
//     if (!paymentAdvice) {
//       throw new NotFoundException(`Payment Advice with ID ${id} not found`);
//     }
//     return paymentAdvice;
//   }

//   // Update a Payment Advice by ID
//   @Put(':id')
//   async update(@Param('id') id: string, @Body() updatePaymentAdviceDto: CreatePaymentAdviceDTO) {
//     return this.paymentAdviceService.update(id, updatePaymentAdviceDto);
//   }

//   // Delete a Payment Advice by ID
//   @Delete(':id')
//   async delete(@Param('id') id: string) {
//     return this.paymentAdviceService.delete(id);
//   }
// }
