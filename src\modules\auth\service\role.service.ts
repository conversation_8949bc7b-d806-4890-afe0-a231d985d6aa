import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateRoleDTO } from '../dto/create-role.dto';
import { Role, RoleDocument } from '../schema/role.schema';
import { Permission, PermissionDocument } from '../schema/permission.schema';

@Injectable()
export class RoleService {
  constructor(
    @InjectModel(Role.name) private roleModel: Model<RoleDocument>,
    @InjectModel(Permission.name) private permissionModel: Model<PermissionDocument>,
  ) { }

  async create(createRoleDto: CreateRoleDTO): Promise<Role> {
    const role = new this.roleModel({
      name: createRoleDto.name,
      permissions: createRoleDto.permissions,  // Array of permission IDs
    });
    return await role.save();
  }

  async createRole(createRoleDto: CreateRoleDTO): Promise<Role> {
    // Check if role name already exists
    const existingRole = await this.roleModel.findOne({ name: createRoleDto.name });
    if (existingRole) {
      throw new ConflictException('Role name already exists');
    }

    // Validate permissions if provided
    if (createRoleDto.permissions && createRoleDto.permissions.length > 0) {
      const permissions = await this.permissionModel.find({
        _id: { $in: createRoleDto.permissions }
      });
      if (permissions.length !== createRoleDto.permissions.length) {
        throw new NotFoundException('One or more permissions not found');
      }
    }

    const role = new this.roleModel(createRoleDto);
    return role.save();
  }

  async findAll(): Promise<Role[]> {
    return this.roleModel.find()
      .populate('permissions')
      .exec();
  }

  async findById(id: string): Promise<Role> {
    const role = await this.roleModel.findById(id)
      .populate('permissions')
      .exec();

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    return role;
  }

  async updateRole(id: string, updateRoleDto: CreateRoleDTO): Promise<Role> {
    // Check if new name conflicts with existing roles (excluding current role)
    if (updateRoleDto.name) {
      const existingRole = await this.roleModel.findOne({
        name: updateRoleDto.name,
        _id: { $ne: id }
      });
      if (existingRole) {
        throw new ConflictException('Role name already exists');
      }
    }

    // Validate permissions if provided
    if (updateRoleDto.permissions && updateRoleDto.permissions.length > 0) {
      const permissions = await this.permissionModel.find({
        _id: { $in: updateRoleDto.permissions }
      });
      if (permissions.length !== updateRoleDto.permissions.length) {
        throw new NotFoundException('One or more permissions not found');
      }
    }

    const role = await this.roleModel.findByIdAndUpdate(
      id,
      updateRoleDto,
      { new: true }
    ).populate('permissions');

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    return role;
  }

  async deleteRole(id: string): Promise<Role> {
    const role = await this.roleModel.findByIdAndDelete(id);
    if (!role) {
      throw new NotFoundException('Role not found');
    }
    return role;
  }

  async assignPermissionsToRole(roleId: string, permissionIds: string[]): Promise<Role> {
    // Validate that all permission IDs exist
    const permissions = await this.permissionModel.find({
      _id: { $in: permissionIds }
    });
    if (permissions.length !== permissionIds.length) {
      throw new NotFoundException('One or more permissions not found');
    }

    const role = await this.roleModel.findByIdAndUpdate(
      roleId,
      { $addToSet: { permissions: { $each: permissionIds } } },
      { new: true }
    ).populate('permissions');

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    return role;
  }

  async removePermissionsFromRole(roleId: string, permissionIds: string[]): Promise<Role> {
    const role = await this.roleModel.findByIdAndUpdate(
      roleId,
      { $pull: { permissions: { $in: permissionIds } } },
      { new: true }
    ).populate('permissions');

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    return role;
  }

  async getRolesByNames(roleNames: string[]): Promise<Role[]> {
    return this.roleModel.find({
      name: { $in: roleNames }
    }).populate('permissions').exec();
  }
}
