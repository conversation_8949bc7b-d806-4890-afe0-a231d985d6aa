import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Type } from 'class-transformer';
import { IsArray, IsMongoId, IsNotEmpty, IsString, ValidateNested } from 'class-validator';
import { Document, Types } from 'mongoose';

class WorkflowParticipant {

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;  // User ID of the participant (linked to User collection)

  @Prop({ type: String, required: true })
  approvalType: string;  // Could be 'Approved By' or 'Reviewed By'
}

export type FieldRequestLogDocument = FieldRequestLog & Document;

@Schema({ timestamps: true, collection: 'fr_main' })
export class FieldRequestLog {

  @Prop({ type: Types.ObjectId, ref: 'FieldRequest', required: true })
  fieldRequestId: Types.ObjectId;  // Links to the Field Request

 @Prop([
     {
       item: { type: Number },
       description: { type: String },
       quantity: { type: Number },
       unit: { type: String },
       partNo: { type: String },
       priority: { type: String },
       rigName: { type: String}
     }
   ])
   items: Array<{
     item: number;
     description: string;
     quantity: number;
     unit: string;
     partNo: string;
     priority: string;
     rigName: string;
   }>;
 
   @Prop()
   serialNumber: string;
 
   @Prop()
   frType: string; //Material/Goods , Services
 
   @Prop()
   frNumber: string;
 
   @Prop()
   remark: string;
 
   @Prop([{ type: WorkflowParticipant }])  // Use the defined WorkflowParticipant class here
   workflowParticipants: WorkflowParticipant[];
 
   @Prop({
     attachments: [
       {
         remark: { type: String },
         type: { type: String },
         s3Url: { type: String },
       }
     ]
   })
   attachments: Array<{
     remark: string;
     type: string;
     s3Url: string;
   }>;
 
   @Prop()
   status: string; // e.g., "submitted", "approved", "rejected", "pending"
 
   @Prop({ type: Date })
   submittedAt: Date;  // When the PaymentAdvice was submitted
 
   @Prop({ type: Date })
   completedAt?: Date;  // When the PaymentAdvice workflow was completed
 
   @Prop({ type: Number })
   totalDuration?: number;  // Total duration (in hours) of the payment advice approval process
 
   @Prop({ type: Number })
   revision?: number;  // Total duration (in hours) of the payment advice approval process

   @Prop({ type: String })
   comment: string;
}

export const FieldRequestLogSchema = SchemaFactory.createForClass(FieldRequestLog);
// Ensure index is created for the serial number field
FieldRequestLogSchema.index({ serialNumber: 1 }, { unique: true });