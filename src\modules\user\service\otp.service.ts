// src/otp/otp.service.ts
import { Injectable, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as crypto from 'crypto';
import { Otp, OtpDocument } from '../schema/otp.schema';
import { User, UserDocument } from '../schema/user.schema';
import { UserService } from './user.service';
import { JwtService } from '@nestjs/jwt';
import { SecurityService } from './security.service';
import { EmailScheduler, EmailSchedulerDocument } from 'src/modules/common/schema/pa-email.schema';

@Injectable()
export class OtpService {
  constructor(
    @InjectModel(Otp.name) private readonly otpModel: Model<OtpDocument>,
    @InjectModel(User.name) private readonly userModel: Model<UserDocument>,
    @InjectModel(EmailScheduler.name) private emailSchedulerModel: Model<EmailSchedulerDocument>,
    private userService: UserService,
    private jwtService: JwtService,
    private securityService: SecurityService
  ) { }

  async requestOtp(encryptedData: string): Promise<void> {
    const decryptedData = this.securityService.decrypt(encryptedData);

    // Check if the user exists
    const user = await this.userModel.findOne({ email: decryptedData.email });
    if (!user) {
      throw new BadRequestException('Access Denied');
    }

    // Check existing OTP record
    const record = await this.otpModel.findOne({ email: decryptedData.email });

    if (record) {
      // Check if the account is locked
      if (record.lockExpiresAt && new Date() < record.lockExpiresAt) {
        throw new BadRequestException(
          'Account is locked due to too many failed attempts. Please try again later.'
        );
      }

      // Check if the retry count is at the limit
      if (record.retryCount >= 3) {
        throw new BadRequestException(
          'Retry limit reached. Please wait before requesting a new OTP.'
        );
      }
    }

    // Generate a 6-digit OTP
    const otp = crypto.randomInt(100000, 999999).toString();

    // Set OTP expiration to 30 minutes from now
    const expiresAt = new Date(Date.now() + 30 * 60 * 1000);

    // Save or update the OTP record in the database
    await this.otpModel.updateOne(
      { email: decryptedData.email },
      {
        otp,
        expiresAt,
        isVerified: false
      },
      { upsert: true }
    );

    // Sent OTP Email
    const emailScheduler1 = new this.emailSchedulerModel({
      to: decryptedData.email,
      subject: `Uzma Eco System: OTP`,
      body: `<p>Dear ${user.fullName}, <br/>Your one-time password (OTP) for secure login is: -
        <br/><br/><h3><b> ${otp}</b></h3> <br/>Please use this code to complete your login. The OTP is valid for the next 30 minutes.<br/><br/>If you did not request this code, please contact support team immediately.
<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
    });
    await emailScheduler1.save();

    // Send OTP via email (you can integrate a mail service like SendGrid, Nodemailer, etc.)
    // console.log(`OTP for ${decryptedData.email}: ${otp}`);
  }

  async verifyOtp(encryptedData: string): Promise<any> {
    const decryptedData = this.securityService.decrypt(encryptedData);
    const record = await this.otpModel.findOne({ email: decryptedData.email });

    if (!record) {
      throw new BadRequestException('Invalid OTP request.');
    }

    // Check if the account is locked
    if (record.lockExpiresAt && new Date() < record.lockExpiresAt) {
      throw new BadRequestException('Account is locked. Please try again later.');
    }

    // Validate OTP
    if (record.otp !== decryptedData.otp) {
      await this.handleFailedAttempt(record);
      throw new BadRequestException('Invalid OTP.');
    }

    // Check OTP expiration
    if (new Date() > record.expiresAt) {
      throw new BadRequestException('OTP expired.');
    }

    // Reset retry count and lock status
    await this.otpModel.updateOne(
      { email: decryptedData.email },
      { isVerified: true, retryCount: 0, lockExpiresAt: null }
    );

    const user = await this.userService.validateOTP(decryptedData.email);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const payload = {
      user: {
        userId: user._id,
        userName: user.username,
        userEmail: user.email,
        userFaceId: user.faceId
      }
    };
    const token = this.jwtService.sign(payload);
    const data = { user, token };

    return { data: this.securityService.encrypt(data) };
  }

  private async handleFailedAttempt(record: OtpDocument) {
    const maxRetries = 3;
    const lockDuration = 30 * 60 * 1000; // 30 minutes in milliseconds

    // Increment retry count
    record.retryCount += 1;

    if (record.retryCount >= maxRetries) {
      // Lock the account for 30 minutes
      record.lockExpiresAt = new Date(Date.now() + lockDuration);
      record.retryCount = 0; // Reset retry count after lock
      await record.save();
      throw new BadRequestException('Account locked due to too many failed attempts. Please try again later.');
    }
    await record.save();
  }

}
