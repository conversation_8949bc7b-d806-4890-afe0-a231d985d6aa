import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from 'src/modules/user/schema/user.schema';

@Schema({ timestamps: true, collection: 'project_cost_main' }) // Custom collection name
export class ProjectCostMain extends Document {
  @Prop({ required: true })
  approvedBudget: number;

  @Prop({ required: true })
  budgetUsed: number;

  @Prop({ required: true })
  invoice: number;

  @Prop()
  costSaving: number;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  updatedBy: Types.ObjectId | User;
}

export const ProjectCostMainSchema = SchemaFactory.createForClass(ProjectCostMain);
