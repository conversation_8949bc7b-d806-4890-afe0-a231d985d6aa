import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuditLog, AuditLogSchema } from './schema/audit-log.schema';
import { ProjectBP, ProjectBPSchema } from './schema/project-bp.schema';
import { ProjectCostDetail, ProjectCostDetailSchema } from './schema/project-cost-detail.schema';
import { ProjectCostMain, ProjectCostMainSchema } from './schema/project-cost-main.schema';
import { ProjectDiscount, ProjectDiscountSchema } from './schema/project-discount.schema';
import { ProjectGallery, ProjectGallerySchema } from './schema/project-gallery.schema';
import { ProjectInsight, ProjectInsightSchema } from './schema/project-insight.schema';
import { ProjectPO, ProjectPOSchema } from './schema/project-po.schema';
import { ProjectProgress, ProjectProgressSchema } from './schema/project-progress.schema';
import { ProjectPV, ProjectPVSchema } from './schema/project-pv.schema';
import { ProjectRemark, ProjectRemarkSchema } from './schema/project-remark.schema';
import { ProjectReport, ProjectReportSchema } from './schema/project-report.schema';
import { Payment, PaymentSchema } from './schema/pv.schema';
import { RPAWebhook, RPAWebhookSchema } from './schema/rpa-webhook.schema';
import { SageReportRPA, SageReportRPASchema } from './schema/sage-pv.schema';
import { ProjectCostDetailController } from './controller/project-cost-detail.controller';
import { ProjectCostMainController } from './controller/project-cost-main.controller';
import { ProjectGalleryController } from './controller/project-gallery.controller';
import { ProjectInsightController } from './controller/project-insight.controller';
import { ProjectPOController } from './controller/project-po.controller';
import { ProjectProgressController } from './controller/project-progress.controller';
import { ProjectPVController } from './controller/project-pv.controller';
import { ProjectReportController } from './controller/project-report.controller';
import { RPAApiController } from './controller/rpa-api.controller';
import { SageReportRPAController } from './controller/sage-report-rpa.controller';
import { ProjectCostDetailService } from './service/project-cost-detail.service';
import { ProjectCostMainService } from './service/project-cost-main.service';
import { ProjectGalleryService } from './service/project-gallery.service';
import { ProjectInsightService } from './service/project-insight.service';
import { ProjectPOService } from './service/project-po.service';
import { ProjectProgressService } from './service/project-progress.service';
import { ProjectPVService } from './service/project-pv.service';
import { ProjectReportService } from './service/project-report.service';
import { SeahPVService } from './service/pv.service';
import { RPAApiService } from './service/rpa-api.service';
import { SageReportRPAService } from './service/sage-report-rpa.service';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [
    MongooseModule.forFeature([
       { name: AuditLog.name, schema: AuditLogSchema},
       { name: ProjectBP.name, schema: ProjectBPSchema},
       { name: ProjectCostDetail.name, schema: ProjectCostDetailSchema},
       { name: ProjectCostMain.name, schema: ProjectCostMainSchema },
       { name: ProjectDiscount.name, schema: ProjectDiscountSchema },
       { name: ProjectGallery.name, schema: ProjectGallerySchema },
       { name: ProjectInsight.name, schema: ProjectInsightSchema },
       { name: ProjectPO.name, schema: ProjectPOSchema },
       { name: ProjectProgress.name, schema: ProjectProgressSchema },
       { name: ProjectPV.name, schema: ProjectPVSchema },
       { name: ProjectRemark.name, schema: ProjectRemarkSchema },
       { name: ProjectReport.name, schema: ProjectReportSchema },
       { name: Payment.name, schema: PaymentSchema},
       { name: RPAWebhook.name, schema: RPAWebhookSchema },
       { name: SageReportRPA.name, schema: SageReportRPASchema}
    ]),
    CommonModule
  ],
  controllers: [ProjectCostDetailController, ProjectCostMainController, ProjectGalleryController, ProjectInsightController, ProjectPOController, ProjectProgressController, ProjectPVController, ProjectReportController, ProjectPVController, RPAApiController, SageReportRPAController],
  providers: [ProjectCostDetailService, ProjectCostMainService, ProjectGalleryService, ProjectInsightService, ProjectPOService, ProjectProgressService, ProjectPVService, ProjectReportService, SeahPVService, RPAApiService, SageReportRPAService],
  exports: [],
})
export class SeahModule {}