import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

// Define the team stats interface
interface TeamStats {
  totalTeamMembers: number;
  submittedByTeam: number;
  pendingFromTeam: number;
  completedByTeam: number;
}

@Schema({ timestamps: true, collection: 'ippa_dashboard_stats' })
export class DashboardStats {
  _id: Types.ObjectId;

  @Prop({ required: true, index: true })
  userId: Types.ObjectId;

  @Prop({ required: true, index: true })
  userRole: string; // 'employee', 'supervisor', 'hr', 'hod'

  @Prop({ required: true, index: true })
  financialYear: string;

  @Prop()
  department?: string;

  // Stats data
  @Prop({ required: true })
  totalSubmissions: number;

  @Prop({ required: true })
  approved: number;

  @Prop({ required: true })
  pendingReview: number;

  @Prop({ required: true })
  completed: number;

  @Prop({ required: true })
  submitted: number;

  @Prop({ required: true })
  draft: number;

  // For supervisors - team stats
  @Prop({ 
    type: Object,
    default: undefined,
    required: false 
  })
  teamStats?: TeamStats;

  // Last updated
  @Prop({ default: Date.now })
  lastUpdated: Date;

  @Prop({ default: true })
  isActive: boolean;
}

export type DashboardStatsDocument = DashboardStats & Document;
export const DashboardStatsSchema = SchemaFactory.createForClass(DashboardStats);