import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MigrationController } from './controller/migration.controller';
import { MigrationService } from './service/migration.service';
import { User, UserSchema } from '../user/schema/user.schema';

@Module({
  imports: [
     MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
  ],
  controllers: [MigrationController],
  providers: [MigrationService],
  exports: [],
})
export class MigrationModule {}