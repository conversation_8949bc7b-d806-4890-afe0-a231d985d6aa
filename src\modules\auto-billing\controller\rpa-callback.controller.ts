import { Controller, Post, Body, HttpCode, Headers, UnauthorizedException, Logger, MaxFileSizeValidator, ParseFilePipe, UploadedFile, UseInterceptors } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RpaCallbackService } from '../service/rpa-callback.service';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('rpa-callback')
export class RpaCallbackController {
  private readonly logger = new Logger(RpaCallbackController.name);
  
  constructor(
    private readonly rpaCallbackService: RpaCallbackService,
    private readonly configService: ConfigService,
  ) {}

  @Post('document-created')
  @UseInterceptors(FileInterceptor('file'))
  @HttpCode(200)
  async handleDocumentCreated(
    // @Headers('authorization') authHeader: string,
    @Body() callbackData: any,
    @UploadedFile() file: Express.Multer.File,
    // @UploadedFile(
    //   new ParseFilePipe({
    //     validators: [
    //       new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 }), // 10MB max
    //       // new FileTypeValidator({ fileType: '.(xlsx|xls)' }),
    //     ],
    //   }),
    // ) file?: Express.Multer.File,
  ) {
    // Verify the authorization header
    // this.verifyCallbackAuth(authHeader);
    
    this.logger.log(`Received RPA callback: ${JSON.stringify(callbackData)}`);
    
    try {
      // Process the callback based on document type
      const result = await this.rpaCallbackService.processDocumentCallback(callbackData, file);
      return { success: true, message: 'Callback processed successfully', data: result };
    } catch (error) {
      this.logger.error(`Error processing RPA callback: ${error.message}`, error.stack);
      // Return 200 to prevent RPA from retrying, but log the error
      return { success: false, message: error.message };
    }
  }

  /**
   * Verify the authentication token from the RPA system
   */
  private verifyCallbackAuth(authHeader: string): void {
    const expectedToken = this.configService.get<string>('RPA_CALLBACK_TOKEN');
    
    if (!authHeader) {
      this.logger.warn('Missing authorization header in RPA callback');
      throw new UnauthorizedException('Missing authorization header');
    }
    
    const [scheme, token] = authHeader.split(' ');
    
    if (scheme !== 'Bearer' || !token) {
      this.logger.warn('Invalid authorization format in RPA callback');
      throw new UnauthorizedException('Invalid authorization format');
    }
    
    if (token !== expectedToken) {
      this.logger.warn('Invalid token in RPA callback');
      throw new UnauthorizedException('Invalid token');
    }
  }
}