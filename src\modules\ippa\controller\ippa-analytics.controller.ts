// ippa-analytics.controller.ts
import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { IppaAnalyticsService } from '../service/ippa-analytics.service';

@Controller('ippa/analytics')
// @UseGuards(JwtAuthGuard)
export class IppaAnalyticsController {
  constructor(private readonly analyticsService: IppaAnalyticsService) {}

  @Get('department-submissions')
  async getDepartmentSubmissionAnalysis(
    @Query('financialYear') financialYear?: string,
    @Query('status') status?: string,
  ) {
    return this.analyticsService.getDepartmentSubmissionAnalysis(
      financialYear,
      status,
    );
  }

  @Post('dashboard-summary')
  async getDashboardSummary(@Body() encryptedData: any) {
    return this.analyticsService.getDashboardSummary(encryptedData.data);
  }
}