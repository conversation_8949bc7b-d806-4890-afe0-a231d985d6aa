import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

class WorkflowParticipant {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;  // User ID of the participant (linked to User collection)

  @Prop({ type: String, required: true })
  approvalType: string;  // Could be 'Approved By' or 'Reviewed By'
}

export type FieldRequestDocument = FieldRequest & Document;

@Schema({ timestamps: true, collection: 'fr_main' })
export class FieldRequest {

  @Prop()
  companyName: string;

  @Prop()
  companyAddress: string;

  @Prop()
  companyLogo: string;

  @Prop()
  currency: string;

  // Establish relationship with the Department collection
  @Prop({ type: Types.ObjectId, ref: 'Department', required: true })
  departmentCode: Types.ObjectId;  // Linked to Department collection

  @Prop([
    {
      item: { type: Number },
      description: { type: String },
      quantity: { type: Number },
      unit: { type: String },
      partNo: { type: String },
      priority: { type: String },
      rigName: { type: String }
    }
  ])
  items: Array<{
    item: number;
    description: string;
    quantity: number;
    unit: string;
    partNo: string;
    priority: string;
    rigName: string;
  }>;

  @Prop()
  serialNumber: string;

  @Prop()
  frType: string; //Material/Goods , Services

  @Prop()
  frNumber: string;

  @Prop()
  projectCode: string;

  @Prop()
  date: string;

  @Prop()
  remark: string;

  @Prop([{ type: WorkflowParticipant }])  // Use the defined WorkflowParticipant class here
  workflowParticipants: WorkflowParticipant[];

  @Prop({
    attachments: [
      {
        remark: { type: String },
        type: { type: String },
        s3Url: { type: String },
      }
    ]
  })
  attachments: Array<{
    remark: string;
    type: string;
    s3Url: string;
  }>;

  @Prop()
  status: string; // e.g., "submitted", "approved", "rejected", "pending"

  @Prop({ type: Date })
  submittedAt: Date;  // When the PaymentAdvice was submitted

  @Prop({ type: Date })
  completedAt?: Date;  // When the PaymentAdvice workflow was completed

  @Prop({ type: Number })
  totalDuration?: number;  // Total duration (in hours) of the payment advice approval process

  @Prop({ type: Number })
  revision?: number;  // Total duration (in hours) of the payment advice approval process

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  createdBy: Types.ObjectId;  // User who created the Payment Advice
}

export const FieldRequestSchema = SchemaFactory.createForClass(FieldRequest);
// Ensure index is created for the serial number field
FieldRequestSchema.index({ serialNumber: 1 }, { unique: true });