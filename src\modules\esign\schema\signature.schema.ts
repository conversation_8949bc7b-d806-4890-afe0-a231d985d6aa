import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type SignatureDocument = Signature & Document;

@Schema({ timestamps: true , collection: 'e_sign_signature'})
export class Signature {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  filename: string;

  @Prop({ required: true })
  url: string;

  @Prop({ required: true })
  key: string;

  @Prop({ required: true })
  mimeType: string;

  @Prop()
  defaultWidth: number;

  @Prop()
  defaultHeight: number;

  @Prop()
  defaultOpacity: number;

  @Prop({ default: false })
  isActive: boolean;

  @Prop({ default: true })
  isEnabled: boolean;
}

export const SignatureSchema = SchemaFactory.createForClass(Signature);