import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CommonModule } from '../common/common.module';
import { IppaUser, IppaUserSchema } from './schema/ippa-user.schema';
import { IppaUserController } from './controller/ippa-user.controller';
import { IppaUserService } from './service/ippa-user.service';
import { User, UserSchema } from '../user/schema/user.schema';
import { Department, DepartmentSchema } from '../user/schema/department.schema';
import { IppaEmployeeController } from './controller/ippa-employee.controller';
import { IppaEmployeeService } from './service/ippa-employee.service';
import { IPPASubmission, IPPASubmissionSchema } from './schema/ippa-submission.schema';
import { IPPAUserRole, IPPAUserRoleSchema } from './schema/ippa-role.schema';
import { IppaSupervisorController, IppaUserRoleController, IppaWorkflowController } from './controller/ippa-supervisor.controller';
import { IppaSupervisorService } from './service/ippa-supervisor.service';
import { IppaWorkflowService } from './service/ippa-workflow.service';
import { IppaAnalyticsController } from './controller/ippa-analytics.controller';
import { IppaAnalyticsService } from './service/ippa-analytics.service';
import { IPPAMessage, IPPAMessageSchema } from './schema/ippa-message.schema';
import { IppaMessageController } from './controller/ippa-message.controller';
import { IPPAMessageService } from './service/ippa-message.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: IppaUser.name, schema: IppaUserSchema },
      { name: User.name, schema: UserSchema },
      { name: Department.name, schema: DepartmentSchema },
      { name: IPPASubmission.name, schema: IPPASubmissionSchema },
      { name: IPPAUserRole.name, schema: IPPAUserRoleSchema },
      { name: IPPAMessage.name, schema: IPPAMessageSchema },
      // { name: DashboardStats.name, schema: DashboardStatsSchema },
      // { name: DepartmentIPPAStats.name, schema: DepartmentIPPAStatsSchema },
    ]),
    CommonModule
  ],
  controllers: [IppaUserController, IppaEmployeeController, IppaSupervisorController, IppaWorkflowController, IppaUserRoleController, IppaAnalyticsController, IppaMessageController],
  providers: [IppaUserService, IppaEmployeeService, IppaSupervisorService, IppaWorkflowService, IppaAnalyticsService, IPPAMessageService],
  exports: [IppaUserService, IppaEmployeeService, IppaSupervisorService, IppaWorkflowService, IppaAnalyticsService, IPPAMessageService],
})
export class IPPAModule {}