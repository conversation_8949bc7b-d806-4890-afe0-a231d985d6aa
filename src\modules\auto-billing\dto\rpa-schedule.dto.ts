import { IsString, <PERSON>NotEmpt<PERSON>, Is<PERSON>num, IsO<PERSON>al, IsN<PERSON>ber, IsDateString, IsObject } from 'class-validator';
import { RpaTaskType } from '../schema/rpa-schedule.schema';

export class ScheduleTaskDto {
  @IsEnum(RpaTaskType)
  @IsNotEmpty()
  taskType: RpaTaskType;

  @IsString()
  @IsNotEmpty()
  billingId: string;

  @IsNumber()
  @IsOptional()
  priority?: number;

  @IsDateString()
  @IsOptional()
  scheduledAt?: string;

  @IsObject()
  @IsOptional()
  additionalData?: Record<string, any>;
}

export class ScheduleSequentialTasksDto {
  @IsString()
  @IsNotEmpty()
  billingId: string;
}

export class GetTasksByBillingIdDto {
  @IsString()
  @IsNotEmpty()
  billingId: string;
}

export class GetTaskStatusDto {
  @IsString()
  @IsNotEmpty()
  taskId: string;
}

export class CancelTaskDto {
  @IsString()
  @IsNotEmpty()
  taskId: string;
}