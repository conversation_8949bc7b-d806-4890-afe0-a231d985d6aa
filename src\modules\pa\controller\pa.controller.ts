import { Controller, Post, Body, Get, Query, Put, Param, UploadedFile, UseInterceptors, NotFoundException, Patch, UsePipes, ValidationPipe, Res } from '@nestjs/common';
import { ApproveRejectWorkflowDto } from '../dto/workflow.dto';
import { PaymentAdviceService } from '../service/pa.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { UpdateBankReceiptsDto } from '../dto/update-receipt.dto';
import { PaymentAdviceReportDto } from '../dto/pa-report.dto';
import { Response } from 'express';
import { S3Service } from 'src/modules/common/services/s3.service';

@Controller('payment-advice')
export class PaymentAdviceController {
  constructor(private readonly paymentAdviceService: PaymentAdviceService, private readonly s3service : S3Service) {}

  // Create a new Payment Advice
  @Post('create')
  async create(@Body() encryptedData: any) {
    return this.paymentAdviceService.createPaymentAdvice(encryptedData.data);
  }

  @Post('dashboard')
  async dashboard(@Body() encryptedData: any) {
    return this.paymentAdviceService.getAnalysis(encryptedData.data);
  }

  // Get the list of Payment Advice with filtering and pagination
  @Post('list')
  async list(@Body() encryptedData: any) {
    return this.paymentAdviceService.getPaymentAdviceList(encryptedData.data);
  }

  @Post('detail')
  async getPaymentAdviceById(@Body() encryptedData: any) {
    try {
      return await this.paymentAdviceService.getPaymentAdviceById(encryptedData.data);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException('Error retrieving Payment Advice');
    }
  }

  // Approve or reject a Payment Advice workflow step
  @Put('workflow/approve-reject')
  async approveReject(@Body() dto: ApproveRejectWorkflowDto) {
    return this.paymentAdviceService.approveRejectPaymentAdvice(dto);
  }

  @Post('attachment')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    return await this.s3service.uploadPaymentAdviceAttachment(file);
  }

  @Patch('bank-receipts')
  async updateBankReceipts(@Body() dto: UpdateBankReceiptsDto) {
    return this.paymentAdviceService.updateBankReceipts(dto);
  }

  @Post('assign-pa-serialno')
  async assignPASerialNo() {
    return this.paymentAdviceService.assignSerialNumbers();
  }

  @Post('report')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getReport(@Body() body) {
    const encryptedData = body?.data || body; // Handle the case where encryptedData is in a 'data' property
    return this.paymentAdviceService.generateReportByDepartmentAPI(encryptedData);
  }

  @Post('report/download')
  @UsePipes(new ValidationPipe({ transform: true }))
  async downloadReport(@Body() body: any, @Res() res: Response) {
    const encryptedData = body?.data || body; // Handle the case where encryptedData is in a 'data' property
    return this.paymentAdviceService.downloadExcelReport(res, encryptedData);
  }

  @Get('sage/payment')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getPayment() {
   var data  = { company: 'UZEN', startDate: '19/03/2025', endDate: '19/03/2025', department: ''};
   return data;
  }
}
