// In src/billing/schema/billing-finance.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type BillingFinanceDocument = BillingFinance & Document;

@Schema({ timestamps: true })
class FinanceApprover {
  @Prop({ required: true, type: Types.ObjectId, ref: 'User' })
  userId: Types.ObjectId;
  
  @Prop({ required: true })
  role: string; // e.g., 'finance-manager', 'finance-director', 'accountant'

  @Prop({ required: true })
  order: number;

  @Prop({ default: true })
  isActive: boolean;
}

export const FinanceApproverSchema = SchemaFactory.createForClass(FinanceApprover);

@Schema({ timestamps: true, collection: 'billing_finance' })
export class BillingFinance extends Document {
  @Prop({ required: true, unique: true })
  financeFlowId: string;

  @Prop({ type: Types.ObjectId, ref: 'Project', required: false })
  project?: Types.ObjectId; // Optional - if null, applies to all projects

  @Prop({ type: Types.ObjectId, ref: 'Department', required: false })
  department?: Types.ObjectId; // Optional - if null, applies to all departments

  @Prop({ type: [FinanceApproverSchema], required: true })
  approvers: FinanceApprover[];

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: 'sequential' })
  approvalType: string; // 'sequential' or 'parallel'

  @Prop()
  description?: string;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop()
  updatedAt?: Date;
}

export const BillingFinanceSchema = SchemaFactory.createForClass(BillingFinance);