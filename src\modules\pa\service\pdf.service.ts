// import { Injectable } from '@nestjs/common';
// import * as fs from 'fs';
// import { PDFDocument } from 'pdf-lib';
// import { fromPath } from 'pdf2pic';
// import * as sharp from 'sharp';

// @Injectable()
// export class PdfService {
//   async convertPdfToImageAndBack(inputPath: string, outputPath: string): Promise<void> {
//     // Get total pages in the PDF
//     const totalPages = await this.getPdfPageCount(inputPath);

//     // Convert PDF to images
//     const images = await this.convertPdfToImages(inputPath, totalPages);

//     // Create a new PDF with image-based pages
//     await this.createImageBasedPdf(images, outputPath);

//     console.log(`Image-only PDF created at ${outputPath}`);
//   }

//   private async getPdfPageCount(pdfPath: string): Promise<number> {
//     const pdfBuffer = fs.readFileSync(pdfPath);
//     const pdfDoc = await PDFDocument.load(pdfBuffer);
//     return pdfDoc.getPageCount();
//   }

//   private async convertPdfToImages(pdfPath: string, totalPages: number): Promise<string[]> {
//     const pdf2pic = fromPath(pdfPath, {
//       density: 300, // DPI for high-quality images
//       format: 'png',
//       saveFilename: 'page',
//       savePath: './tmp', // Temporary directory for images
//     });

//     const imagePaths: string[] = [];
//     for (let page = 1; page <= totalPages; page++) {
//       const image = await pdf2pic(page); // Use `pdf2pic` function
//       imagePaths.push(image.path);
//     }

//     return imagePaths;
//   }

//   private async createImageBasedPdf(imagePaths: string[], outputPath: string): Promise<void> {
//     const pdfDoc = await PDFDocument.create();

//     for (const imagePath of imagePaths) {
//       const imageBuffer = fs.readFileSync(imagePath);

//       // Resize image to A4 size (optional, depending on your requirements)
//       const resizedImageBuffer = await sharp(imageBuffer)
//         .resize({ width: 2480, height: 3508 }) // A4 dimensions in pixels at 300 DPI
//         .toBuffer();

//       const image = await pdfDoc.embedPng(resizedImageBuffer);
//       const page = pdfDoc.addPage([image.width, image.height]);
//       page.drawImage(image, {
//         x: 0,
//         y: 0,
//         width: image.width,
//         height: image.height,
//       });
//     }

//     const pdfBytes = await pdfDoc.save();
//     fs.writeFileSync(outputPath, pdfBytes);
//   }
// }
