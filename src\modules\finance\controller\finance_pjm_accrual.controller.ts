import { Controller, Get, Post, Put, Delete, Param, Body } from '@nestjs/common';
import { PJMAccrual } from '../schema/finance_pjm_accrual';
import { PJMAccrualService } from '../service/finance_pjm_accrual.service';

@Controller('finance/accrual')
export class PJMAccrualController {
  constructor(private readonly pjmAccrualService: PJMAccrualService) {}

  @Post()
  async create(@Body() pjmAccrual: PJMAccrual) {
    return this.pjmAccrualService.create(pjmAccrual);
  }

  @Get()
  async findAll() {
    return this.pjmAccrualService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.pjmAccrualService.findOne(id);
  }

  @Put(':id')
  async update(@Param('id') id: string, @Body() updateData: Partial<PJMAccrual>) {
    return this.pjmAccrualService.update(id, updateData);
  }

  @Delete(':id')
  async delete(@Param('id') id: string) {
    return this.pjmAccrualService.delete(id);
  }
}
