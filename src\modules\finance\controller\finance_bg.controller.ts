import { Body, Controller, Delete, Get, HttpStatus, Param, ParseIntPipe, Patch, Post, Query, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FinanceBGService } from '../service/finance_bg.service';
import { FinanceBG } from '../schema/finance_bg';

@Controller('bg')
export class FinanceBGController {
  constructor(private readonly financeBGService: FinanceBGService) { }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async importPayments(@UploadedFile() file: Express.Multer.File): Promise<string> {
    return await this.financeBGService.importFinanceBG(file.buffer);
  }

  @Post('getAll')
  async getAllYGL01(
    @Body() encryptedData: any
  ){
    return await this.financeBGService.getAllFinanceBG();
  }

  @Post('create')
  async create(@Body() encryptedData: any): Promise<FinanceBG> {
    return await this.financeBGService.createFinanceBG(encryptedData.data);
  }

  // @Post()
  // async findAll(@Query('page') page = 1, @Query('limit') limit = 10): Promise<any> {
  //   return await this.financeBGService.getAllFinanceBG(page, limit);
  // }

  @Post('getById')
  async findById(@Body() encryptedData: any): Promise<any> {
    return await this.financeBGService.getFinanceBGById(encryptedData.data);
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateData: any): Promise<FinanceBG> {
    return await this.financeBGService.updateFinanceBG(id, updateData);
  }

  @Delete(':id')
  async delete(@Param('id') id: string): Promise<{ deleted: boolean }> {
    return await this.financeBGService.deleteFinanceBG(id);
  }
}
