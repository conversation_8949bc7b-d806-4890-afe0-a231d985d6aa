// src/project-progress/dto/project-progress.dto.ts
import { IsNotEmpty, IsNumber, IsOptional, IsDateString, IsMongoId } from 'class-validator';

export class CreateProjectProgressDto {
  @IsNumber()
  @IsNotEmpty()
  percentage: number;

  @IsNumber()
  @IsNotEmpty()
  baseline: number;

  @IsOptional()
  variance: number;

  @IsOptional()
  liveDate: string;

  @IsMongoId()
  @IsOptional()
  updatedBy?: string; // Optional user reference
}

export class UpdateProjectProgressDto {
  @IsNumber()
  @IsOptional()
  percentage?: number;

  @IsNumber()
  @IsOptional()
  baseline?: number;

  @IsNumber()
  @IsOptional()
  variance?: number;

  @IsDateString()
  @IsOptional()
  liveDate?: string;

  @IsMongoId()
  @IsOptional()
  updatedBy?: string; // Optional user reference
}
