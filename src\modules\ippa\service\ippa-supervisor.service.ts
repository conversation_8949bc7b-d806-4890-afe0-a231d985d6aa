// ippa-supervisor.service.ts
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  IPPASubmission,
  IPPASubmissionDocument,
  IPPAStatus,
  ReviewerType,
} from '../schema/ippa-submission.schema';
import { IppaUser, IppaUserDocument } from '../schema/ippa-user.schema';
import {
  Department,
  DepartmentDocument,
} from 'src/modules/user/schema/department.schema';
import { User, UserDocument } from 'src/modules/user/schema/user.schema';
import { SecurityService } from 'src/modules/user/service/security.service';

interface SupervisorApprovalRequest {
  submissionId: string;
  supervisorEmail: string;
  objectives: Array<{
    index: number;
    supervisorRating: number;
    supervisorComments: string;
    supervisorWeightedPoints: number;
    uploadedAt: string | Date; // This property is missing
  }>;
  supervisorRemarks: string; // Mandatory
  supervisorAttachments?: Array<{
    fileName: string;
    filePath: string;
    fileSize: number;
    mimeType: string;
    description?: string;
    uploadedAt: Date; // This property is missing
  }>;
}

interface HODApprovalRequest {
  submissionId: string;
  hodEmail: string;
  objectives?: Array<{
    index: number;
    hodRating?: number;
    hodComments?: string;
    hodWeightedPoints?: number;
  }>;
  hodRemarks?: string; // Optional
  hodAttachments?: Array<{
    fileName: string;
    filePath: string;
    fileSize: number;
    mimeType: string;
    description?: string;
    uploadedAt: Date;
  }>;
}

interface HRApprovalRequest {
  submissionId: string;
  hrEmail: string;
  hrRemarks?: string; // Optional
  hrAttachments?: Array<{
    fileName: string;
    filePath: string;
    fileSize: number;
    mimeType: string;
    description?: string;
    uploadedAt: Date;
  }>;
  isCompleted: boolean; // HR marks as completed
}

@Injectable()
export class IppaSupervisorService {
  constructor(
    @InjectModel(IPPASubmission.name)
    private ippaSubmissionModel: Model<IPPASubmissionDocument>,
    @InjectModel(IppaUser.name)
    private ippaUserModel: Model<IppaUserDocument>,
    @InjectModel(User.name)
    private userModel: Model<UserDocument>,
    @InjectModel(Department.name)
    private departmentModel: Model<DepartmentDocument>,
    private securityService: SecurityService
  ) {}

  /**
   * Get submissions requiring approval for supervisor/HOD/HR
   */
  async getToApproveSubmissions(
    approverEmail: string,
    page: number = 1,
    limit: number = 10,
    filters?: {
      status?: IPPAStatus;
      financialYear?: string;
      search?: string;
    },
  ) {
    // Get user role information
    const userRole = await this.getUserIPPARoleRaw(approverEmail);
    // console.log("user role ", userRole)
    // Build query based on approver role
    const queryFilter: any = {
      $or: [
        // Line Manager approvals
        {
          currentReviewerId: userRole.user.id,
          'approvalWorkflow.personId': userRole.user.id,
          status: IPPAStatus.PENDING_SUPERVISOR_REVIEW,
          'approvalWorkflow.stepType': 'supervisor',
        },
        // HOD approvals (if they are HOD)
        {
          status: IPPAStatus.PENDING_HOD_REVIEW,
          'approvalWorkflow.stepType': 'hod',
          'approvalWorkflow.personId': userRole.user.id,
          'approvalWorkflow.status': 'pending',
        },
        // HR approvals (if they are HR)
        {
          status: IPPAStatus.PENDING_HR_REVIEW,
          'approvalWorkflow.stepType': 'hr',
          'approvalWorkflow.personId': userRole.user.id,
          'approvalWorkflow.status': 'pending',
        },
      ],
    };

    // Add additional filters
    if (filters?.status) {
      queryFilter.status = filters.status;
    }
    if (filters?.financialYear) {
      queryFilter.financialYear = filters.financialYear;
    }
    if (filters?.search) {
      queryFilter.$and = queryFilter.$and || [];
      queryFilter.$and.push({
        $or: [
          { ippaId: { $regex: filters.search, $options: 'i' } },
          { employeeName: { $regex: filters.search, $options: 'i' } },
          { submissionDepartment: { $regex: filters.search, $options: 'i' } },
        ],
      });
    }

    const totalCount =
      await this.ippaSubmissionModel.countDocuments(queryFilter);
    const skip = (page - 1) * limit;

    const submissions = await this.ippaSubmissionModel
      .find(queryFilter)
      .populate('employeeId', 'fullName email department position')
      .sort({ submittedAt: 1 }) // Oldest first for approvals
      .skip(skip)
      .limit(limit)
      .lean()
      .exec();

    // Format submissions with approval context
    const formattedSubmissions = submissions.map((submission) => {
      const approverRole = this.getApproverRole(submission, userRole.user.id);
      const canApprove = this.canApprove(submission, userRole.user.id);

      return {
        ...submission,
        approverRole,
        canApprove,
        daysPending: this.calculateDaysPending(submission),
        isOverdue: this.isSubmissionOverdue(submission),
        nextAction: this.getNextActionForApprover(submission, approverRole),
      };
    });

    return {
      submissions: formattedSubmissions,
      totalCount,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalCount / limit),
        pageSize: limit,
        hasNextPage: page < Math.ceil(totalCount / limit),
        hasPrevPage: page > 1,
      },
      approverInfo: {
        name: userRole.user.fullName,
        email: userRole.user.email,
        department: userRole.user.department,
        roles: userRole.roles,
      },
    };
  }

  /**
   * Get user's IPPA role and permissions
   */
  // async getUserIPPARole(userEmail: string): Promise<any> {
  //   // Find the user
  //   const ppaUser = await this.ippaUserModel
  //     .findOne({ email: userEmail })
  //     .populate('department lineManager')
  //     .lean();

  //   const mainUser = await this.userModel
  //     .findOne({ email: userEmail })
  //     .populate('department')
  //     .lean();

  //   if (!ppaUser || !mainUser) {
  //     throw new Error('User not found');
  //   }

  //   // Check if user is a line manager (has reportees)
  //   const reportees = await this.ippaUserModel
  //     .find({ lineManager: mainUser!._id })
  //     .select('fullName email department departmentCode')

  //     .lean();
  //   // console.log('main user ', mainUser._id);

  //   // Check if user is HOD for any department
  //   const hodDepartments = await this.departmentModel
  //     .find({
  //       hodPersonId: new Types.ObjectId(mainUser!._id),
  //       isActive: true,
  //     })
  //     .select('code description')
  //     .lean();

  //   // Check if user is HR (you can define HR role based on department or specific role)
  //   // const isHR = (user.department as any).code === 'HR' || user.roles?.includes('hr');
  //   const isHR = mainUser!._id.toString() === '674829e0f3d796aa36ff1e0d';

  //   // Determine user roles
  //   const roles = ['employee']; // Everyone is an employee

  //   if (reportees.length > 0) {
  //     roles.push('supervisor');
  //   }

  //   if (hodDepartments.length > 0) {
  //     roles.push('hod');
  //   }

  //   if (isHR) {
  //     roles.push('hr');
  //   }

  //   // Define permissions based on roles
  //   const permissions = {
  //     canSubmitIPPA: true,
  //     canApproveAsLineManager: reportees.length > 0,
  //     canApproveAsHOD: hodDepartments.length > 0,
  //     canReviewAsHR: isHR,
  //     canViewAllSubmissions: isHR,
  //     canViewDepartmentSubmissions: hodDepartments.length > 0,
  //     canViewOwnSubmissions: true,
  //     canViewReporteeSubmissions: reportees.length > 0,
  //   };

  //   return {
  //     user: {
  //       id: mainUser._id,
  //       email: mainUser.email,
  //       fullName: mainUser.fullName,
  //       department: ppaUser.department,
  //     },
  //     roles,
  //     permissions,
  //     isLineManager: reportees.length > 0,
  //     isHOD: hodDepartments.length > 0,
  //     isHR,
  //     managedDepartments: hodDepartments,
  //     reportees: reportees.map((r) => ({
  //       id: r._id,
  //       fullName: r.fullName,
  //       email: r.email,
  //       department: r.department,
  //       departmentCode: r.departmentCode
  //     })),
  //     hodDepartmentCodes: hodDepartments.map((d) => d.code),
  //   };
  // }

  // async getUserIPPARole(userEmail: string): Promise<any> {
  //   // Find the user
  //   const ppaUser = await this.ippaUserModel
  //     .findOne({ email: userEmail })
  //     .populate('department lineManager')
  //     .lean();

  //   const mainUser = await this.userModel
  //     .findOne({ email: userEmail })
  //     .populate('department')
  //     .lean();

  //   if (!ppaUser || !mainUser) {
  //     throw new Error('User not found');
  //   }

  //   // Check if user is a line manager (has reportees)
  //   const reportees = await this.ippaUserModel
  //     .find({ lineManager: mainUser!._id })
  //     .select('fullName email department departmentCode')
  //     .lean();

  //   // Check if user is HOD for any department
  //   const hodDepartments = await this.departmentModel
  //     .find({
  //       hodPersonId: new Types.ObjectId(mainUser!._id),
  //       isActive: true,
  //     })
  //     .select('code description')
  //     .lean();

  //   // Get unique department IDs from reportees with validation
  //   const reporteeDepartmentIds = reportees.length > 0
  //     ? [...new Set(reportees
  //         .filter(r => r.department && Types.ObjectId.isValid(r.department))
  //         .map(r => r.department.toString()))]
  //     : [];

  //   // console.log('reporteeDepartmentIds:', reporteeDepartmentIds); // Debug log

  //   // Fetch department details for reportee departments
  //   const lineManagerDepartments = reporteeDepartmentIds.length > 0
  //     ? await this.departmentModel
  //         .find({
  //           _id: { $in: reporteeDepartmentIds }
  //         })
  //         .select('code description')
  //         .lean()
  //     : [];

  //   // console.log('lineManagerDepartments:', lineManagerDepartments); // Debug log

  //   // Combine HOD departments and line manager departments (remove duplicates)
  //   const allManagedDepartments = [...hodDepartments];

  //   // Add line manager departments that are not already in HOD departments
  //   lineManagerDepartments.forEach(lmDept => {
  //     if (!hodDepartments.some(hodDept => hodDept.code.toString() === lmDept.code.toString())) {
  //       allManagedDepartments.push(lmDept);
  //     }
  //   });

  //   // Check if user is HR
  //   const isHR = mainUser!._id.toString() === '674829e0f3d796aa36ff1e0d';

  //   // Determine user roles
  //   const roles = ['employee']; // Everyone is an employee

  //   if (reportees.length > 0) {
  //     roles.push('supervisor');
  //   }

  //   if (hodDepartments.length > 0) {
  //     roles.push('hod');
  //   }

  //   if (isHR) {
  //     roles.push('hr');
  //   }

  //   // Define permissions based on roles
  //   const permissions = {
  //     canSubmitIPPA: true,
  //     canApproveAsLineManager: reportees.length > 0,
  //     canApproveAsHOD: hodDepartments.length > 0,
  //     canReviewAsHR: isHR,
  //     canViewAllSubmissions: isHR,
  //     canViewDepartmentSubmissions: allManagedDepartments.length > 0, // Updated to check combined departments
  //     canViewOwnSubmissions: true,
  //     canViewReporteeSubmissions: reportees.length > 0,
  //   };

  //   return {
  //     user: {
  //       id: mainUser._id,
  //       email: mainUser.email,
  //       fullName: mainUser.fullName,
  //       department: ppaUser.department,
  //     },
  //     roles,
  //     permissions,
  //     isLineManager: reportees.length > 0,
  //     isHOD: hodDepartments.length > 0,
  //     isHR,
  //     managedDepartments: allManagedDepartments, // Now includes both HOD and line manager departments
  //     reportees: reportees.map((r) => ({
  //       id: r._id,
  //       fullName: r.fullName,
  //       email: r.email,
  //       department: r.department,
  //       departmentCode: r.departmentCode
  //     })),
  //     hodDepartmentCodes: hodDepartments.map((d) => d.code),
  //     lineManagerDepartmentCodes: lineManagerDepartments.map((d) => d.code), // Added for clarity
  //     allManagedDepartmentCodes: allManagedDepartments.map((d) => d.code), // All department codes managed
  //   };
  // }

  async getUserIPPARole(encryptedData: any): Promise<any> {

    const userEmail = this.securityService.decrypt(encryptedData.data);

    // Find the user
    const ppaUser = await this.ippaUserModel
      .findOne({ email: userEmail })
      .populate('department lineManager')
      .lean();

    const mainUser = await this.userModel
      .findOne({ email: userEmail })
      .populate('department')
      .lean();

    if (!ppaUser || !mainUser) {
      throw new Error('User not found');
    }

    // Check if user is a line manager (has reportees)
    const reportees = await this.ippaUserModel
      .find({ lineManager: mainUser!._id })
      .select('fullName email department departmentCode')
      .lean();

    // Check if user is HOD for any department
    const hodDepartments = await this.departmentModel
      .find({
        hodPersonId: new Types.ObjectId(mainUser!._id),
        isActive: true,
      })
      .select('code description')
      .lean();

    // Get unique department codes from reportees
    const reporteeDepartmentCodes =
      reportees.length > 0
        ? [
            ...new Set(
              reportees
                .filter((r) => r.departmentCode)
                .map((r) => r.departmentCode),
            ),
          ]
        : [];

    // console.log('reporteeDepartmentCodes:', reporteeDepartmentCodes); // Debug log

    // Fetch department details for reportee departments using department codes
    const lineManagerDepartments =
      reporteeDepartmentCodes.length > 0
        ? await this.departmentModel
            .find({
              code: { $in: reporteeDepartmentCodes },
            })
            .select('code description')
            .lean()
        : [];

    // console.log('lineManagerDepartments:', lineManagerDepartments); // Debug log

    // Combine HOD departments and line manager departments (remove duplicates)
    const allManagedDepartments = [...hodDepartments];

    // Add line manager departments that are not already in HOD departments
    lineManagerDepartments.forEach((lmDept) => {
      if (
        !hodDepartments.some(
          (hodDept) => hodDept._id.toString() === lmDept._id.toString(),
        )
      ) {
        allManagedDepartments.push(lmDept);
      }
    });

    // Check if user is HR
    const isHR = mainUser!._id.toString() === '674829e0f3d796aa36ff1e0d';

    // Determine user roles
    const roles = ['employee']; // Everyone is an employee

    if (reportees.length > 0) {
      roles.push('supervisor');
    }

    if (hodDepartments.length > 0) {
      roles.push('hod');
    }

    if (isHR) {
      roles.push('hr');
    }

    // Define permissions based on roles
    const permissions = {
      canSubmitIPPA: true,
      canApproveAsLineManager: reportees.length > 0,
      canApproveAsHOD: hodDepartments.length > 0,
      canReviewAsHR: isHR,
      canViewAllSubmissions: isHR,
      canViewDepartmentSubmissions: allManagedDepartments.length > 0, // Updated to check combined departments
      canViewOwnSubmissions: true,
      canViewReporteeSubmissions: reportees.length > 0,
    };

    return {
      user: {
        id: mainUser._id,
        email: mainUser.email,
        fullName: mainUser.fullName,
        department: ppaUser.department,
      },
      roles,
      permissions,
      isLineManager: reportees.length > 0,
      isHOD: hodDepartments.length > 0,
      isHR,
      managedDepartments: allManagedDepartments, // Now includes both HOD and line manager departments
      reportees: reportees.map((r) => ({
        id: r._id,
        fullName: r.fullName,
        email: r.email,
        department: r.department,
        departmentCode: r.departmentCode,
      })),
      hodDepartmentCodes: hodDepartments.map((d) => d.code),
      lineManagerDepartmentCodes: lineManagerDepartments.map((d) => d.code), // Added for clarity
      allManagedDepartmentCodes: allManagedDepartments.map((d) => d.code), // All department codes managed
    };
  }

   async getUserIPPARoleRaw(userEmail: any): Promise<any> {

    // Find the user
    const ppaUser = await this.ippaUserModel
      .findOne({ email: userEmail })
      .populate('department lineManager')
      .lean();

    const mainUser = await this.userModel
      .findOne({ email: userEmail })
      .populate('department')
      .lean();

    if (!ppaUser || !mainUser) {
      throw new Error('User not found');
    }

    // Check if user is a line manager (has reportees)
    const reportees = await this.ippaUserModel
      .find({ lineManager: mainUser!._id })
      .select('fullName email department departmentCode')
      .lean();

    // Check if user is HOD for any department
    const hodDepartments = await this.departmentModel
      .find({
        hodPersonId: new Types.ObjectId(mainUser!._id),
        isActive: true,
      })
      .select('code description')
      .lean();

    // Get unique department codes from reportees
    const reporteeDepartmentCodes =
      reportees.length > 0
        ? [
            ...new Set(
              reportees
                .filter((r) => r.departmentCode)
                .map((r) => r.departmentCode),
            ),
          ]
        : [];

    // console.log('reporteeDepartmentCodes:', reporteeDepartmentCodes); // Debug log

    // Fetch department details for reportee departments using department codes
    const lineManagerDepartments =
      reporteeDepartmentCodes.length > 0
        ? await this.departmentModel
            .find({
              code: { $in: reporteeDepartmentCodes },
            })
            .select('code description')
            .lean()
        : [];

    // console.log('lineManagerDepartments:', lineManagerDepartments); // Debug log

    // Combine HOD departments and line manager departments (remove duplicates)
    const allManagedDepartments = [...hodDepartments];

    // Add line manager departments that are not already in HOD departments
    lineManagerDepartments.forEach((lmDept) => {
      if (
        !hodDepartments.some(
          (hodDept) => hodDept._id.toString() === lmDept._id.toString(),
        )
      ) {
        allManagedDepartments.push(lmDept);
      }
    });

    // Check if user is HR
    const isHR = mainUser!._id.toString() === '674829e0f3d796aa36ff1e0d';

    // Determine user roles
    const roles = ['employee']; // Everyone is an employee

    if (reportees.length > 0) {
      roles.push('supervisor');
    }

    if (hodDepartments.length > 0) {
      roles.push('hod');
    }

    if (isHR) {
      roles.push('hr');
    }

    // Define permissions based on roles
    const permissions = {
      canSubmitIPPA: true,
      canApproveAsLineManager: reportees.length > 0,
      canApproveAsHOD: hodDepartments.length > 0,
      canReviewAsHR: isHR,
      canViewAllSubmissions: isHR,
      canViewDepartmentSubmissions: allManagedDepartments.length > 0, // Updated to check combined departments
      canViewOwnSubmissions: true,
      canViewReporteeSubmissions: reportees.length > 0,
    };

    return {
      user: {
        id: mainUser._id,
        email: mainUser.email,
        fullName: mainUser.fullName,
        department: ppaUser.department,
      },
      roles,
      permissions,
      isLineManager: reportees.length > 0,
      isHOD: hodDepartments.length > 0,
      isHR,
      managedDepartments: allManagedDepartments, // Now includes both HOD and line manager departments
      reportees: reportees.map((r) => ({
        id: r._id,
        fullName: r.fullName,
        email: r.email,
        department: r.department,
        departmentCode: r.departmentCode,
      })),
      hodDepartmentCodes: hodDepartments.map((d) => d.code),
      lineManagerDepartmentCodes: lineManagerDepartments.map((d) => d.code), // Added for clarity
      allManagedDepartmentCodes: allManagedDepartments.map((d) => d.code), // All department codes managed
    };
  }
  
  
  /**
   * Supervisor approves submission with ratings
   */
  async supervisorApprove(
    encryptedData: SupervisorApprovalRequest| any,
  ): Promise<IPPASubmissionDocument> {

    const approvalData = this.securityService.decrypt(encryptedData.data);

    const submission = await this.ippaSubmissionModel.findById(
      approvalData.submissionId,
    );
    if (!submission) {
      throw new NotFoundException('IPPA submission not found');
    }

    // Get user role
    const userRole = await this.getUserIPPARoleRaw(approvalData.supervisorEmail);
    if (!userRole.isLineManager) {
      throw new Error('User is not authorized to approve as supervisor');
    }

    const supervisor = await this.userModel
      .findOne({ email: approvalData.supervisorEmail.toLowerCase().trim() })
      .lean();

    if (!supervisor) {
      throw new NotFoundException('Supervisor not found');
    }

    // console.log("submission ", submission)
    // console.log("supervisor ", supervisor)

    // Verify supervisor can approve this submission
    if (!this.canApprove(submission, supervisor._id)) {
      throw new ForbiddenException(
        'You are not authorized to approve this submission',
      );
    }

    // Update objectives with supervisor ratings
    // submission.objectives.forEach((objective, index) => {
    //   const supervisorObjective = approvalData.objectives.find(
    //     (obj) => obj.index === index,
    //   );
    //   if (supervisorObjective) {
    //     objective.supervisorRating = supervisorObjective.supervisorRating;
    //     objective.supervisorComments = supervisorObjective.supervisorComments;
    //     objective.supervisorWeightedPoints =
    //       supervisorObjective.supervisorWeightedPoints;
    //   }
    // });

    approvalData.objectives.forEach((supervisorObjective, index) => {
    if (submission.objectives[index]) {
      submission.objectives[index].supervisorRating = supervisorObjective.supervisorRating;
      submission.objectives[index].supervisorComments = supervisorObjective.supervisorComments;
      submission.objectives[index].supervisorWeightedPoints = supervisorObjective.supervisorWeightedPoints;
    }
  });

    // Calculate supervisor overall score
    const supervisorTotalWeightedPoints = submission.objectives.reduce(
      (sum, obj) => {
        return sum + (obj.supervisorWeightedPoints || 0);
      },
      0,
    );
    const supervisorOverallScore =
      supervisorTotalWeightedPoints / (submission.employeeTotalWeightage / 100);

    // Add supervisor review
    submission.reviews.push({
      reviewerType: ReviewerType.SUPERVISOR,
      reviewerId: supervisor._id,
      reviewerName: supervisor.fullName,
      remarks: approvalData.supervisorRemarks,
      attachments: approvalData.supervisorAttachments || [],
      overallScore: supervisorOverallScore,
      reviewedAt: new Date(),
      department: supervisor.department,
    });

    // Update workflow
    const currentStep = submission.approvalWorkflow.find(
      (step) =>
        step.personId.toString() === supervisor._id.toString() &&
        step.status === 'pending',
    );
    if (currentStep) {
      currentStep.status = 'completed';
      currentStep.date = new Date().toLocaleString('en-GB');
    }

    // Move to next step
    const nextStep = await this.moveToNextWorkflowStep(submission, supervisor);

    // Update submission
    submission.supervisorOverallScore = supervisorOverallScore;
    submission.supervisorReviewedAt = new Date();
    submission.status = nextStep.status;
    submission.currentReviewerId = nextStep.reviewerId || undefined;
    submission.currentWorkflowStep = nextStep.stepId;
    submission.lastModifiedAt = new Date();

    return await submission.save();
  }

  /**
   * HOD approves submission (optional ratings)
   */
  async hodApprove(
    approvalData: HODApprovalRequest,
  ): Promise<IPPASubmissionDocument> {
    const submission = await this.ippaSubmissionModel.findById(
      approvalData.submissionId,
    );
    if (!submission) {
      throw new NotFoundException('IPPA submission not found');
    }

    const hod = await this.ippaUserModel
      .findOne({ email: approvalData.hodEmail.toLowerCase().trim() })
      .lean();

    if (!hod) {
      throw new NotFoundException('HOD not found');
    }

    if (!this.canApprove(submission, hod._id)) {
      throw new ForbiddenException(
        'You are not authorized to approve this submission',
      );
    }

    // Update objectives with HOD ratings (if provided)
    if (approvalData.objectives) {
      submission.objectives.forEach((objective, index) => {
        const hodObjective = approvalData.objectives?.find(
          (obj) => obj.index === index + 1,
        );
        if (hodObjective) {
          objective.hodRating = hodObjective.hodRating;
          objective.hodComments = hodObjective.hodComments;
          objective.hodWeightedPoints = hodObjective.hodWeightedPoints;
        }
      });
    }

    // Add HOD review (if remarks or attachments provided)
    if (approvalData.hodRemarks || approvalData.hodAttachments?.length) {
      submission.reviews.push({
        reviewerType: ReviewerType.HOD,
        reviewerId: hod._id,
        reviewerName: hod.fullName,
        remarks: approvalData.hodRemarks || '',
        attachments: approvalData.hodAttachments || [],
        overallScore: 0, // HOD doesn't calculate overall score
        reviewedAt: new Date(),
        department: hod.department,
      });
    }

    // Update workflow
    const currentStep = submission.approvalWorkflow.find(
      (step) =>
        step.personId.toString() === hod._id.toString() &&
        step.status === 'pending',
    );
    if (currentStep) {
      currentStep.status = 'completed';
      currentStep.date = new Date().toLocaleString('en-GB');
    }

    // Move to next step (HR)
    const nextStep = await this.moveToNextWorkflowStep(submission, hod);

    submission.status = nextStep.status;
    submission.currentReviewerId = nextStep.reviewerId || undefined;

    submission.currentWorkflowStep = nextStep.stepId;
    submission.lastModifiedAt = new Date();

    return await submission.save();
  }

  /**
   * HR completes the submission
   */
  async hrComplete(
    approvalData: HRApprovalRequest,
  ): Promise<IPPASubmissionDocument> {
    const submission = await this.ippaSubmissionModel.findById(
      approvalData.submissionId,
    );
    if (!submission) {
      throw new NotFoundException('IPPA submission not found');
    }

    const hr = await this.ippaUserModel
      .findOne({ email: approvalData.hrEmail.toLowerCase().trim() })
      .lean();

    if (!hr) {
      throw new NotFoundException('HR user not found');
    }

    if (!this.canApprove(submission, hr._id)) {
      throw new ForbiddenException(
        'You are not authorized to complete this submission',
      );
    }

    // Add HR review (if remarks or attachments provided)
    if (approvalData.hrRemarks || approvalData.hrAttachments?.length) {
      submission.reviews.push({
        reviewerType: ReviewerType.HR,
        reviewerId: hr._id,
        reviewerName: hr.fullName,
        remarks: approvalData.hrRemarks || '',
        attachments: approvalData.hrAttachments || [],
        overallScore: 0,
        reviewedAt: new Date(),
        department: 'HR',
      });
    }

    // Complete workflow
    const currentStep = submission.approvalWorkflow.find(
      (step) =>
        step.personId.toString() === hr._id.toString() &&
        step.status === 'pending',
    );
    if (currentStep) {
      currentStep.status = 'completed';
      currentStep.date = new Date().toLocaleString('en-GB');
    }

    // Mark as completed
    submission.status = IPPAStatus.COMPLETED;
    submission.currentReviewerId = undefined;
    submission.completedAt = new Date();
    submission.lastModifiedAt = new Date();

    return await submission.save();
  }

  /**
   * Get submission details for review/approval
   */
  async getSubmissionForReview(
    submissionId: string,
    reviewerEmail: string,
  ): Promise<IPPASubmissionDocument> {
    const submission = await this.ippaSubmissionModel
      .findById(submissionId)
      .populate(
        'employeeId',
        'fullName email department position employeeGrade',
      )
      .populate('currentReviewerId', 'fullName email department')
      .exec();

    if (!submission) {
      throw new NotFoundException('IPPA submission not found');
    }

    const reviewer = await this.ippaUserModel
      .findOne({ email: reviewerEmail.toLowerCase().trim() })
      .lean();

    if (!reviewer) {
      throw new NotFoundException('Reviewer not found');
    }

    // Check if reviewer can access this submission
    if (!this.canViewSubmission(submission, reviewer._id)) {
      throw new ForbiddenException(
        'You are not authorized to view this submission',
      );
    }

    return submission;
  }

  // Helper methods
  private getApproverRole(submission: any, approverId: Types.ObjectId): string {
    if (submission.currentReviewerId?.toString() === approverId.toString()) {
      switch (submission.status) {
        case IPPAStatus.PENDING_SUPERVISOR_REVIEW:
          return 'Line Manager';
        case IPPAStatus.PENDING_HOD_REVIEW:
          return 'HOD';
        case IPPAStatus.PENDING_HR_REVIEW:
          return 'HR';
        default:
          return 'Reviewer';
      }
    }

    const workflowStep = submission.approvalWorkflow?.find(
      (step) => step.personId.toString() === approverId.toString(),
    );
    return workflowStep?.title || 'Unknown';
  }

  private canApprove(submission: any, approverId: Types.ObjectId): boolean {

    // console.log("current reviewer id ", submission.currentReviewerId?.toString())
    // console.log("approver ID ", approverId.toString())
    return (
      submission.currentReviewerId?.toString() === approverId.toString() &&
      submission.status !== IPPAStatus.COMPLETED &&
      submission.status !== IPPAStatus.DRAFT
    );
  }

  private canViewSubmission(submission: any, userId: Types.ObjectId): boolean {
    // Employee can view their own
    if (submission.employeeId.toString() === userId.toString()) {
      return true;
    }

    // Current reviewer can view
    if (submission.currentReviewerId?.toString() === userId.toString()) {
      return true;
    }

    // Anyone in workflow can view
    const inWorkflow = submission.approvalWorkflow?.some(
      (step) => step.personId.toString() === userId.toString(),
    );

    return inWorkflow || false;
  }

  private calculateDaysPending(submission: any): number {
    const now = new Date();
    const startDate = submission.submittedAt || submission.createdAt;
    const diffTime = Math.abs(now.getTime() - startDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  private isSubmissionOverdue(submission: any): boolean {
    const daysPending = this.calculateDaysPending(submission);

    switch (submission.status) {
      case IPPAStatus.PENDING_SUPERVISOR_REVIEW:
        return daysPending > 14;
      case IPPAStatus.PENDING_HOD_REVIEW:
        return daysPending > 10;
      case IPPAStatus.PENDING_HR_REVIEW:
        return daysPending > 7;
      default:
        return false;
    }
  }

  private getNextActionForApprover(
    submission: any,
    approverRole: string,
  ): string {
    switch (approverRole) {
      case 'Line Manager':
        return 'Review objectives and provide ratings';
      case 'HOD':
        return 'Review and approve (optional ratings)';
      case 'HR':
        return 'Final review and complete';
      default:
        return 'Review required';
    }
  }

  private async moveToNextWorkflowStep(
    submission: any,
    currentApprover: any,
  ): Promise<{
    status: IPPAStatus;
    reviewerId: Types.ObjectId | null;
    stepId: number;
  }> {
    // Find next pending step
    const nextStep = submission.approvalWorkflow.find(
      (step) => step.status === 'upcoming',
    );

    if (nextStep) {
      nextStep.status = 'pending';

      // Determine status based on next step
      if (nextStep.title.includes('HOD')) {
        return {
          status: IPPAStatus.PENDING_HOD_REVIEW,
          reviewerId: nextStep.personId,
          stepId: nextStep.stepId,
        };
      } else if (
        nextStep.title.includes('HR') ||
        nextStep.title.includes('Reviewed by')
      ) {
        return {
          status: IPPAStatus.PENDING_HR_REVIEW,
          reviewerId: nextStep.personId,
          stepId: nextStep.stepId,
        };
      }
    }

    // No more steps - should be completed by HR
    return {
      status: IPPAStatus.PENDING_HR_REVIEW,
      reviewerId: null,
      stepId: submission.approvalWorkflow.length,
    };
  }
}
