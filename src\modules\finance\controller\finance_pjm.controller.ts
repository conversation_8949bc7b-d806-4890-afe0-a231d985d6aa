import { Controller, Get, Post, Put, Delete, Body, Param } from '@nestjs/common';
import { PJM } from '../schema/finance_pjm';
import { PJMService } from '../service/finance_pjm.service';

@Controller('finance/pjm')
export class PJMController {
  constructor(private readonly pjmService: PJMService) {}

  @Post()
  async createPJM(@Body() pjm: PJM) {
    return this.pjmService.create(pjm);
  }

  @Get()
  async getAllPJMs() {
    return this.pjmService.findAll();
  }

  @Get(':id')
  async getPJMById(@Param('id') id: string) {
    return this.pjmService.findOne(id);
  }

  @Put(':id')
  async updatePJM(@Param('id') id: string, @Body() updateData: Partial<PJM>) {
    return this.pjmService.update(id, updateData);
  }

  @Delete(':id')
  async deletePJM(@Param('id') id: string) {
    return this.pjmService.delete(id);
  }
}
