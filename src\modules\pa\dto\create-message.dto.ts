// create-message.dto.ts
import { IsMongoId, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateMessageDto {
  @IsMongoId()
  @IsNotEmpty()
  paymentAdviceId: string;

  @IsMongoId()
  @IsNotEmpty()
  senderId: string;

  @IsString()
  @IsNotEmpty()
  senderRole: string;

  @IsString()
  @IsNotEmpty()
  message: string;

  @IsString()
  @IsOptional()
  attachmentUrl?: string;
}