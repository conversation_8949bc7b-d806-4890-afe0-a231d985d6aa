// ippa-message.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { IPPAMessage, IPPAMessageDocument } from '../schema/ippa-message.schema';
import { IPPASubmission, IPPASubmissionDocument } from '../schema/ippa-submission.schema';
import { User, UserDocument } from 'src/modules/user/schema/user.schema';
import { EmailScheduler, EmailSchedulerDocument } from 'src/modules/common/schema/pa-email.schema';
import { CreateIPPAMessageDto } from '../dto/create-ippa-message.dto';

@Injectable()
export class IPPAMessageService {
  constructor(
    @InjectModel(IPPAMessage.name) private messageModel: Model<IPPAMessageDocument>,
    @InjectModel(IPPASubmission.name) private ippaSubmissionModel: Model<IPPASubmissionDocument>,
    @InjectModel(EmailScheduler.name) private emailSchedulerModel: Model<EmailSchedulerDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) { }

  async create(createMessageDto: CreateIPPAMessageDto): Promise<IPPAMessage | any> {

    const ippaSubmission = await this.ippaSubmissionModel
      .findById(createMessageDto.ippaSubmissionId)
      .select('ippaId employeeName employeeDepartment employeePosition lineManagerId lineManagerName approvalWorkflow financialYear reviewPeriod status')
      .populate('lineManagerId', '_id fullName email')
      .populate('approvalWorkflow.personId', '_id fullName email')
      .exec();

    if(!ippaSubmission) {
      throw new NotFoundException('IPPA submission not found');
    }

    // Find the sender's information from approval workflow
    const sendBy = ippaSubmission.approvalWorkflow?.find((step) => {
      return step.personId['_id'].toString() === createMessageDto.senderId;
    });

    // console.log("sendBy ", sendBy)

    // If sender not found in workflow, check if it's the employee themselves
    let senderName = 'Unknown Sender';
    if (sendBy) {
      senderName = sendBy.personName || sendBy.personId['fullName'];
    } else if (ippaSubmission.lineManagerId && ippaSubmission.lineManagerId['_id'].toString() === createMessageDto.senderId) {
      senderName = ippaSubmission.lineManagerName || ippaSubmission.lineManagerId['fullName'];
    }

    // Collect all recipients from approval workflow excluding the sender
    const recipients = new Map<string, any>();

    // Add line manager if not the sender
    if (ippaSubmission.lineManagerId && ippaSubmission.lineManagerId['_id'].toString() !== createMessageDto.senderId) {
      recipients.set(ippaSubmission.lineManagerId['_id'].toString(), {
        _id: ippaSubmission.lineManagerId['_id'],
        fullName: ippaSubmission.lineManagerName || ippaSubmission.lineManagerId['fullName'],
        email: ippaSubmission.lineManagerId['email']
      });
    }

    // Add all workflow participants except sender
    if (ippaSubmission.approvalWorkflow) {
      for (const step of ippaSubmission.approvalWorkflow) {
        if (step.personId['_id'].toString() !== createMessageDto.senderId) {
          recipients.set(step.personId['_id'].toString(), {
            _id: step.personId['_id'],
            fullName: step.personName || step.personId['fullName'],
            email: step.personId['email']
          });
        }
      }
    }

    // Send emails to all recipients
    for (const [userId, recipient] of recipients) {
      let attachmentUrl = '';
      if (createMessageDto.attachmentUrl) {
        attachmentUrl = createMessageDto.attachmentUrl;
      }

      // Create and save email notification for each recipient
      const emailScheduler = new this.emailSchedulerModel({
        to: recipient.email,
        subject: `IPPA: ${ippaSubmission.ippaId} Chat Activity`,
        body: `<p>Dear ${recipient.fullName}, <br/>
                   IPPA submission - ${ippaSubmission.ippaId} has a message from<b> ${senderName}</b>.<br/><br/>
                   Employee - ${ippaSubmission.employeeName}<br/>
                   Department - ${ippaSubmission.employeeDepartment}<br/>
                   Position - ${ippaSubmission.employeePosition}<br/>
                   Financial Year - ${ippaSubmission.financialYear}<br/>
                   Review Period - ${ippaSubmission.reviewPeriod}<br/>
                   Status - <b>${ippaSubmission.status.replace(/_/g, ' ').toUpperCase()}</b><br/><br/>
                   Message - <b>${createMessageDto.message}</b><br/><br/>
                   Attachment - <b>${attachmentUrl}</b><br/><br/>
                   Thank you.<br/><br/>
                   Best Regards,<br/>
                   UZMA ECO SYSTEM</p>`,
      });
      await emailScheduler.save();
    }

    const newMessage = new this.messageModel(createMessageDto);
    return newMessage.save();
  }

  async findAllByIPPASubmissionId(ippaSubmissionId: string): Promise<any> {
    return this.messageModel
      .find({ ippaSubmissionId: ippaSubmissionId })
      .sort({ createdAt: 1 })
      .populate('senderId', 'fullName role faceImage')
      .exec();
  }

  async markAsRead(messageId: string, userId: string): Promise<IPPAMessage | any> {
    return this.messageModel.findByIdAndUpdate(
      messageId,
      { $addToSet: { readBy: new Types.ObjectId(userId) } },
      { new: true },
    ).exec();
  }

//   async markAsRead(messageId: string, userId: string): Promise<IPPAMessage | any> {
//   try {
//     // Validate inputs
//     if (!messageId || !userId) {
//       throw new Error('MessageId and userId are required');
//     }

//     // Validate ObjectId formats
//     if (!Types.ObjectId.isValid(messageId)) {
//       throw new Error('Invalid messageId format');
//     }

//     if (!Types.ObjectId.isValid(userId)) {
//       throw new Error('Invalid userId format');
//     }

//     // console.log('Marking message as read:', { messageId, userId });

//     // First, check if the message exists
//     const existingMessage = await this.messageModel.findById(messageId).exec();
//     if (!existingMessage) {
//       throw new Error('Message not found');
//     }

//     // console.log('Existing message found:', existingMessage._id);
//     // console.log('Current readBy array:', existingMessage.readBy);

//     // Create ObjectId instance
//     const userObjectId = new Types.ObjectId(userId);

//     // Check if user is already in readBy array
//     const alreadyRead = existingMessage.readBy.some(readUserId => 
//       readUserId.toString() === userId.toString()
//     );

//     if (alreadyRead) {
//       // console.log('Message already marked as read by this user');
//       return existingMessage;
//     }

//     // Update the message
//     const updatedMessage = await this.messageModel.findByIdAndUpdate(
//       messageId,
//       { 
//         $addToSet: { readBy: userObjectId },
//         $set: { updatedAt: new Date() } // Optional: track when it was updated
//       },
//       { new: true }
//     ).populate('senderId', 'fullName email faceImage').exec();

//     if (!updatedMessage) {
//       throw new Error('Failed to update message');
//     }

//     // console.log('Message marked as read successfully:', updatedMessage._id);
//     // console.log('Updated readBy array:', updatedMessage.readBy);

//     return updatedMessage;
//   } catch (error) {
//     console.error('Error in markAsRead:', error);
//     throw error;
//   }
// }

  async getUnreadMessageCount(body: any): Promise<number> {
    const count = await this.messageModel.countDocuments({
      ippaSubmissionId: new Types.ObjectId(body.ippaSubmissionId),
      readBy: { $ne: new Types.ObjectId(body.userId) }
    }).exec();
    return count;
  }
}