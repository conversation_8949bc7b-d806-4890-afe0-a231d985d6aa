import { IsString, IsMongoId, IsOptional } from 'class-validator';

export class CreateUserDTO {
  @IsString()
  username: string;

  @IsString()
  fullName: string;

  @IsString()
  email: string;

  @IsString()
  faceId: string;

  @IsString()
  faceImage: string;  // AWS face image link

  @IsString()
  department: string;

  @IsString()
  designation: string;

  @IsString()
  loginMethod: string;  // 'face' or 'password'

  @IsMongoId()
  role: string;  // Role ID

  @IsOptional()
  createdBy: string;  // ID of the user who created this user
}
