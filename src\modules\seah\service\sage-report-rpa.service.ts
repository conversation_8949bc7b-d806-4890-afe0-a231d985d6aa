import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { SageReportRPA, SageReportRPADocument } from '../schema/sage-pv.schema';

@Injectable()
export class SageReportRPAService {
  constructor(
    @InjectModel(SageReportRPA.name) private sageReportRPAModel: Model<SageReportRPADocument>,
  ) {}

  async create(sageReportRPAData: Partial<SageReportRPA>): Promise<SageReportRPA> {
    const newSageReportRPA = new this.sageReportRPAModel(sageReportRPAData);
    return newSageReportRPA.save();
  }

  async findAll(): Promise<SageReportRPA[]> {
    return this.sageReportRPAModel.find().sort({ createdAt: -1 }).exec();
  }

  async findOne(id: string): Promise<SageReportRPA | any> {
    return this.sageReportRPAModel.findById(id).exec();
  }

  async update(id: string, updateData: Partial<SageReportRPA>): Promise<SageReportRPA | any> {
    return this.sageReportRPAModel.findByIdAndUpdate(id, updateData, { new: true }).exec();
  }

  async remove(id: string): Promise<SageReportRPA | any> {
    return this.sageReportRPAModel.findByIdAndDelete(id).exec();
  }

  async markAsPosted(id: string): Promise<SageReportRPA | any> {

    const report = this.sageReportRPAModel.findByIdAndUpdate(
      id,
      { 
        isPosted: true, 
        postedDateTime: new Date() 
      },
      { new: true }
    ).exec();

    if(!report) {
      throw new NotFoundException("Report not found");
    }
    return report;
  }

  async logRPAActivity(type: string, totalRecord: string, totalAmount: number, isSuccess: boolean, remark: string = ''): Promise<SageReportRPA> {
    return this.create({
      type,
      totalRecord,
      totalAmount,
      isSuccess,
      remark,
      isPosted: false,
    });
  }
}