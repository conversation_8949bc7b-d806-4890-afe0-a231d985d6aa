import { Controller, Get, Post, Param, Body, Put, Delete, NotFoundException, Query, HttpException, HttpStatus } from '@nestjs/common';
import { CreateLoaDto } from '../dto/pa-loa.dto';
import { LoaService } from '../service/pa-loa.service';
import { SecurityService } from 'src/modules/user/service/security.service';

@Controller('loa')
export class LoaController {
  constructor(private readonly loaService: LoaService,
    private readonly securityService: SecurityService
  ) {}

  @Post()
  async create(@Body() createLoaDto: CreateLoaDto) {
    return this.loaService.create(createLoaDto);
  }

  @Get()
  async findAll() {
    return this.loaService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.loaService.findOne(id);
  }

  @Put(':id')
  async update(@Param('id') id: string, @Body() updateLoaDto: Partial<CreateLoaDto>) {
    return this.loaService.update(id, updateLoaDto);
  }

  @Delete(':id')
  async delete(@Param('id') id: string) {
    return this.loaService.delete(id);
  }

  @Post('structure-loa')
  async test(@Body() body: any) {
    const { oldId, newId } = body;

    if (!oldId || !newId) {
      throw new HttpException(
        'Invalid input data',
        HttpStatus.BAD_REQUEST
      );
    }
    return await this.loaService.updateFinanceArray(oldId, newId);
  }

  @Post('approvers')
  async getApprovers(@Body() encryptedData: any) {
    const decryptedData = this.securityService.decrypt(encryptedData.data);

    const { departmentId, currency, amount, identifier} = decryptedData;

    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount)) {
      throw new NotFoundException('Invalid amount');
    }
    return this.loaService.findApproversForAmount(departmentId, numericAmount, currency, identifier);
  }
}
