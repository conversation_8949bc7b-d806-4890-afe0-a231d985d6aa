import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ProjectCostMain } from '../schema/project-cost-main.schema';
import { CreateProjectCostMainDto, UpdateProjectCostMainDto } from '../dto/project-cost-main.dto';

@Injectable()
export class ProjectCostMainService {
  constructor(
    @InjectModel(ProjectCostMain.name) private projectCostMainModel: Model<ProjectCostMain>,
  ) {}

  // Create
  async create(createProjectCostMainDto: CreateProjectCostMainDto): Promise<ProjectCostMain> {
    const projectCostMain = new this.projectCostMainModel(createProjectCostMainDto);
    return projectCostMain.save();
  }

  // Read all
  async findAll(): Promise<ProjectCostMain> {
    const project = await this.projectCostMainModel.findOne().exec();
     if(!project) {
      throw new NotFoundException("Can't find project cost")
     }
    return project;
  }

  // Read one by ID
  async findOne(id: string): Promise<ProjectCostMain> {
    const projectCostMain = await this.projectCostMainModel.findById(id).exec();
    if (!projectCostMain) {
      throw new NotFoundException(`Project cost with ID "${id}" not found`);
    }
    return projectCostMain;
  }

  // Update
  async update(id: string, updateProjectCostMainDto: UpdateProjectCostMainDto): Promise<ProjectCostMain> {
    const updatedProjectCost = await this.projectCostMainModel
      .findByIdAndUpdate(id, updateProjectCostMainDto, { new: true })
      .exec();

    if (!updatedProjectCost) {
      throw new NotFoundException(`Project cost with ID "${id}" not found`);
    }
    return updatedProjectCost;
  }

//   // Delete
//   async remove(id: string): Promise<ProjectCostMain> {
//     const deletedProjectCost = await this.projectCostMainModel.findByIdAndRemove(id).exec();
//     if (!deletedProjectCost) {
//       throw new NotFoundException(`Project cost with ID "${id}" not found`);
//     }
//     return deletedProjectCost;
//   }
}
