import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

// Enums for better type safety
export enum IPPAStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  PENDING_SUPERVISOR_REVIEW = 'pending_supervisor_review',
  SUPERVISOR_REVIEWED = 'supervisor_reviewed',
  PENDING_HR_REVIEW = 'pending_hr_review',
  PENDING_HOD_REVIEW = 'pending_hod_review',
  HOD_REVIEWED = 'hod_reviewed', 
  COMPLETED = 'completed',
  TO_REVIEW = 'to-review',
  NOT_SUBMITTED = 'not-submitted',
//   REJECTED = 'rejected'
}

export enum ReviewerType {
  EMPLOYEE = 'employee',
  SUPERVISOR = 'supervisor',
  HR = 'hr',
  HOD = 'hod'
}

// Sub-schemas for different sections
@Schema({ _id: false })
export class Objective {
  @Prop({ required: true })
  keyResultsArea: string;

  @Prop({ required: true })
  workObjective: string;

  @Prop({ required: true })
  targetBase: string;

  @Prop({ required: true })
  targetExceed: string;

  @Prop({ required: true })
  targetStretch: string;

  @Prop({ required: true, min: 0, max: 100 })
  weightage: number;

  @Prop({ required: true })
  yearEndResults: string;

  @Prop({ required: true })
  performanceAssessment: string;

  // Employee's self-rating (maintained forever)
  @Prop({ required: true, min: 1, max: 5 })
  employeeRating: number;

  @Prop()
  employeeWeightedPoints: number;

  // Supervisor's rating (final rating)
  @Prop({ min: 1, max: 5 })
  supervisorRating?: number;

  @Prop()
  supervisorWeightedPoints?: number;

  @Prop()
  supervisorComments?: string;

  // HOD rating
   @Prop({ min: 1, max: 5 })
  hodRating?: number;

  @Prop()
  hodWeightedPoints?: number;

  @Prop()
  hodComments?: string;
  
}

@Schema({ _id: false })
export class DevelopmentPlan {
  @Prop({ required: true })
  competencyType: string;

  @Prop({ required: true })
  areaForImprovement: string;

  @Prop({ required: true })
  activity: string;

  @Prop({ required: true })
  timeline: string;

  @Prop()
  yearEndComments: string;

  @Prop()
  supervisorFeedback?: string;

  @Prop()
  hodRemarks?: string; // HOD can add remarks to development plans
}

@Schema({ _id: false })
export class Attachment {
  @Prop({ required: true })
  fileName: string;

  @Prop({ required: true })
  filePath: string;

  @Prop({ required: true })
  fileSize: number;

  @Prop({ required: true })
  mimeType: string;

  @Prop({ default: Date.now })
  uploadedAt: Date;

  @Prop()
  description?: string;
}

@Schema({ _id: false })
export class ReviewSection {
  @Prop({ type: String, enum: ReviewerType, required: true })
  reviewerType: ReviewerType;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  reviewerId: Types.ObjectId;

  @Prop()
  reviewerName: string; // Store name for historical purposes

  @Prop()
  remarks: string;

  @Prop({ type: [Attachment] })
  attachments: Attachment[];

  @Prop()
  overallScore?: number; // Only for supervisor

  @Prop({ default: Date.now })
  reviewedAt: Date;

  @Prop()
  department?: string; // Reviewer's department at time of review
}

@Schema({ _id: false })
export class ApprovalWorkflowStep {
  @Prop({ required: true })
  stepId: number;

  @Prop({ required: true })
  title: string;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  personId: Types.ObjectId;

  @Prop({ required: true })
  personName: string;

  @Prop()
  department?: string;

  @Prop({ default: '-' })
  date: string;

  @Prop({ enum: ['completed', 'pending', 'upcoming'], default: 'upcoming' })
  status: string;

  @Prop({ enum: ['employee', 'supervisor', 'hod', 'hr'], default: 'employee' })
  stepType: string;

  @Prop()
  isRequired: boolean;
}

// Main IPPA Schema
@Schema({ timestamps: true, collection: 'ippa_submissions' })
export class IPPASubmission {
  _id: Types.ObjectId;

  // Generate unique IPPA ID
  @Prop({ required: true, unique: true, index: true })
  ippaId: string; // Format: UZMA-PPA-YYYYMMDD-XXXX

  // Reference to IPPA User
  @Prop({ type: Types.ObjectId, ref: 'IppaUser', required: true, index: true })
  employeeId: Types.ObjectId;

  // Employee details (snapshot for historical data)
  @Prop({ required: true })
  employeeName: string;

  @Prop({ required: true })
  employeeEmail: string;

  @Prop({ required: true })
  employeeDepartment: string;

  @Prop({ required: true })
  employeePosition: string;

  @Prop({ required: true })
  employeeGrade: string;

  // Line Manager details (snapshot)
  @Prop({ type: Types.ObjectId, ref: 'User', required: true, index: true })
  lineManagerId: Types.ObjectId;

  @Prop({ required: true })
  lineManagerName: string;

  @Prop({ required: true })
  lineManagerEmail: string;

  // Financial Year and Period
  @Prop({ required: true, index: true })
  financialYear: string;

  @Prop({ required: true })
  reviewPeriod: string;

  // Status Management
  @Prop({ type: String, enum: IPPAStatus, default: IPPAStatus.DRAFT, index: true })
  status: IPPAStatus;

  // Current reviewer for workflow
  @Prop({ type: Types.ObjectId, ref: 'User', index: true })
  currentReviewerId?: Types.ObjectId;

  // Section 2: Performance Objectives
  @Prop({ type: [Objective], required: true })
  objectives: Objective[];

  // Section 3: Development Plans
  @Prop({ type: [DevelopmentPlan], required: true })
  developmentPlans: DevelopmentPlan[];

  // Section 4: Reviews by different stakeholders
  @Prop({ type: [ReviewSection] })
  reviews: ReviewSection[];

  // Approval Workflow
  @Prop({ type: [ApprovalWorkflowStep] })
  approvalWorkflow: ApprovalWorkflowStep[];

  @Prop()
  currentWorkflowStep: number;

  // Calculated scores - Employee
  @Prop()
  employeeTotalWeightage: number;

  @Prop()
  employeeTotalWeightedPoints: number;

  @Prop()
  employeeOverallScore: number;

  // Calculated scores - Supervisor (Final)
  @Prop()
  supervisorTotalWeightedPoints?: number;

  @Prop()
  supervisorOverallScore?: number;

  // Performance Rating based on final score
  @Prop()
  finalPerformanceRating?: string;

  @Prop()
  finalPerformanceDescription?: string;

  // Important dates
  @Prop()
  submittedAt?: Date;

  @Prop()
  supervisorReviewedAt?: Date;

  @Prop()
  hrReviewedAt?: Date;

  @Prop()
  completedAt?: Date;

  // Department at time of submission (for historical tracking)
  @Prop({ required: true, index: true })
  submissionDepartment: string;

  @Prop({ required: true, index: true })
  submissionDepartmentCode: string;

  // HOD access tracking
  @Prop({ type: [Types.ObjectId], ref: 'User' })
  hodViewedBy: Types.ObjectId[];

  @Prop({ default: true })
  isActive: boolean;

  // Additional metadata
  @Prop()
  isDraft: boolean;

  @Prop()
  lastModifiedBy: Types.ObjectId;

  @Prop()
  lastModifiedAt: Date;
}

export type IPPASubmissionDocument = IPPASubmission & Document;
export const IPPASubmissionSchema = SchemaFactory.createForClass(IPPASubmission);

// ==================== INDEXES ====================
// Compound indexes for efficient queries
IPPASubmissionSchema.index({ employeeId: 1, financialYear: 1 }); // Unique per employee per year
IPPASubmissionSchema.index({ status: 1, currentReviewerId: 1 }); // Pending reviews
IPPASubmissionSchema.index({ lineManagerId: 1, status: 1 }); // Supervisor dashboard
IPPASubmissionSchema.index({ financialYear: 1, status: 1 }); // Year-wise filtering
IPPASubmissionSchema.index({ submissionDepartment: 1, status: 1 }); // Department filtering
IPPASubmissionSchema.index({ submissionDepartmentCode: 1, financialYear: 1 }); // HOD access
IPPASubmissionSchema.index({ ippaId: 1 }); // Unique IPPA ID lookup
IPPASubmissionSchema.index({ submittedAt: 1 }); // Date range queries
IPPASubmissionSchema.index({ employeeName: 'text', employeeEmail: 'text' }); // Text search