import { Controller, Get, Post, Body, Param, Put, Delete } from '@nestjs/common';
import { ProjectCostMainService } from '../service/project-cost-main.service';
import { CreateProjectCostMainDto, UpdateProjectCostMainDto } from '../dto/project-cost-main.dto';
import { ProjectCostMain } from '../schema/project-cost-main.schema';

@Controller('project-cost-main')
export class ProjectCostMainController {
  constructor(private readonly projectCostMainService: ProjectCostMainService) {}

  @Post()
  async create(@Body() createProjectCostMainDto: CreateProjectCostMainDto): Promise<ProjectCostMain> {
    return this.projectCostMainService.create(createProjectCostMainDto);
  }

  @Get()
  async findAll(): Promise<ProjectCostMain> {
    return this.projectCostMainService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<ProjectCostMain> {
    return this.projectCostMainService.findOne(id);
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateProjectCostMainDto: UpdateProjectCostMainDto,
  ): Promise<ProjectCostMain> {
    return this.projectCostMainService.update(id, updateProjectCostMainDto);
  }

//   @Delete(':id')
//   async remove(@Param('id') id: string): Promise<ProjectCostMain> {
//     return this.projectCostMainService.remove(id);
//   }
}
