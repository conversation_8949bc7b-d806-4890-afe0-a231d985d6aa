import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ApprovalFlow, ApprovalFlowDocument } from '../schema/approval-flow.schema';
import { Department } from '../../user/schema/department.schema';
import { UserService } from '../../user/service/user.service';
import { Project } from 'src/modules/auto-billing/schema/billing-project.schema';

@Injectable()
export class ApprovalFlowService {
  constructor(
    @InjectModel(ApprovalFlow.name) private approvalFlowModel: Model<ApprovalFlowDocument>,
    @InjectModel(Department.name) private departmentModel: Model<Department>,
    @InjectModel(Project.name) private projectModel: Model<Project>,
    private userService: UserService,
  ) {}

  async createApprovalFlow(approvalFlowData: any): Promise<ApprovalFlow> {
    // Verify department and project exist
    const department = await this.departmentModel.findById(approvalFlowData.department);
    if (!department) {
      throw new NotFoundException(`Department with ID ${approvalFlowData.department} not found`);
    }
    
    const project = await this.projectModel.findById(approvalFlowData.project);
    if (!project) {
      throw new NotFoundException(`Project with ID ${approvalFlowData.project} not found`);
    }
    
    const flowId = await this.generateFlowId(
      department.code,
      project.projectId,
      approvalFlowData.documentType
    );
    
    // Validate that each userId exists
    if (approvalFlowData.approvers && Array.isArray(approvalFlowData.approvers)) {
      for (const approver of approvalFlowData.approvers) {
        // Ensure the userId is a valid ObjectId
        if (!(approver.userId instanceof Types.ObjectId) && !Types.ObjectId.isValid(approver.userId)) {
          throw new Error(`Invalid userId for approver at position ${approver.order}`);
        }
        
        // Check if user exists
        await this.userService.getUserById(approver.userId);
      }
    }
    
    const approvalFlow = new this.approvalFlowModel({
      ...approvalFlowData,
      flowId,
    });

    return approvalFlow.save();
  }

  async getApprovalFlow(departmentId: string, projectId: string, documentType: string): Promise<ApprovalFlow> {
    // Convert string IDs to ObjectId if they're not already
    // const departmentObjectId = Types.ObjectId.isValid(departmentId) ? new Types.ObjectId(departmentId) : departmentId;
    // const projectObjectId = Types.ObjectId.isValid(projectId) ? new Types.ObjectId(projectId) : projectId;
    
    const approvalFlow = await this.approvalFlowModel.findOne({
      department: departmentId,
      project: projectId,
      documentType,
      isActive: true,
    }).populate('approvers.userId');
    
    if (!approvalFlow) {
      throw new NotFoundException('Approval flow not found for this department and project');
    }
    
    return approvalFlow;
  }

  async getAllApprovalFlows(filters: any = {}): Promise<ApprovalFlow[]> {
    const queryFilter: any = {
      isActive: true
    };
    
    if (filters.department) {
      queryFilter.department = new Types.ObjectId(filters.department);
    }
    
    if (filters.project) {
      queryFilter.project = new Types.ObjectId(filters.project);
    }
    
    if (filters.documentType) {
      queryFilter.documentType = filters.documentType;
    }
    
    return this.approvalFlowModel.find(queryFilter)
      .populate('department')
      .populate('project')
      .populate('approvers.userId')
      .sort({ createdAt: -1 });
  }

  async updateApprovalFlow(flowId: string, updateData: any): Promise<ApprovalFlow> {
    const approvalFlow = await this.approvalFlowModel.findOne({ flowId });
    
    if (!approvalFlow) {
      throw new NotFoundException('Approval flow not found');
    }
    
    // Validate users if approvers are being updated
    if (updateData.approvers && Array.isArray(updateData.approvers)) {
      for (const approver of updateData.approvers) {
        if (approver.userId && !(approver.userId instanceof Types.ObjectId) && !Types.ObjectId.isValid(approver.userId)) {
          throw new Error(`Invalid userId for approver`);
        }
        
        if (approver.userId) {
          await this.userService.getUserById(approver.userId);
        }
      }
    }
    
    Object.assign(approvalFlow, {
      ...updateData,
      updatedAt: new Date(),
    });
    
    return approvalFlow.save();
  }

  async deactivateApprovalFlow(flowId: string): Promise<ApprovalFlow> {
    const approvalFlow = await this.approvalFlowModel.findOne({ flowId });
    
    if (!approvalFlow) {
      throw new NotFoundException('Approval flow not found');
    }
    
    approvalFlow.isActive = false;
    approvalFlow.updatedAt = new Date();
    
    return approvalFlow.save();
  }

  private async generateFlowId(departmentCode: string, projectId: string, documentType: string): Promise<string> {
    const departmentCode3 = departmentCode.substring(0, 3).toUpperCase();
    const projectCode3 = projectId.substring(0, 3).toUpperCase();
    const typeCode = documentType.substring(0, 3).toUpperCase();
    
    const count = await this.approvalFlowModel.countDocuments({
      flowId: new RegExp(`FLOW-${departmentCode3}-${projectCode3}-${typeCode}`)
    });
    
    const sequence = (count + 1).toString().padStart(2, '0');
    return `FLOW-${departmentCode3}-${projectCode3}-${typeCode}-${sequence}`;
  }
}