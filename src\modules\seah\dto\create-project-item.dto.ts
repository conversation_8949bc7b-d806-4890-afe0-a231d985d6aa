import { IsString, IsN<PERSON>ber, IsDate, IsOptional, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateItemDto {
  @IsString()
  PONo: string;

  @IsString()
  BPCode: string;

  @IsString()
  BPName: string;

  @IsOptional()
  @Type(() => Date)
  PODate?: Date;

  @IsString()
  @IsOptional()
  Buyer?: string;

  @IsNumber()
  @IsOptional()
  GrossRM?: number;

  @IsNumber()
  @IsOptional()
  TaxRM?: number;

  @IsNumber()
  @IsOptional()
  TotalRM?: number;

  @IsString()
  @IsOptional()
  currency?: string;

  @IsNumber()
  @IsOptional()
  TotalOri?: number;

  @IsString()
  @IsOptional()
  term?: string;

  @IsString()
  @IsOptional()
  pjmCode?: string;

  @IsString()
  @IsOptional()
  signature?: string;

  @IsString()
  @IsOptional()
  poStatus?: string;

  @IsString()
  @IsOptional()
  grnStatus?: string;

  @IsString()
  @IsOptional()
  invoiceStatus?: string;

  @IsString()
  @IsOptional()
  department?: string;

  @IsString()
  @IsOptional()
  prjCode?: string;

  @IsString()
  @IsOptional()
  ppmIssued?: string;

  @IsString()
  @IsOptional()
  ppmpaid?: string;

  @IsBoolean()
  @IsOptional()
  isExcluded?: boolean;

  @IsString()
  @IsOptional()
  remark?: string;

  @IsString()
  @IsOptional()
  status?: string;

  @IsString()
  @IsOptional()
  type?: string;
}