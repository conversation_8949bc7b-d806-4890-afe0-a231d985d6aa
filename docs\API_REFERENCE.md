# UzmaCloud API Reference

## 🌐 Base Information
- **Base URL**: `http://localhost:3000` (development) / `https://uzmacloud.com` (production)
- **API Prefix**: `/api`
- **Content-Type**: `application/json`
- **Authentication**: <PERSON><PERSON> (JWT)

## 📋 Table of Contents
- [Authentication](#authentication)
- [Auto-Billing Module](#auto-billing-module)
- [Payment Advice Module](#payment-advice-module)
- [User Management](#user-management)
- [Face Recognition](#face-recognition)
- [Health Monitoring](#health-monitoring)

---

## 🔐 Authentication

### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "60f7b3b3b3b3b3b3b3b3b3b3",
      "email": "<EMAIL>",
      "fullName": "<PERSON>e",
      "role": "user"
    }
  }
}
```

### Protected Route Headers
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

---

## 🧾 Auto-Billing Module

Base path: `/api/billing`

### Create Billing Advice
```http
POST /api/billing
Authorization: Bearer <token>
Content-Type: application/json

{
  "department": "60f7b3b3b3b3b3b3b3b3b3b3",
  "project": "60f7b3b3b3b3b3b3b3b3b3b4",
  "billingDate": "2024-01-15T00:00:00.000Z",
  "currency": "MYR",
  "bankAccount": "**********",
  "paymentTerms": "NET 30",
  "lineItems": [
    {
      "product": "60f7b3b3b3b3b3b3b3b3b3b5",
      "description": "Software Development Services",
      "unit": "hours",
      "quantity": 100,
      "price": 150.00,
      "tax": 15.00,
      "total": 15015.00
    }
  ],
  "attachments": [
    {
      "fileName": "contract.pdf",
      "fileType": "application/pdf",
      "fileUrl": "https://s3.amazonaws.com/bucket/contract.pdf",
      "description": "Service contract"
    }
  ]
}
```

### Get Billing Advice
```http
GET /api/billing/{billingId}
Authorization: Bearer <token>
```

### Approve Billing
```http
PUT /api/billing/approve
Authorization: Bearer <token>
Content-Type: application/json

{
  "billingId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "userId": "60f7b3b3b3b3b3b3b3b3b3b4",
  "status": "approved",
  "comments": "Approved for processing"
}
```

### Generate Documents
```http
POST /api/billing/{billingId}/sale-order
POST /api/billing/{billingId}/delivery-order
POST /api/billing/{billingId}/invoice
Authorization: Bearer <token>
```

### Finance Approval
```http
PUT /api/billing/finance-approve
Authorization: Bearer <token>
Content-Type: application/json

{
  "billingId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "userId": "60f7b3b3b3b3b3b3b3b3b3b4",
  "status": "approved",
  "comments": "Finance approved"
}
```

---

## 💰 Payment Advice Module

Base path: `/api/payment-advice`

### Create Payment Advice
```http
POST /api/payment-advice/create
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": "encrypted_payload_here"
}
```

### Get Payment Advice List
```http
POST /api/payment-advice/list
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": "encrypted_filter_payload"
}
```

### Get Payment Advice Details
```http
POST /api/payment-advice/detail
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": "encrypted_id_payload"
}
```

### Update Bank Receipts
```http
PATCH /api/payment-advice/bank-receipts
Authorization: Bearer <token>
Content-Type: application/json

{
  "paymentAdviceId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "bankReceipts": [
    {
      "receiptNumber": "RCP-2024-001",
      "receiptDate": "2024-01-15T10:30:00.000Z",
      "amount": 5000.00,
      "bankName": "Maybank",
      "accountNumber": "**********",
      "s3Url": "https://s3.amazonaws.com/bucket/receipt.pdf",
      "remark": "Partial payment received"
    }
  ]
}
```

### Approve/Reject Workflow
```http
PUT /api/payment-advice/workflow/approve-reject
Authorization: Bearer <token>
Content-Type: application/json

{
  "paymentAdviceId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "userId": "60f7b3b3b3b3b3b3b3b3b3b4",
  "action": "approve",
  "comments": "Approved for payment"
}
```

### Upload Attachment
```http
POST /api/payment-advice/attachment
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <binary_file_data>
```

---

## 👥 User Management

Base path: `/api/users`

### Get User Profile
```http
GET /api/users/profile
Authorization: Bearer <token>
```

### Update User Profile
```http
PUT /api/users/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "fullName": "John Doe Updated",
  "phone": "+***********",
  "designation": "Senior Developer"
}
```

### Get Users List
```http
GET /api/users?page=1&limit=10&search=john
Authorization: Bearer <token>
```

---

## 👤 Face Recognition

Base path: `/api/face`

### Register Face
```http
POST /api/face/register
Authorization: Bearer <token>
Content-Type: multipart/form-data

userId: 60f7b3b3b3b3b3b3b3b3b3b3
image: <binary_image_data>
```

### Verify Face
```http
POST /api/face/verify
Authorization: Bearer <token>
Content-Type: multipart/form-data

userId: 60f7b3b3b3b3b3b3b3b3b3b3
image: <binary_image_data>
```

### Delete Face Data
```http
DELETE /api/face/{userId}
Authorization: Bearer <token>
```

---

## 🏥 Health Monitoring

### Basic Health Check
```http
GET /health
```

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600,
  "service": "uzmacloud-api"
}
```

### Detailed Health Check
```http
GET /health/detailed
```

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "service": "uzmacloud-api",
  "version": "1.0.0",
  "uptime": 3600,
  "database": {
    "status": "connected",
    "type": "MongoDB"
  },
  "aws": {
    "s3": "connected",
    "rekognition": "connected"
  },
  "environment": {
    "nodeEnv": "development",
    "port": 3000
  }
}
```

---

## 📊 Common Response Patterns

### Success Response
```json
{
  "success": true,
  "data": { /* response data */ },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Email is required"
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Pagination Response
```json
{
  "success": true,
  "data": {
    "items": [ /* array of items */ ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

---

## 🔧 HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error
