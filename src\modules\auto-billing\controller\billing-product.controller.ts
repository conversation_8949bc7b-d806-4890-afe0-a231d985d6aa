import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { CreateProductDto, UpdateProductDto, ProductResponseDto } from '../dto/billing-product.dto';
import { BillingProductService } from '../service/billing-product.service';

@Controller('auto-billing/products')
export class BillingProductController {
  constructor(private readonly productService: BillingProductService) {}

  @Post()
  async create(@Body() createProductDto: CreateProductDto) {
    const product = await this.productService.create(createProductDto);
    return this.mapToResponseDto(product);
  }

  @Get()
  async findAll(@Query('companyId') companyId?: string) {
    let products;
    if (companyId) {
      products = await this.productService.findByCompany(companyId);
    } else {
      products = await this.productService.findAll();
    }
    return products.map(product => this.mapToResponseDto(product));
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const product = await this.productService.findOne(id);
    return this.mapToResponseDto(product);
  }

  @Get('by-product-id/:productId')
  async findByProductId(@Param('productId') productId: string) {
    const product = await this.productService.findByProductId(productId);
    return this.mapToResponseDto(product);
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateProductDto: UpdateProductDto) {
    const product = await this.productService.update(id, updateProductDto);
    return this.mapToResponseDto(product);
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    const product = await this.productService.remove(id);
    return this.mapToResponseDto(product);
  }

  private mapToResponseDto(product: any): ProductResponseDto {
    return {
      id: product._id.toString(),
      productId: product.productId,
      productName: product.productName,
      departmentId: product.departmentId,
      projectId: product.projectId,
      description: product.description,
      unit: product.unit,
      price: product.price,
      tax: product.tax,
      isActive: product.isActive,
      additionalDetails: product.additionalDetails,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
    };
  }
}