import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Billing, BillingDocument } from '../schema/billing.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Product } from '../schema/billing-product.schema';
import { Project } from '../schema/billing-project.schema';
import { catchError, firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { BillingService } from './billing.service';

@Injectable()
export class RpaService {
  private readonly logger = new Logger(RpaService.name);

  constructor(
    private httpService: HttpService,
    private configService: ConfigService,
    @InjectModel(Billing.name) private billingModel: Model<BillingDocument>,
    @InjectModel(Product.name) private productModel: Model<Product>,
    @Inject(forwardRef(() => BillingService)) // ✅ fix here
    private billingService: BillingService,
  ) { }

  async generateSaleOrder(billing: Billing, taskId: any): Promise<{ documentId: string; fileUrl: string }> {
    this.logger.log(`Generating Sale Order for billing ${billing._id}`);
    const rpaEndpoint = this.configService.get('RPA_ENDPOINT');
    const token = this.configService.get('RPA_API_TOKEN');

    try {
      // Convert billing to RPA API format
      const rpaData = await this.convertBillingToRpaFormat(billing, taskId);

      // Call RPA API to generate sale order with bearer token
      const { data } = await firstValueFrom(
        this.httpService.post(
          `${rpaEndpoint}/Trigger_SalesOrderCreation`,
          { injason: JSON.stringify(rpaData) },
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        ).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error generating Sale Order: ${error.message}`);
            throw new Error(`Failed to generate Sale Order: ${error.response?.data || error.message}`);
          })
        )
      );

      if (!data || !data.id || !data.key) {
        throw new Error('Invalid response from RPA service for Sale Order');
      }

      this.logger.log(`Successfully generated Sale Order: ${data.documentId}`);

      return {
        documentId: data.documentId,
        fileUrl: data.fileUrl
      };
    } catch (error) {
      this.logger.error(`Failed to generate Sale Order: ${error.message}`);
      throw error; // Re-throw to let the scheduler handle the error
    }
  }

  async generateDeliveryOrder(billing: Billing, taskId: any): Promise<{ documentId: string; fileUrl: string }> {
    this.logger.log(`Generating Delivery Order for billing ${billing._id}`);
    const rpaEndpoint = this.configService.get('RPA_ENDPOINT');
    const token = this.configService.get('RPA_API_TOKEN');

    // Verify prerequisites
    if (!billing.saleOrderId) {
      throw new Error('Cannot generate Delivery Order without a Sale Order ID');
    }

    try {
      // Create the data structure required for delivery order
      const doRequest = await this.prepareDeliveryOrderRequest(billing, taskId);

      // Call RPA API to generate delivery order
      const { data } = await firstValueFrom(
        this.httpService.post(
          `${rpaEndpoint}/Trigger_Delivery_Order_Creation`,
          { injason: JSON.stringify(doRequest) },
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        ).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error generating Delivery Order: ${error.message}`);
            throw new Error(`Failed to generate Delivery Order: ${error.response?.data || error.message}`);
          })
        )
      );

      if (!data || !data.id || !data.key) {
        throw new Error('Invalid response from RPA service for Delivery Order');
      }

      this.logger.log(`Successfully generated Delivery Order: ${data.documentId}`);

      return {
        documentId: data.documentId,
        fileUrl: data.fileUrl
      };
    } catch (error) {
      this.logger.error(`Failed to generate Delivery Order: ${error.message}`);
      throw error; // Re-throw to let the scheduler handle the error
    }
  }

  async generateInvoice(billing: Billing, taskId: any): Promise<{ documentId: string; fileUrl: string }> {
    this.logger.log(`Generating Invoice for billing ${billing._id}`);
    const rpaEndpoint = this.configService.get('RPA_ENDPOINT');
    const token = this.configService.get('RPA_API_TOKEN');

    // Verify prerequisites
    if (!billing.saleOrderId) {
      throw new Error('Cannot generate Invoice without a Sale Order ID');
    }

    if (!billing.deliveryOrderId) {
      throw new Error('Cannot generate Invoice without a Delivery Order ID');
    }

    try {

      // Create the data structure required for delivery order
      const invoiceRequest = await this.prepareInvoiceRequest(billing, taskId);

      // Call RPA API to generate invoice
      const { data } = await firstValueFrom(
        this.httpService.post(
          `${rpaEndpoint}/Trigger_Invoice_Creation`,
          { injason: JSON.stringify(invoiceRequest) },
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        ).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error generating Invoice: ${error.message}`);
            throw new Error(`Failed to generate Invoice: ${error.response?.data || error.message}`);
          })
        )
      );

      if (!data || !data.id || !data.key) {
        throw new Error('Invalid response from RPA service for Invoice');
      }

      this.logger.log(`Successfully generated Invoice: ${data.documentId}`);

      return {
        documentId: data.documentId,
        fileUrl: data.fileUrl
      };
    } catch (error) {
      this.logger.error(`Failed to generate Invoice: ${error.message}`);
      throw error; // Re-throw to let the scheduler handle the error
    }
  }

  async postFinanceInvoice(billing: Billing, taskId: any): Promise<{ documentId: string; fileUrl: string }> {
    this.logger.log(`Posting Invoice for billing ${billing._id}`);
    const rpaEndpoint = this.configService.get('RPA_ENDPOINT');
    const token = this.configService.get('RPA_API_TOKEN');

    // Verify prerequisites
    if (!billing.invoiceId) {
      throw new Error('Cannot post Invoice without a Sale Order ID');
    }

    // Check status - only schedule if approved
    if (billing.status !== 'finance_approved') {
      this.logger.log(`Cannot schedule Post Invoice tasks yet for billing ${billing._id} -finance not approved`);
    }

    try {

      // Create the data structure required for delivery order
      const postInvoiceRequest = await this.preparePostInvoiceRequest(billing, taskId);

      // Call RPA API to post invoice
      const { data } = await firstValueFrom(
        this.httpService.post(
          `${rpaEndpoint}/Trigger_PostInvoice`,
          { injason: JSON.stringify(postInvoiceRequest) },
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        ).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error posting Invoice: ${error.message}`);
            throw new Error(`Failed to post Invoice: ${error.response?.data || error.message}`);
          })
        )
      );

      if (!data || !data.id || !data.key) {
        throw new Error('Invalid response from RPA service for Invoice');
      }

      this.logger.log(`Successfully posted Invoice: ${data.documentId}`);

      return {
        documentId: data.documentId,
        fileUrl: data.fileUrl
      };
    } catch (error) {
      this.logger.error(`Failed to generate Invoice: ${error.message}`);
      throw error; // Re-throw to let the scheduler handle the error
    }
  }

  private async prepareDeliveryOrderRequest(billing: Billing, taskId: any): Promise<any> {
    // Populate department, project, and products if not already populated
    if (!billing.department || typeof billing.department === 'string' || billing.department instanceof Types.ObjectId) {
      billing = await this.populateBillingReferences(billing);
    }

    const project = billing.project as any;

    const workDoneDate = new Date(billing.billingDate);
    const formattedDate = `${workDoneDate.getDate().toString().padStart(2, '0')}/${
      (workDoneDate.getMonth() + 1).toString().padStart(2, '0')}/${
      workDoneDate.getFullYear().toString().slice(-2)}`;

    const request  = {
      ShipmentSite: project.shipmentSites,
      SalesSite: project.saleSites,
      SalesOrderNumber: billing.saleOrderId,
      UZMAbankselection: billing.bankAccount,
      workdonedate: formattedDate, //2025-05-05T00:00:00.000+00:00
      JCTreportcode: project.doReportCode,
      taskId: taskId,
    }

    return request;
  }

  private async prepareInvoiceRequest(billing: Billing, taskId: any): Promise<any> {
    // Populate department, project, and products if not already populated
    if (!billing.department || typeof billing.department === 'string' || billing.department instanceof Types.ObjectId) {
      billing = await this.populateBillingReferences(billing);
    }

    const project = billing.project as any;
    
    const request  = {
      DeliveryOrderNumber: billing.deliveryOrderId,
      INVreportcode: project.invoiceReportCode,
      Comment: "-",
      taskId: taskId,
    }

    return request;
  }

  private async preparePostInvoiceRequest(billing: Billing, taskId: any): Promise<any> {
    // Populate department, project, and products if not already populated
    if (!billing.department || typeof billing.department === 'string' || billing.department instanceof Types.ObjectId) {
      billing = await this.populateBillingReferences(billing);
    }

    const project = billing.project as any;

    const request  = {
      InvNumber: billing.invoiceId,
      INVreportcode: project.invoiceReportCode,
      taskId: taskId,
    }
    return request;
  }
  
  /**
  * Converts a Billing object to the format required by RPA API
  */
  private async convertBillingToRpaFormat(billing: Billing, taskId: string): Promise<any> {
    // Populate department, project, and products if not already populated
    if (!billing.department || typeof billing.department === 'string' || billing.department instanceof Types.ObjectId) {
      billing = await this.populateBillingReferences(billing);
    }

    // Extract department and project data
    const department = billing.department as any;
    const project = billing.project as any;

    // Create the BA (Business Area) object with required fields
    const BA = {
      SalesSite: project.saleSites,
      SoldTo: project.bpCode, // Default
      Project: project.projectName,
      ShipmentSite: project.shipmentSites,
      DepartmentAnalyticalDimension: department.code,
      ProjectAnalyticalDimension: project.projectCode,
      BizActivityAnalyticalDimension: project.bizActivity,
      Salesreportcode: project.soReportCode,
      PaymentTerm: billing.paymentTerms,
    };

    // Create line items array
    const lineItems = await Promise.all(billing.lineItems.map(async (item) => {
      // Get product details if it's an ObjectId reference
      let product;
      if (item.product instanceof Types.ObjectId || typeof item.product === 'string') {
        product = await this.productModel.findById(item.product);
      } else {
        product = item.product as any;
      }

      // Extract month and year from the billing date for description
      const billingDate = new Date(billing.billingDate);
      const month = billingDate.toLocaleString('en-US', { month: 'long' }).toUpperCase();
      const year = billingDate.getFullYear();

      return {
        Product: product.productName,
        Description: `${product.productName} - ${item.description}`,
        StandardDescription: `[${product.productName}]`,
        ShipSite: project.shipmentSites,
        SAL: item.unit,
        OrderedQty: item.quantity,
        GrossPrice: item.price
      };
    }));

    // Get file attachments - using a placeholder PDF for now
    const fileAttachments = billing.attachments.map(att => att.fileUrl);
    // const fileAttachments = ["https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"];

    // Return the complete RPA data structure
    return {
      BA,
      lineItems,
      fileAttachments,
      taskId
    };
  }

  /**
   * Populates the department, project, and product references in the billing object
   */
  private async populateBillingReferences(billing: Billing): Promise<Billing> {
    // Use findById and populate to get a fully populated document
    const populatedBilling = await this.billingModel.findById(billing._id)
      .populate('department')
      .populate('project')
      .populate({
        path: 'lineItems.product',
        model: 'Product'
      })
      .exec();

    if (!populatedBilling) {
      throw new Error(`Billing with ID ${billing._id} not found during population`);
    }

    return populatedBilling;
  }
}