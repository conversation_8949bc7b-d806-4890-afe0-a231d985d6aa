# Development Guide

## 🛠️ Development Setup

### Prerequisites
- **Node.js** v18+
- **MongoDB** (local or Docker)
- **Git**
- **VS Code** (recommended)

### Quick Start
```bash
# Clone and setup
git clone <repository-url>
cd uzmacloud-api
npm install
cp .env.example .env

# Start development server
npm run start:dev
```

## 📁 Project Structure

```
src/
├── modules/              # Feature modules
│   ├── auto-billing/     # Automated billing system
│   │   ├── controller/   # HTTP controllers
│   │   ├── service/      # Business logic
│   │   ├── schema/       # MongoDB schemas
│   │   ├── dto/          # Data transfer objects
│   │   └── auto-billing.module.ts
│   ├── pa/              # Payment advice module
│   ├── user/            # User management
│   ├── auth/            # Authentication
│   ├── face/            # Face recognition
│   ├── common/          # Shared utilities
│   └── ...
├── config/              # Configuration files
├── main.ts             # Application entry point
└── app.module.ts       # Root module
```

## 🏗️ Architecture Patterns

### Module Structure
Each module follows NestJS conventions:

```typescript
// Module definition
@Module({
  imports: [MongooseModule.forFeature([...])],
  controllers: [ExampleController],
  providers: [ExampleService],
  exports: [ExampleService]
})
export class ExampleModule {}
```

### Controller Pattern
```typescript
@Controller('example')
export class ExampleController {
  constructor(private readonly service: ExampleService) {}

  @Post()
  async create(@Body() dto: CreateExampleDto) {
    return this.service.create(dto);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.service.findOne(id);
  }
}
```

### Service Pattern
```typescript
@Injectable()
export class ExampleService {
  constructor(
    @InjectModel(Example.name) 
    private model: Model<ExampleDocument>
  ) {}

  async create(dto: CreateExampleDto): Promise<Example> {
    const created = new this.model(dto);
    return created.save();
  }
}
```

### Schema Pattern
```typescript
@Schema({ timestamps: true, collection: 'examples' })
export class Example {
  @Prop({ required: true })
  name: string;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  createdBy: Types.ObjectId;
}

export const ExampleSchema = SchemaFactory.createForClass(Example);
```

## 🔧 Development Workflow

### 1. Feature Development
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and test
npm run start:dev
npm run test

# Commit changes
git add .
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/new-feature
```

### 2. Code Quality
```bash
# Linting
npm run lint

# Formatting
npm run format

# Type checking
npm run build
```

### 3. Testing
```bash
# Unit tests
npm run test

# Watch mode
npm run test:watch

# Coverage
npm run test:cov

# E2E tests
npm run test:e2e
```

## 📝 Coding Standards

### TypeScript Guidelines
- Use **strict mode** enabled
- Prefer **interfaces** over types for object shapes
- Use **enums** for constants
- Always specify **return types** for functions

```typescript
// Good
interface CreateUserDto {
  email: string;
  fullName: string;
  role: UserRole;
}

enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  MANAGER = 'manager'
}

async function createUser(dto: CreateUserDto): Promise<User> {
  // implementation
}
```

### NestJS Best Practices
- Use **dependency injection** properly
- Implement **proper error handling**
- Use **DTOs** for validation
- Follow **module boundaries**

```typescript
// Good service implementation
@Injectable()
export class UserService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private readonly logger: Logger
  ) {}

  async create(dto: CreateUserDto): Promise<User> {
    try {
      const user = new this.userModel(dto);
      const saved = await user.save();
      this.logger.log(`User created: ${saved.email}`);
      return saved;
    } catch (error) {
      this.logger.error(`Failed to create user: ${error.message}`);
      throw new BadRequestException('Failed to create user');
    }
  }
}
```

### Database Guidelines
- Use **proper indexing** for queries
- Implement **data validation** in schemas
- Use **population** for references
- Handle **transactions** for complex operations

```typescript
// Good schema with validation
@Schema({ timestamps: true })
export class User {
  @Prop({ 
    required: true, 
    unique: true,
    validate: {
      validator: (email: string) => /\S+@\S+\.\S+/.test(email),
      message: 'Invalid email format'
    }
  })
  email: string;

  @Prop({ required: true, minlength: 2, maxlength: 100 })
  fullName: string;
}
```

## 🧪 Testing Guidelines

### Unit Testing
```typescript
describe('UserService', () => {
  let service: UserService;
  let model: Model<UserDocument>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: getModelToken(User.name),
          useValue: mockUserModel,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    model = module.get<Model<UserDocument>>(getModelToken(User.name));
  });

  it('should create a user', async () => {
    const dto = { email: '<EMAIL>', fullName: 'Test User' };
    const result = await service.create(dto);
    
    expect(result).toBeDefined();
    expect(result.email).toBe(dto.email);
  });
});
```

### E2E Testing
```typescript
describe('UserController (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/users (POST)', () => {
    return request(app.getHttpServer())
      .post('/users')
      .send({ email: '<EMAIL>', fullName: 'Test User' })
      .expect(201)
      .expect((res) => {
        expect(res.body.email).toBe('<EMAIL>');
      });
  });
});
```

## 🔍 Debugging

### VS Code Configuration
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug NestJS",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/src/main.ts",
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "restart": true,
      "protocol": "inspector"
    }
  ]
}
```

### Logging
```typescript
// Use NestJS Logger
import { Logger } from '@nestjs/common';

@Injectable()
export class ExampleService {
  private readonly logger = new Logger(ExampleService.name);

  async someMethod() {
    this.logger.log('Processing started');
    this.logger.debug('Debug information');
    this.logger.warn('Warning message');
    this.logger.error('Error occurred', error.stack);
  }
}
```

## 🚀 Performance Tips

### Database Optimization
```typescript
// Use indexes
@Schema({ timestamps: true })
export class User {
  @Prop({ index: true })
  email: string;

  @Prop({ index: true })
  department: Types.ObjectId;
}

// Use lean queries for read-only operations
const users = await this.userModel.find().lean().exec();

// Use select to limit fields
const users = await this.userModel
  .find()
  .select('email fullName')
  .exec();
```

### Caching
```typescript
// Use caching for expensive operations
@Injectable()
export class UserService {
  private cache = new Map();

  async getExpensiveData(key: string) {
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }

    const data = await this.performExpensiveOperation(key);
    this.cache.set(key, data);
    return data;
  }
}
```

## 🔧 Common Patterns

### Error Handling
```typescript
// Global exception filter
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();

    const status = exception instanceof HttpException 
      ? exception.getStatus() 
      : HttpStatus.INTERNAL_SERVER_ERROR;

    response.status(status).json({
      success: false,
      error: {
        code: exception.constructor.name,
        message: exception.message,
        timestamp: new Date().toISOString(),
        path: request.url,
      },
    });
  }
}
```

### Validation
```typescript
// DTO with validation
export class CreateUserDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @Length(2, 100)
  fullName: string;

  @IsEnum(UserRole)
  role: UserRole;
}
```

### Authentication Guard
```typescript
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  canActivate(context: ExecutionContext) {
    return super.canActivate(context);
  }

  handleRequest(err, user, info) {
    if (err || !user) {
      throw err || new UnauthorizedException();
    }
    return user;
  }
}
```

## 📚 Useful Commands

```bash
# Development
npm run start:dev          # Start with hot reload
npm run start:debug        # Start with debugger

# Testing
npm run test               # Run unit tests
npm run test:watch         # Watch mode
npm run test:cov           # Coverage report
npm run test:e2e           # End-to-end tests

# Code Quality
npm run lint               # ESLint
npm run format             # Prettier
npm run build              # TypeScript compilation

# Database
npm run migration:run      # Run migrations
npm run seed:run           # Run seeders

# Custom scripts (add to package.json)
npm run api:test           # Test API endpoints
npm run db:reset           # Reset database
```

## 🔗 Useful Resources

- [NestJS Documentation](https://docs.nestjs.com/)
- [Mongoose Documentation](https://mongoosejs.com/docs/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Jest Testing Framework](https://jestjs.io/docs/getting-started)
- [MongoDB Best Practices](https://docs.mongodb.com/manual/administration/production-notes/)
