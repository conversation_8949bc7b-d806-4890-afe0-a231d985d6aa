// workflow-builder.service.ts
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { IppaUser, IppaUserDocument } from '../schema/ippa-user.schema';
import {
  Department,
  DepartmentDocument,
} from 'src/modules/user/schema/department.schema';

@Injectable()
export class IppaWorkflowService {
  constructor(
    @InjectModel(Department.name)
    private departmentModel: Model<DepartmentDocument>,
    @InjectModel(IppaUser.name)
    private ippaUserModel: Model<IppaUserDocument>,
  ) {}

  /**
   * Build enhanced approval workflow with HOD configuration
   */
  async buildEnhancedApprovalWorkflow(
    employeeData: any,
    options?: {
      forceIncludeHOD?: boolean;
      customHRReviewer?: { id: string; name: string };
    },
  ): Promise<any[]> {
    const steps: any[] = [];
    let stepId = 1;

    // Step 1: Employee Submission
    steps.push({
      stepId: stepId++,
      title: 'Prepared by',
      personId: employeeData._id,
      personName: employeeData.fullName,
      department: employeeData.department,
      date: new Date().toLocaleString('en-GB'),
      status: 'completed',
      isRequired: true,
      stepType: 'employee',
    });

    // Step 2: Line Manager Review (Always required)
    if (employeeData.lineManager) {
      steps.push({
        stepId: stepId++,
        title: 'Approved by',
        personId: employeeData.lineManager._id,
        personName: employeeData.lineManager.fullName,
        department:
          employeeData.lineManager.department || employeeData.department,
        date: '-',
        status: 'pending',
        isRequired: true,
        stepType: 'supervisor',
      });
    }

    // Step 3: HOD Review (Based on department configuration)
    const hodStep = await this.checkAndAddHODStep(
      employeeData,
      stepId,
      options?.forceIncludeHOD,
    );
    if (hodStep) {
      steps.push(hodStep);
      stepId++;
    }

    // Final Step: HR Review
    const hrReviewer =
      options?.customHRReviewer || (await this.getDefaultHRReviewer());
    if (hrReviewer) {
      steps.push({
        stepId: stepId++,
        title: 'Reviewed by',
        personId: new Types.ObjectId(hrReviewer.id),
        personName: hrReviewer.name,
        department: 'HR',
        date: '-',
        status: 'upcoming',
        isRequired: true,
        stepType: 'hr',
      });
    }

    return steps;
  }

  /**
   * Check if HOD step should be included and create the step
   */
  private async checkAndAddHODStep(
    employeeData: any,
    stepId: number,
    forceInclude?: boolean,
  ): Promise<any | null> {
    // Get department configuration
    const department = await this.departmentModel
      .findOne({
        code: employeeData.departmentCode,
        isActive: true,
      })
      .populate('hodPersonId', 'fullName email department')
      .lean();

    if (!department) {
      console.warn(
        `Department not found for code: ${employeeData.departmentCode}`,
      );
      return null;
    }

    // Check if HOD approval is enabled for this department
    const shouldIncludeHOD = forceInclude || department.enableHodApproval;

    if (!shouldIncludeHOD || !department.hodPersonId) {
      return null;
    }

    // Ensure HOD is different from line manager
    const hodId = department.hodPersonId._id || department.hodPersonId;
    const lineManagerId = employeeData.lineManager?._id;

    if (hodId.toString() === lineManagerId?.toString()) {
      console.log('HOD is same as line manager, skipping HOD step');
      return null;
    }

    return {
      stepId: stepId,
      title: 'Approved by (HOD)',
      personId: hodId,
      personName: (department.hodPersonId as any)?.fullName || 'HOD',
      department: department.description,
      date: '-',
      status: 'upcoming',
      isRequired: department.enableHodApproval,
      stepType: 'hod',
    };
  }

  /**
   * Get default HR reviewer
   */
  private async getDefaultHRReviewer(): Promise<{
    id: string;
    name: string;
  } | null> {
    // You can implement logic to find HR users
    // For now, return the static HR reviewer
    return {
      id: '674829e0f3d796aa36ff1e0d',
      name: 'Zamhari Zaidi',
    };
  }

  /**
   * Update workflow when moving to next step
   */
  async updateWorkflowStep(
    workflow: any[],
    currentStepId: number,
    approverId: Types.ObjectId,
  ): Promise<{
    nextStatus: string;
    nextReviewerId: Types.ObjectId | null;
    nextStepId: number;
  }> {
    // Mark current step as completed
    const currentStep = workflow.find((step) => step.stepId === currentStepId);
    if (currentStep) {
      currentStep.status = 'completed';
      currentStep.date = new Date().toLocaleString('en-GB');
    }

    // Find next step
    const nextStep = workflow.find((step) => step.status === 'upcoming');

    if (nextStep) {
      nextStep.status = 'pending';

      // Determine next status based on step type
      let nextStatus: string;
      switch (nextStep.stepType) {
        case 'supervisor':
          nextStatus = 'pending_supervisor_review';
          break;
        case 'hod':
          nextStatus = 'pending_hod_review';
          break;
        case 'hr':
          nextStatus = 'pending_hr_review';
          break;
        default:
          nextStatus = 'pending_review';
      }

      return {
        nextStatus,
        nextReviewerId: nextStep.personId,
        nextStepId: nextStep.stepId,
      };
    }

    // No more steps - should be completed
    return {
      nextStatus: 'completed',
      nextReviewerId: null,
      nextStepId: workflow.length,
    };
  }

  /**
   * Get workflow status for display
   */
  getWorkflowStatusInfo(workflow: any[]): {
    currentStep: number;
    totalSteps: number;
    completedSteps: number;
    pendingSteps: number;
    upcomingSteps: number;
    currentStepTitle: string;
    nextStepTitle: string;
    isCompleted: boolean;
  } {
    const totalSteps = workflow.length;
    const completedSteps = workflow.filter(
      (step) => step.status === 'completed',
    ).length;
    const pendingSteps = workflow.filter(
      (step) => step.status === 'pending',
    ).length;
    const upcomingSteps = workflow.filter(
      (step) => step.status === 'upcoming',
    ).length;

    const currentStep = workflow.find((step) => step.status === 'pending');
    const nextStep = workflow.find((step) => step.status === 'upcoming');

    return {
      currentStep: completedSteps + 1,
      totalSteps,
      completedSteps,
      pendingSteps,
      upcomingSteps,
      currentStepTitle: currentStep?.title || 'Completed',
      nextStepTitle: nextStep?.title || 'No further steps',
      isCompleted: completedSteps === totalSteps,
    };
  }

  /**
   * Check if user can approve at current step
   */
  canUserApproveStep(
    workflow: any[],
    userId: Types.ObjectId,
  ): {
    canApprove: boolean;
    stepType: string;
    stepTitle: string;
    isRequired: boolean;
  } {
    const currentStep = workflow.find((step) => step.status === 'pending');

    if (!currentStep) {
      return {
        canApprove: false,
        stepType: 'none',
        stepTitle: 'No pending step',
        isRequired: false,
      };
    }

    const canApprove = currentStep.personId.toString() === userId.toString();

    return {
      canApprove,
      stepType: currentStep.stepType,
      stepTitle: currentStep.title,
      isRequired: currentStep.isRequired,
    };
  }

  /**
   * Get all pending approvals for a user across all submissions
   */
  async getUserPendingApprovals(userId: Types.ObjectId): Promise<{
    supervisorApprovals: number;
    hodApprovals: number;
    hrApprovals: number;
    totalPending: number;
  }> {
    // This would typically query the IPPA submissions
    // For now, return mock data
    return {
      supervisorApprovals: 0,
      hodApprovals: 0,
      hrApprovals: 0,
      totalPending: 0,
    };
  }

  /**
   * Validate workflow configuration
   */
  validateWorkflow(workflow: any[]): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Check for required steps
    const requiredStepTypes = ['employee', 'supervisor', 'hr'];
    const presentStepTypes = workflow.map((step) => step.stepType);

    requiredStepTypes.forEach((requiredType) => {
      if (!presentStepTypes.includes(requiredType)) {
        errors.push(`Missing required step type: ${requiredType}`);
      }
    });

    // Check step ID sequence
    const stepIds = workflow.map((step) => step.stepId).sort((a, b) => a - b);
    for (let i = 0; i < stepIds.length; i++) {
      if (stepIds[i] !== i + 1) {
        errors.push(`Step IDs must be sequential starting from 1`);
        break;
      }
    }

    // Check for duplicate persons in workflow
    const personIds = workflow.map((step) => step.personId.toString());
    const uniquePersonIds = [...new Set(personIds)];
    if (personIds.length !== uniquePersonIds.length) {
      errors.push('Duplicate persons found in workflow');
    }

    // Check status consistency
    const completedSteps = workflow.filter(
      (step) => step.status === 'completed',
    );
    const pendingSteps = workflow.filter((step) => step.status === 'pending');
    const upcomingSteps = workflow.filter((step) => step.status === 'upcoming');

    if (pendingSteps.length > 1) {
      errors.push('Only one step can be pending at a time');
    }

    if (
      completedSteps.length > 0 &&
      pendingSteps.length === 0 &&
      upcomingSteps.length > 0
    ) {
      errors.push(
        'Cannot have upcoming steps without a pending step when workflow is in progress',
      );
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get all departments with HOD configuration
   */
  async getAllDepartmentsWithHODConfig(): Promise<any[]> {
    const departments = await this.departmentModel
      .find({ isActive: true })
      .populate('hodPersonId', 'fullName email department')
      .lean();

    return departments.map((dept) => ({
      departmentCode: dept.code,
      description: dept.description,
      division: dept.division,
      enableHodApproval: dept.enableHodApproval || false,
      hodPersonId: dept.hodPersonId,
      hodWorkflowOrder: dept.hodWorkflowOrder || 2,
      isActive: dept.isActive,
    }));
  }

  /**
   * Get department configuration
   */
  async getDepartmentConfig(departmentCode: string): Promise<any> {
    const department = await this.departmentModel
      .findOne({
        code: departmentCode,
        isActive: true,
      })
      .populate('hodPersonId', 'fullName email department')
      .lean();

    if (!department) {
      throw new Error(`Department not found for code: ${departmentCode}`);
    }

    return {
      code: department.code,
      description: department.description,
      division: department.division,
      enableHodApproval: department.enableHodApproval || false,
      hodPersonId: department.hodPersonId,
      hodWorkflowOrder: department.hodWorkflowOrder || 2,
      isActive: department.isActive,
    };
  }

  /**
   * Get workflow template for department
   */
  async getWorkflowTemplate(departmentCode: string): Promise<any> {
    const department = await this.getDepartmentConfig(departmentCode);

    // Mock employee data for template
    const mockEmployee = {
      _id: new Types.ObjectId(),
      fullName: 'Template Employee',
      department: department.description,
      departmentCode: departmentCode,
      lineManager: {
        _id: new Types.ObjectId(),
        fullName: 'Line Manager',
        department: department.description,
      },
    };

    const template = await this.buildEnhancedApprovalWorkflow(mockEmployee);

    return {
      departmentCode,
      departmentName: department.description,
      enableHodApproval: department.enableHodApproval,
      workflowSteps: template.map((step) => ({
        stepId: step.stepId,
        title: step.title,
        stepType: step.stepType,
        isRequired: step.isRequired,
        description: this.getStepDescription(step.stepType),
      })),
      estimatedDuration: this.calculateEstimatedDuration(template),
    };
  }

  /**
   * Get workflow history for submission
   */
  async getWorkflowHistory(submissionId: string): Promise<any> {
    // This would typically fetch from your IPPA submission model
    // For now, returning a mock structure
    const mockWorkflow = [
      {
        stepId: 1,
        title: 'Prepared by',
        stepType: 'employee',
        status: 'completed',
        date: '2024-01-15 10:30:00',
        personName: 'John Doe',
        department: 'IT',
        comments: 'Initial submission completed',
      },
      {
        stepId: 2,
        title: 'Approved by',
        stepType: 'supervisor',
        status: 'completed',
        date: '2024-01-16 14:20:00',
        personName: 'Jane Smith',
        department: 'IT',
        comments: 'Approved with excellent ratings',
      },
      {
        stepId: 3,
        title: 'Approved by (HOD)',
        stepType: 'hod',
        status: 'pending',
        date: '-',
        personName: 'Mike Johnson',
        department: 'IT',
        comments: null,
      },
      {
        stepId: 4,
        title: 'Reviewed by',
        stepType: 'hr',
        status: 'upcoming',
        date: '-',
        personName: 'Zamhari Zaidi',
        department: 'HR',
        comments: null,
      },
    ];

    const statusInfo = this.getWorkflowStatusInfo(mockWorkflow);

    return {
      submissionId,
      steps: mockWorkflow,
      currentStep: statusInfo.currentStep,
      statusInfo,
      timeline: this.generateWorkflowTimeline(mockWorkflow),
      estimatedCompletion: this.calculateEstimatedCompletion(mockWorkflow),
    };
  }

  /**
   * Get step description for workflow template
   */
  private getStepDescription(stepType: string): string {
    const descriptions = {
      employee: 'Employee submits IPPA form with self-assessment',
      supervisor: 'Line Manager reviews and provides ratings/feedback',
      hod: 'Head of Department approves (if enabled for department)',
      hr: 'HR Department completes final review and processing',
    };
    return descriptions[stepType] || 'Unknown step';
  }

  /**
   * Calculate estimated duration for workflow
   */
  private calculateEstimatedDuration(workflow: any[]): number {
    const stepDurations = {
      employee: 1, // 1 day
      supervisor: 3, // 3 days
      hod: 2, // 2 days
      hr: 2, // 2 days
    };

    return workflow.reduce((total, step) => {
      return total + (stepDurations[step.stepType] || 1);
    }, 0);
  }

  /**
   * Generate workflow timeline
   */
  private generateWorkflowTimeline(workflow: any[]): any[] {
    return workflow.map((step) => ({
      stepId: step.stepId,
      title: step.title,
      status: step.status,
      date: step.date,
      personName: step.personName,
      department: step.department,
      duration: this.calculateStepDuration(step),
      isCompleted: step.status === 'completed',
      isPending: step.status === 'pending',
      isUpcoming: step.status === 'upcoming',
    }));
  }

  /**
   * Calculate step duration
   */
  private calculateStepDuration(step: any): number {
    if (step.status !== 'completed' || step.date === '-') {
      return 0;
    }

    // This would calculate actual duration based on timestamps
    // For now, return mock duration in hours
    return Math.floor(Math.random() * 48) + 1;
  }

  /**
   * Calculate estimated completion date
   */
  private calculateEstimatedCompletion(workflow: any[]): string {
    const pendingSteps = workflow.filter((step) => step.status !== 'completed');
    const remainingDays = pendingSteps.length * 2; // 2 days per step average

    const estimatedDate = new Date();
    estimatedDate.setDate(estimatedDate.getDate() + remainingDays);

    return estimatedDate.toISOString().split('T')[0];
  }
}
