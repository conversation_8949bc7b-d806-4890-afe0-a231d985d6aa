import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { LOA, LoaDocument } from '../schema/pa-loa.schema';
import { PASerialNumberService } from './pa-serial-no.service';
import { FieldRequest, FieldRequestDocument } from '../schema/fr-main.schema';
import { FieldRequestWorkflow, FieldRequestWorkflowDocument } from '../schema/fr-workflow.schema';
import { FieldRequestWorkflowDto } from '../dto/fr-workflow.dto';
import { SecurityService } from 'src/modules/user/service/security.service';
import { User, UserDocument } from 'src/modules/user/schema/user.schema';
import { EmailScheduler, EmailSchedulerDocument } from 'src/modules/common/schema/pa-email.schema';

@Injectable()
export class FieldRequestService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(LOA.name) private loaModel: Model<LoaDocument>,
    @InjectModel(FieldRequest.name) private fieldRequestModel: Model<FieldRequestDocument>,
    @InjectModel(FieldRequestWorkflow.name) private fieldRequestWorkflowModel: Model<FieldRequestWorkflowDocument>,
    @InjectModel(EmailScheduler.name) private emailSchedulerModel: Model<EmailSchedulerDocument>,
    private readonly serialNumberService: PASerialNumberService,
    private readonly securityService: SecurityService
  ) { }

  async createFieldRequest(encryptedData : any): Promise<any>{
    const createDto = this.securityService.decrypt(encryptedData);

    const prefix = 'UZMA-EFR';
    const serialNumber = await this.serialNumberService.getNextSerialNumber(prefix);

    const fieldRequest = new this.fieldRequestModel({
      ...createDto,
      serialNumber
    });
    const saveFieldRequest = await fieldRequest.save();

    const loa = await this.loaModel
      .findOne({ departmentId: new Types.ObjectId(createDto.departmentCode), currency: createDto.currency.toUpperCase(), identifier: createDto.identifier , loaType: 'FR'})
      .populate({
        path: 'preparers',
        model: 'User',
        select: 'name email fullName designation faceImage'
      })  // Populate preparers (userId array)
      .populate({
        path: 'finance',
        model: 'User',
        select: 'name email fullName designation faceImage'
      })    // Populate finance (userId array)
      .populate({
        path: 'approvalLimits.approvers',  // Populate approvers inside each approvalLimit
        model: 'User',  // Specify the model explicitly if necessary
        select: 'name email fullName designation faceImage'  // Select the fields you need from the User model
      })
      .exec();

    // console.log("LOA ", loa);
    // Define the fixed participants (Finance)
    // const fixedParticipants = [
    //   {
    //     userId: new Types.ObjectId(loa.finance[0]._id), // Replace with actual CFO user ID
    //     approvalType: '(Payment by)',
    //     role: 'Payment by',
    //     status: 'pending',
    //     assignedAt: null
    //   },
    // ];

    var preparedByDate = new Date();

    // Combine dynamic and fixed participants
    const workflowParticipants = [
      ...createDto.workflowParticipants.map((participant, index) => ({
        userId: new Types.ObjectId(participant.userId),  // Ensure userId is ObjectId
        stepNumber: index + 1,
        approvalType: participant.approvalType,
        role: participant.approvalType,  // Adjust role if needed (e.g., pass it in DTO or handle it differently)
        status: 'pending',
        // Set assignedAt only for the first (current) step
        assignedAt: index === 0 ? preparedByDate : null,  // Only assign the current step
        approvalDate: index == 0 ? preparedByDate : null
      })),
      // Add the fixed participants at the end with no assignedAt initially
      // ...fixedParticipants.map((participant, index) => ({
      //   ...participant,
      //   stepNumber: createDto.workflowParticipants.length + index + 1, // Adjust step numbers accordingly
      //   assignedAt: null, // Will be assigned when the current step reaches them
      // })),
    ];

    console.log(saveFieldRequest)

    // Create the workflow with the participants
    const workflow = new this.fieldRequestWorkflowModel({
      fieldRequestId: saveFieldRequest._id,
      steps: workflowParticipants,
      currentStep: 2,  // Start with the first step
      isCompleted: false,
    });

    await workflow.save();

    // Preparer Email
    // const participantUserId = new Types.ObjectId(createDto.workflowParticipants[0].userId);
    // const preparer = loa.preparers.find((prep: any) => prep._id.equals(participantUserId)) as any;

    // if (preparer) {
    //   const email = preparer.email;
    //   const name = preparer.fullName;

    //   // Save email details to the scheduler
    //   const emailScheduler = new this.emailSchedulerModel({
    //     to: email,
    //     subject: `Field Requisition Created: ${serialNumber}`,
    //     body: `<p>Dear ${name}, <br/>Your Field Requisition - ${serialNumber} has been created succesfully.</b><br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
    //   });
    //   await emailScheduler.save();
    // }

    // First Approver Email
    if (loa && loa.approvalLimits && loa.approvalLimits.length > 0) {
      const firstApprover = loa.approvalLimits[0]?.approvers[0] as any;
      // console.log("First Approver ", firstApprover);
      if (firstApprover) {
        // Save email details to the scheduler
        const emailScheduler = new this.emailSchedulerModel({
          // to: '<EMAIL>',
          to: firstApprover.email,
          subject: `Payment Advice to be signed: ${serialNumber}`,
          body: `<p>Dear ${firstApprover.fullName}, <br/>You have new Field Requisition - ${serialNumber} to be signed.</b><br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
        });
        await emailScheduler.save();
      } else {
        console.log('No approvers found in the first approval limit.');
      }
    } else {
      console.log('No approval limits found.');
    }

    return { data: this.securityService.encrypt(saveFieldRequest) };
  }
  
  // // Create a new payment advice
  // async createPaymentAdvice(encryptedData: any): Promise<any> {
  //   const createDto = this.securityService.decrypt(encryptedData);

  //   const prefix = 'UZMA-EPA';
  //   const serialNumber = await this.serialNumberService.getNextSerialNumber(prefix);

  //   // const paymentAdvice = new this.paymentAdviceModel(createDto);

  //   const paymentAdvice = new this.paymentAdviceModel({
  //     ...createDto,
  //     serialNumber,
  //   });
  //   const savedAdvice = await paymentAdvice.save();

  //   const loa = await this.loaModel
  //     .findOne({ departmentId: new Types.ObjectId(createDto.departmentCode), currency: createDto.currency.toUpperCase(), identifier: createDto.identifier })
  //     .populate({
  //       path: 'preparers',
  //       model: 'User',
  //       select: 'name email fullName designation faceImage'
  //     })  // Populate preparers (userId array)
  //     .populate({
  //       path: 'finance',
  //       model: 'User',
  //       select: 'name email fullName designation faceImage'
  //     })    // Populate finance (userId array)
  //     .populate({
  //       path: 'approvalLimits.approvers',  // Populate approvers inside each approvalLimit
  //       model: 'User',  // Specify the model explicitly if necessary
  //       select: 'name email fullName designation faceImage'  // Select the fields you need from the User model
  //     })
  //     .exec();

  //   // console.log("LOA ", loa);

  //   // Define the fixed participants (Finance)
  //   const fixedParticipants = [
  //     {
  //       userId: new Types.ObjectId(loa.finance[0]._id), // Replace with actual CFO user ID
  //       approvalType: '(Payment by)',
  //       role: 'Payment by',
  //       status: 'pending',
  //       assignedAt: null
  //     },
  //   ];

  //   var preparedByDate = new Date();

  //   // Combine dynamic and fixed participants
  //   const workflowParticipants = [
  //     ...createDto.workflowParticipants.map((participant, index) => ({
  //       userId: new Types.ObjectId(participant.userId),  // Ensure userId is ObjectId
  //       stepNumber: index + 1,
  //       approvalType: participant.approvalType,
  //       role: participant.approvalType,  // Adjust role if needed (e.g., pass it in DTO or handle it differently)
  //       status: 'pending',
  //       // Set assignedAt only for the first (current) step
  //       assignedAt: index === 0 ? preparedByDate : null,  // Only assign the current step
  //       approvalDate: index == 0 ? preparedByDate : null
  //     })),
  //     // Add the fixed participants at the end with no assignedAt initially
  //     ...fixedParticipants.map((participant, index) => ({
  //       ...participant,
  //       stepNumber: createDto.workflowParticipants.length + index + 1, // Adjust step numbers accordingly
  //       assignedAt: null, // Will be assigned when the current step reaches them
  //     })),
  //   ];

  //   // Create the workflow with the participants
  //   const workflow = new this.approvalWorkflowModel({
  //     paymentAdviceId: savedAdvice._id,
  //     steps: workflowParticipants,
  //     currentStep: 2,  // Start with the first step
  //     isCompleted: false,
  //   });

  //   await workflow.save();

  //   // Preparer Email
  //   const participantUserId = new Types.ObjectId(createDto.workflowParticipants[0].userId);
  //   const preparer = loa.preparers.find((prep: any) => prep._id.equals(participantUserId)) as any;

  //   if (preparer) {
  //     const email = preparer.email;
  //     const name = preparer.fullName;
  //     // console.log(`Found preparer with email: ${email}`);

  //     // Save email details to the scheduler
  //     const emailScheduler = new this.emailSchedulerModel({
  //       to: email,
  //       subject: `Payment Advice Created: ${serialNumber}`,
  //       body: `<p>Dear ${name}, <br/>Your payment advice - ${serialNumber} has been created succesfully.<br/><br/>Vendor Name - ${createDto.vendorName}<br/>Amount - <b>${createDto.currency} ${createDto.amount}</b><br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
  //     });
  //     await emailScheduler.save();
  //   }

  //   // First Approver Email
  //   if (loa && loa.approvalLimits && loa.approvalLimits.length > 0) {
  //     const firstApprover = loa.approvalLimits[0]?.approvers[0] as any;
  //     // console.log("First Approver ", firstApprover);
  //     if (firstApprover) {
  //       // Save email details to the scheduler
  //       const emailScheduler = new this.emailSchedulerModel({
  //         // to: '<EMAIL>',
  //         to: firstApprover.email,
  //         subject: `Payment Advice to be signed: ${serialNumber}`,
  //         body: `<p>Dear ${firstApprover.fullName}, <br/>You have new payment advice - ${serialNumber} to be signed.<br/><br/>Vendor Name - ${createDto.vendorName}<br/>Amount - <b>${createDto.currency} ${createDto.amount.toFixed(2)}</b><br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
  //       });
  //       await emailScheduler.save();
  //     } else {
  //       console.log('No approvers found in the first approval limit.');
  //     }
  //   } else {
  //     console.log('No approval limits found.');
  //   }

  //   return { data: this.securityService.encrypt(savedAdvice) };
  // }

  async getFieldRequisitionList(encryptedData: any): Promise<any> {
    const decryptedData = this.securityService.decrypt(encryptedData);

    const { status, departmentId, assignedUserId, page = 1, limit = 10 } = decryptedData;

    const filters: any = {};
    const skip = (page - 1) * limit;

    if (departmentId) {
      filters.departmentCode = departmentId;
    } else if (assignedUserId) {
      const user = await this.userModel.findById(assignedUserId).select('departments').exec();
      if (user && user.departments && user.departments.length > 0) {
        filters.departmentCode = { $in: user.departments };
      }
    }

    let relevantFieldRequestIds: Types.ObjectId[] = [];

    if (status === 'to-sign' && assignedUserId) {
      const workflows = await this.fieldRequestWorkflowModel
        .find({
          isCompleted: false,
          'steps.userId': new Types.ObjectId(assignedUserId),
          'steps.status': 'pending',
          $expr: {
            $eq: [
              '$currentStep',
              {
                $add: [
                  { $indexOfArray: ['$steps.userId', new Types.ObjectId(assignedUserId)] },
                  1,
                ],
              },
            ],
          },
        })
        .select('fieldRequestId')
        .exec();

      relevantFieldRequestIds = workflows.map((workflow) => workflow.fieldRequestId);
      filters._id = { $in: relevantFieldRequestIds };
      filters.status = 'submitted';
    } else if (status !== 'to-sign') {
      filters.status = status;
    }

    const fieldRequests = await this.fieldRequestModel
      .find(filters)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(Number(limit))
      .populate('departmentCode')
      .populate('createdBy')
      .exec();

    const fieldRequestIds = fieldRequests.map((fr) => fr._id);
    const workflows = await this.fieldRequestWorkflowModel
      .find({ fieldRequestId: { $in: fieldRequestIds } })
      .populate({
        path: 'steps.userId',
        select: 'fullName email',
      })
      .exec();

    const finalPaymentAdvices = fieldRequests.map((fr) => {
      const relatedWorkflow = workflows.find((workflow) => workflow.fieldRequestId.toString() === fr._id.toString());
      return {
        ...fr.toObject(),
        workflowSteps: relatedWorkflow ? relatedWorkflow.steps : [],
        currentStep: relatedWorkflow ? relatedWorkflow.currentStep : null,
        isCompleted: relatedWorkflow ? relatedWorkflow.isCompleted : false,
      };
    });

    const count = await this.fieldRequestModel.countDocuments(filters);

    const responseData = {
      data: finalPaymentAdvices,
      count
    };

    return { data: this.securityService.encrypt(responseData) };
  }

  async getFieldRequisitionById(encryptedData: any): Promise<any> {
    const id = this.securityService.decrypt(encryptedData);

    // Validate and convert `id` to ObjectId
    const objectId = Types.ObjectId.isValid(id) ? new Types.ObjectId(id) : null;
    if (!objectId) {
      throw new NotFoundException('Invalid Payment Advice ID');
    }

    // Step 1: Fetch PaymentAdvice by ID with populated department and creator
    const fieldRequisition = await this.fieldRequestModel
      .findById(objectId)
      .populate('departmentCode')
      .populate('createdBy')
      .exec();

    if (!fieldRequisition) {
      throw new NotFoundException('Payment Advice not found');
    }

    // Step 2: Fetch related workflow and populate workflow steps with user details
    const workflow = await this.fieldRequestWorkflowModel
      .findOne({ fieldRequestId: fieldRequisition._id })
      .populate({
        path: 'steps.userId',
        select: 'fullName email'
      })
      .exec();

    // Step 3: Format the result to include workflow details within the response
    var response = {
      ...fieldRequisition.toObject(),
      workflowSteps: workflow ? workflow.steps : [],
      currentStep: workflow ? workflow.currentStep : null,
      isCompleted: workflow ? workflow.isCompleted : false,
    };

    return { data: this.securityService.encrypt(response) };
  }

  // Approve or reject a payment advice workflow step
  async approveRejectFieldRequisition(dto: FieldRequestWorkflowDto): Promise<FieldRequestWorkflow> {
    const workflow = await this.fieldRequestWorkflowModel
      .findOne({ fieldRequestId: new Types.ObjectId(dto.fieldRequestId) })
      .populate({
        path: 'steps.userId',
        select: '_id fullName email'
      })
      .populate({
        path: 'fieldRequestId',
        select: '_id frType projectCode remark'
      })
      .exec();

    // console.log("workflow ", workflow);
    // return workflow;

    if (!workflow) throw new NotFoundException('Workflow not found for this payment advice');

    const preparerStep = workflow.steps[0];
    const currentStep = workflow.steps[workflow.currentStep - 1];
    const nextStep = workflow.steps[workflow.currentStep];

    // console.log("created by ", preparerStep.userId['email']);
    // console.log("signed by ", currentStep.userId['email']);
    // console.log("next person signed by ", nextStep.userId['email']);

    if (currentStep.userId._id.toString() !== dto.userId) {
      throw new NotFoundException('You are not assigned to this approval step');
    }

    currentStep.status = dto.status;
    currentStep.approvalDate = new Date();
    currentStep.comments = dto.comments;

    if (dto.status === 'rejected') {
      // Halt the workflow and set it as rejected
      workflow.isCompleted = true;
      workflow.completedAt = new Date();

      // Update the payment advice status to "rejected" in the pa_main collection
      await this.fieldRequestModel.updateOne(
        { _id: workflow.fieldRequestId },
        { $set: { status: 'rejected' } }
      );

      // Sent to Creater
      const emailScheduler1 = new this.emailSchedulerModel({
        to: preparerStep.userId['email'],
        // to: '<EMAIL>',
        subject: `MMSVS Field Requsition Rejected: ${workflow.fieldRequestId['serialNumber']}`,
        body: `<p>Dear ${preparerStep.userId['fullName']}, <br/>Your field requisition - ${workflow.fieldRequestId['serialNumber']} has been rejected by ${currentStep.userId['fullName']}.<br/><br/>FR Type - ${workflow.fieldRequestId['frType']}<br/>Project Code - <b>${workflow.fieldRequestId['projectCode']} </b><br/>Note - ${currentStep.comments}<br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
      });
      await emailScheduler1.save();
    } else {
      // If approved, proceed to the next step
      if (nextStep) {
        nextStep.assignedAt = new Date();

        // Sent to Next Person
        const emailScheduler2 = new this.emailSchedulerModel({
          to: nextStep.userId['email'],
          // to: '<EMAIL>',
          subject: `MMSVS Field Requisition to be signed: ${workflow.fieldRequestId['serialNumber']}`,
          body: `<p>Dear ${nextStep.userId['fullName']}, <br/>You have new MMSVS Field Requisition - ${workflow.fieldRequestId['serialNumber']} to be signed.<br/><br/>FR Type - ${workflow.fieldRequestId['frType']}<br/>Project Code - <b>${workflow.fieldRequestId['projectCode']}</b><br/>Note - ${workflow.fieldRequestId['remark']}<br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
        });
        await emailScheduler2.save();

        // Sent to Creater
        const emailScheduler1 = new this.emailSchedulerModel({
          to: preparerStep.userId['email'],
          // to: '<EMAIL>',
          subject: `MMSVS Field Requisition Signed: ${workflow.fieldRequestId['serialNumber']}`,
          body: `<p>Dear ${preparerStep.userId['fullName']}, <br/>Your field requisition - ${workflow.fieldRequestId['serialNumber']} has been signed by ${currentStep.userId['fullName']}.<br/><br/>FR Type - ${workflow.fieldRequestId['frType']}<br/>Project Code - <b>${workflow.fieldRequestId['projectCode']}</b><br/>Note - ${workflow.fieldRequestId['remark']}<br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
        });
        await emailScheduler1.save();
      } else {
        // Sent to Creater
        // const emailScheduler1 = new this.emailSchedulerModel({
        //   to: preparerStep.userId['email'],
        //   // to: '<EMAIL>',
        //   subject: `Payment Made for PA: ${workflow.paymentAdviceId['serialNumber']}`,
        //   body: `<p>Dear ${preparerStep.userId['fullName']}, <br/>Payment has been made for PA - ${workflow.paymentAdviceId['serialNumber']} by finance department.<br/><br/>Vendor Name - ${workflow.paymentAdviceId['vendorName']}<br/>Amount - <b>${workflow.paymentAdviceId['currency']} ${workflow.paymentAdviceId['amount']}</b><br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
        // });
        // await emailScheduler1.save();
      }

      // Update the workflow step count and check completion
      workflow.currentStep += 1;
      // if (workflow.currentStep == workflow.steps.length) {
      //   // Update the payment advice status to "approved" in the pa_main collection
      //   await this.fieldRequestModel.updateOne(
      //     { _id: workflow.fieldRequestId },
      //     { $set: { status: 'approved' } }
      //   );
      // } else
       if (workflow.currentStep > workflow.steps.length) {
        workflow.isCompleted = true;
        workflow.completedAt = new Date();
        // Update the payment advice status to "approved" in the pa_main collection
        await this.fieldRequestModel.updateOne(
          { _id: workflow.fieldRequestId },
          { $set: { status: 'approved' } }
        );
      }
    }
    await workflow.save();
    return workflow;
  }

  // async updateBankReceipts(dto: UpdateBankReceiptsDto): Promise<PaymentAdvice> {
  //   const paymentAdvice = await this.paymentAdviceModel.findById(dto.paymentAdviceId);

  //   if (!paymentAdvice) {
  //     throw new NotFoundException('Payment Advice not found');
  //   }

  //   const totalPAAmount = paymentAdvice.amount; // Total amount in Payment Advice

  //   // Merge existing receipts with new ones
  //   const existingReceipts = paymentAdvice.bankReceipts || [];
  //   const newReceipts = dto.bankReceipts.map((receipt) => ({
  //     ...receipt,
  //     createdAt: new Date(), // Set createdAt if not present (new receipt)
  //     updatedAt: new Date(), // Update timestamp for all receipts
  //   }));

  //   const allReceipts = [...existingReceipts, ...newReceipts];

  //   // Calculate cumulative totals and update flags
  //   let totalReceiptAmount = 0;

  //   const updatedBankReceipts = allReceipts.map((receipt) => {
  //     totalReceiptAmount += Number(receipt.amount);

  //     const isPartial = totalReceiptAmount < totalPAAmount;
  //     const balance = isPartial ? totalPAAmount - totalReceiptAmount : 0;
  //     // console.log(" isPartial ", isPartial);
  //     // console.log(" balance ", balance);

  //     return {
  //       ...receipt,
  //       isPartial,
  //       balance,
  //       updatedAt: new Date(), // Update timestamp for every receipt
  //     };
  //   });

  //   // console.log(" totalPAAmount ", totalPAAmount);
  //   // console.log(" totalReceiptAmount ", totalReceiptAmount);

  //   // Save updated receipts and update PaymentAdvice
  //   paymentAdvice.bankReceipts = updatedBankReceipts;
  //   paymentAdvice.isPartialPayment = totalReceiptAmount < totalPAAmount;
  //   paymentAdvice.partialPaymentBalance = totalReceiptAmount < totalPAAmount
  //     ? totalPAAmount - totalReceiptAmount
  //     : 0;

  //   await paymentAdvice.save();

  //   return paymentAdvice;
  // }

  // async findAllWithoutSerialNumber() {
  //   return this.paymentAdviceModel.find({ serialNumber: { $exists: false } }).exec();
  // }

  // // Generate a new serial number using the SerialNumberService
  // async generateSerialNumber(prefix: string): Promise<string> {
  //   return this.serialNumberService.getNextSerialNumber(prefix);
  // }

  // async assignSerialNumbers() {
  //   const prefix = 'UZMA-EPA';
  //   const paymentAdvicesWithoutSerial = this.findAllWithoutSerialNumber();

  //   for (const advice of await paymentAdvicesWithoutSerial) {
  //     const serialNumber = await this.generateSerialNumber(prefix);
  //     advice.serialNumber = serialNumber;
  //     await advice.save();
  //   }
  // }
}
