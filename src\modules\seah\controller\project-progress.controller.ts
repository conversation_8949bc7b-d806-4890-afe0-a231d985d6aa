// src/project-progress/project-progress.controller.ts
import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { CreateProjectProgressDto, UpdateProjectProgressDto } from '../dto/project-progress.dto';
import { ProjectProgressService } from '../service/project-progress.service';

@Controller('project-progress')
export class ProjectProgressController {
  constructor(private readonly projectProgressService: ProjectProgressService) {}

  @Post()
  create(@Body() createProjectProgressDto: CreateProjectProgressDto) {
    return this.projectProgressService.createOrUpdate(createProjectProgressDto);
  }

  @Get()
  findAll() {
    return this.projectProgressService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.projectProgressService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateProjectProgressDto: UpdateProjectProgressDto) {
    return this.projectProgressService.update(id, updateProjectProgressDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.projectProgressService.remove(id);
  }
}
