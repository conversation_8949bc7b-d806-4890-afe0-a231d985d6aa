import { IsNotEmpty, IsString, IsArray, IsMongoId, IsOptional } from 'class-validator';
import { Types } from 'mongoose';

export class ApprovalLimitDto {
  @IsNotEmpty()
  @IsString()
  minLimit: string;  // E.g., 1500, >=20001

  @IsNotEmpty()
  @IsString()
  maxLimit: string;  // E.g., 1500, >=20001

  @IsArray()
  @IsNotEmpty({ each: true })
  approvers: Types.ObjectId[];  // Array of approver user IDs
}

export class CreateLoaDto {
  @IsNotEmpty()
  @IsMongoId()
  departmentId: Types.ObjectId;  // Department ID reference

  // @IsNotEmpty()
  // @IsString()
  // loaType: string;  // E.g., FR, PR, PA

  @IsNotEmpty()
  @IsString()
  type: string;  // E.g., 

  @IsNotEmpty()
  @IsString()
  identifier: string;  // E.g., 

  @IsOptional()
  @IsString()
  loaType: string;

  @IsNotEmpty()
  @IsString()
  currency: string;  // E.g., USD, MYR

  @IsArray()
  @IsNotEmpty({ each: true })
  preparers: Types.ObjectId[];  // Array of preparer user IDs

  @IsArray()
  @IsNotEmpty({ each: true })
  finance: Types.ObjectId[];  // Array of finance user IDs

  @IsArray()
  @IsNotEmpty({ each: true })
  approvalLimits: ApprovalLimitDto[];  // Approval limits with thresholds and approvers
}
