stages:
  - test
  - build
  - deploy
  - verify

variables:
  AWS_DEFAULT_REGION: ap-southeast-1
  ECR_REGISTRY: ${AWS_ACCOUNT_ID}.dkr.ecr.ap-southeast-1.amazonaws.com
  ECR_REPOSITORY: uzmacloud-api
  ECS_CLUSTER: prod-cluster
  ECS_SERVICE: prod-api-service
  HEALTH_CHECK_URL: https://uzmacloud.com/health
  NODE_VERSION: "18"

# Test stage - Always visible
test:
  image: node:${NODE_VERSION}-alpine
  stage: test
  cache:
    key: ${CI_COMMIT_REF_SLUG}-node
    paths:
      - node_modules/
  before_script:
    - apk add --no-cache python3 make g++
    - npm ci --legacy-peer-deps && npm cache clean --force
  script:
    - echo "📋 Running code quality checks..."
    - npm run lint || echo "⚠️ Linting issues found"
    - echo "🧪 Running tests..."
    - npm run test || echo "⚠️ Tests not configured yet"
    - echo "🏗️ Testing build..."
    - npm run build
    - echo "✅ Build successful"
  artifacts:
    paths:
      - dist/
    expire_in: 1 hour
  # Remove complex rules - make it always visible
  except:
    - never

# Build stage - Visible on main branch
build:
  image: docker:24-dind
  stage: build
  services:
    - docker:24-dind
  before_script:
    - apk add --no-cache aws-cli python3 py3-pip curl
    - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
  script:
    - echo "🐳 Building Docker image..."
    - docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$CI_COMMIT_SHA .
    - docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:latest .
    
    - echo "🧪 Testing container startup..."
    - |
      # Test that container builds and starts successfully
      docker run -d --name test-api \
        -e NODE_ENV=production \
        -e PORT=3000 \
        $ECR_REGISTRY/$ECR_REPOSITORY:$CI_COMMIT_SHA
      
      # Give container time to start
      sleep 15
      
      # Check if container is running
      if docker ps | grep test-api > /dev/null; then
        echo "✅ Container started successfully"
        
        # Show logs to verify NestJS starts
        echo "=== Container startup logs ==="
        docker logs test-api
        
        echo "✅ Docker image test passed"
      else
        echo "❌ Container failed to start"
        docker logs test-api
        exit 1
      fi
      
      # Cleanup
      docker stop test-api && docker rm test-api
    
    - echo "📦 Pushing to ECR..."
    - docker push $ECR_REGISTRY/$ECR_REPOSITORY:$CI_COMMIT_SHA
    - docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
    - echo "✅ Build completed"
  only:
    - main

# Deploy stage - Manual on main branch
deploy:
  image: alpine:latest
  stage: deploy
  before_script:
    - apk add --no-cache python3 py3-pip curl jq aws-cli
  script:
    - echo "🚀 Starting zero-downtime deployment..."

    - echo "🔐 Getting database connection from AWS SSM..."
    - |
      # Get database password from SSM Parameter Store
      DB_PASSWORD=$(aws ssm get-parameters \
        --names "/prod/docdb/master_password" \
        --with-decryption | jq -r '.Parameters[0].Value')
      
      if [ -z "$DB_PASSWORD" ]; then
        echo "❌ Could not retrieve database password from SSM"
        exit 1
      fi
      echo "✅ Retrieved database password from SSM"

      # Use GitLab variables for other database config
      DB_USERNAME="${DB_USERNAME:-admin}"  # Default to 'admin' if not set
      DB_ENDPOINT="${DB_ENDPOINT}"
      DB_PORT="${DB_PORT}"         # Default to 27017 if not set
      DB_NAME="${DB_NAME}"     # Default to 'uzmacloud' if not set
      AWS_ACCESS_KEY_ID="${AWS_ACCESS_KEY_ID}"
      AWS_SECRET_ACCESS_KEY="${AWS_SECRET_ACCESS_KEY}"
      AWS_DEFAULT_REGION="${AWS_DEFAULT_REGION}"
      AWS_S3_BUCKET="${AWS_S3_BUCKET}"
      AWS_S3_SEAH="${AWS_S3_SEAH}"
      ENCRYPTION_SECRET="${ENCRYPTION_SECRET}"
      REKOGNITION_COLLECTION_ID="${REKOGNITION_COLLECTION_ID}"
      
      # Construct secure connection string
      DATABASE_URL="mongodb://${DB_USERNAME}:${DB_PASSWORD}@${DB_ENDPOINT}:${DB_PORT}/${DB_NAME}?ssl=true&tlsAllowInvalidCertificates=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
      
      echo "✅ Database connection string constructed from SSM + GitLab variables"
      echo "   Username: ${DB_USERNAME}"
      echo "   Endpoint: ${DB_ENDPOINT}"
      echo "   Port: ${DB_PORT}"
      echo "   Database: ${DB_NAME}"
    
    - echo "📝 Creating new task definition..."
    - |
      if aws ecs describe-task-definition --task-definition uzmacloud-prod-api >/dev/null 2>&1; then
        echo "Updating existing task definition..."
        TASK_DEF_JSON=$(aws ecs describe-task-definition \
          --task-definition uzmacloud-prod-api \
          --query 'taskDefinition')
        
        echo "$TASK_DEF_JSON" | jq \
          --arg family "uzmacloud-prod-api" \
          --arg IMAGE "$ECR_REGISTRY/$ECR_REPOSITORY:$CI_COMMIT_SHA" \
          --arg DATABASE_URL "$DATABASE_URL" \
          --arg AWS_ACCESS_KEY_ID "$AWS_ACCESS_KEY_ID" \
          --arg AWS_SECRET_ACCESS_KEY "$AWS_SECRET_ACCESS_KEY" \
          --arg AWS_DEFAULT_REGION "$AWS_DEFAULT_REGION" \
          --arg AWS_S3_BUCKET "$AWS_S3_BUCKET" \
          --arg AWS_S3_SEAH "$AWS_S3_SEAH" \
          --arg ENCRYPTION_SECRET "$ENCRYPTION_SECRET" \
          --arg REKOGNITION_COLLECTION_ID "$REKOGNITION_COLLECTION_ID" \
          '
            .family = $family |
            .containerDefinitions[0].image = $IMAGE |
            .containerDefinitions[0].environment = [
              {"name": "NODE_ENV", "value": "production"},
              {"name": "PORT", "value": "3000"},
              {"name": "DATABASE_URL", "value": $DATABASE_URL},
              {"name": "AWS_ACCESS_KEY_ID", "value": $AWS_ACCESS_KEY_ID},
              {"name": "AWS_SECRET_ACCESS_KEY", "value": $AWS_SECRET_ACCESS_KEY},
              {"name": "AWS_DEFAULT_REGION", "value": $AWS_DEFAULT_REGION},
              {"name": "AWS_S3_BUCKET", "value": $AWS_S3_BUCKET},
              {"name": "AWS_S3_SEAH", "value": $AWS_S3_SEAH},
              {"name": "ENCRYPTION_SECRET", "value": $ENCRYPTION_SECRET},
              {"name": "REKOGNITION_COLLECTION_ID", "value": $REKOGNITION_COLLECTION_ID}
            ] |
            del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .placementConstraints, .compatibilities, .registeredAt, .registeredBy)
          ' > task-definition.json

      else
        echo "Creating first task definition..."
        
        # Create JSON using jq and save to file
        jq -n \
          --arg family "uzmacloud-prod-api" \
          --arg image "$ECR_REGISTRY/$ECR_REPOSITORY:$CI_COMMIT_SHA" \
          --arg executionRole "arn:aws:iam::$AWS_ACCOUNT_ID:role/ecsTaskExecutionRole" \
          --arg DATABASE_URL "$DATABASE_URL" \
          --arg AWS_ACCESS_KEY_ID "$AWS_ACCESS_KEY_ID" \
          --arg AWS_SECRET_ACCESS_KEY "$AWS_SECRET_ACCESS_KEY" \
          --arg AWS_DEFAULT_REGION "$AWS_DEFAULT_REGION" \
          --arg AWS_S3_BUCKET "$AWS_S3_BUCKET" \
          --arg AWS_S3_SEAH "$AWS_S3_SEAH" \
          --arg ENCRYPTION_SECRET "$ENCRYPTION_SECRET" \
          --arg REKOGNITION_COLLECTION_ID "$REKOGNITION_COLLECTION_ID" \
          '{
            family: $family,
            networkMode: "awsvpc",
            requiresCompatibilities: ["FARGATE"],
            cpu: "512",
            memory: "1024",
            executionRoleArn: $executionRole,
            containerDefinitions: [
              {
                name: "api",
                image: $image,
                portMappings: [
                  {
                    containerPort: 3000,
                    protocol: "tcp"
                  }
                ],
                environment: [
                  {
                    name: "NODE_ENV",
                    value: "production"
                  },
                  {
                    name: "PORT",
                    value: "3000"
                  },
                  {
                    name: "DATABASE_URL",
                    value: "$DATABASE_URL"
                  },
                  {
                    name: "AWS_ACCESS_KEY_ID",
                    value: "$AWS_ACCESS_KEY_ID"
                  },
                  {
                    name: "AWS_SECRET_ACCESS_KEY",
                    value: "$AWS_SECRET_ACCESS_KEY"
                  },
                  {
                    name: "AWS_DEFAULT_REGION",
                    value: "$AWS_DEFAULT_REGION"
                  },
                  {
                    name: "AWS_S3_BUCKET",
                    value: "$AWS_S3_BUCKET"
                  },
                  {
                    name: "AWS_S3_SEAH",
                    value: "$AWS_S3_SEAH"
                  },
                  {
                    name: "ENCRYPTION_SECRET",
                    value: "$ENCRYPTION_SECRET"
                  },
                  {
                    name: "REKOGNITION_COLLECTION_ID",
                    value: "$REKOGNITION_COLLECTION_ID"
                  }
                ],
                healthCheck: {
                  command: [
                    "CMD-SHELL",
                    "curl -f http://localhost:3000/health || exit 1"
                  ],
                  interval: 30,
                  timeout: 10,
                  retries: 3,
                  startPeriod: 120
                },
                logConfiguration: {
                  logDriver: "awslogs",
                  options: {
                    "awslogs-group": "/aws/ecs/prod-api",
                    "awslogs-region": $AWS_DEFAULT_REGION,
                    "awslogs-stream-prefix": "ecs"
                  }
                },
                essential: true,
                stopTimeout: 30
              }
            ]
          }' > task-definition.json
      fi
      
      # Validate JSON file
      echo "Validating JSON file..."
      if jq empty task-definition.json; then
        echo "✅ JSON is valid"
      else
        echo "❌ JSON is invalid"
        cat task-definition.json
        exit 1
      fi
      
      # Debug: Show the JSON file content
      echo "=== Task Definition JSON File ==="
      cat task-definition.json | jq .
      echo "================================="
      
      # Register new task definition using file
      NEW_REVISION=$(aws ecs register-task-definition \
        --cli-input-json file://task-definition.json \
        --query 'taskDefinition.revision' \
        --output text)
      
      echo "✅ Task definition registered: uzmacloud-prod-api:$NEW_REVISION"
    
    - echo "🔄 Zero-downtime rolling deployment..."
    - |
      aws ecs update-service \
        --cluster $ECS_CLUSTER \
        --service $ECS_SERVICE \
        --task-definition uzmacloud-prod-api:$NEW_REVISION \
        --force-new-deployment
    
    - echo "⏳ Monitoring deployment..."
    - |
      for i in {1..20}; do
        STATUS=$(aws ecs describe-services \
          --cluster $ECS_CLUSTER \
          --services $ECS_SERVICE \
          --query 'services[0].deployments[?status==`PRIMARY`].rolloutState' \
          --output text)
        
        echo "⏱️ Status: $STATUS"
        
        if [ "$STATUS" = "COMPLETED" ]; then
          echo "✅ Deployment completed!"
          break
        elif [ "$STATUS" = "FAILED" ]; then
          echo "❌ Deployment failed!"
          exit 1
        fi
        
        sleep 30
      done
    
    - echo "✅ Deployment successful!"
  
  only:
    - main
  when: manual
  environment:
    name: production
    url: $HEALTH_CHECK_URL

# Verify stage - Runs after deploy
verify:
  image: alpine:latest
  stage: verify
  before_script:
    - apk add --no-cache curl jq
  script:
    - echo "🔍 Verifying deployment..."
    
    - echo "💚 Testing health endpoints..."
    - |
      for i in {1..10}; do
        if curl -f -s $HEALTH_CHECK_URL > /dev/null; then
          echo "✅ Health check passed"
          break
        else
          echo "⏳ Attempt $i failed, retrying..."
          sleep 10
          if [ $i -eq 10 ]; then
            echo "❌ Health check failed"
            exit 1
          fi
        fi
      done
    
    - echo "🔗 Testing API endpoints..."
    - curl -f -s https://uzmacloud.com/api/v1/tests/profile | jq . || echo "⚠️ API endpoint test failed"
    - curl -f -s https://uzmacloud.com/health/detailed | jq . || echo "⚠️ Detailed health test failed"
    
    - echo "🎉 Verification completed!"
  
  only:
    - main