import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true, collection: 'company' })
export class Company extends Document {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  logo: string;

  @Prop({ required: true })
  address: string;

  @Prop({ required: true })
  tel: string;

  @Prop({ required: true })
  fax: string;

  @Prop({ required: true })
  isActive: boolean;
}
export const CompanySchema = SchemaFactory.createForClass(Company);
