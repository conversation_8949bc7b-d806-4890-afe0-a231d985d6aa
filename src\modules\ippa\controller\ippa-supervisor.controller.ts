// ippa-supervisor.controller.ts
import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/modules/auth/guard/jwt.guard';
import { IppaSupervisorService } from '../service/ippa-supervisor.service';
import { IppaWorkflowService } from '../service/ippa-workflow.service';
import { SecurityService } from 'src/modules/user/service/security.service';

@ApiTags('IPPA Supervisor')
@Controller('ippa/supervisor')
// @UseGuards(JwtAuthGuard)
export class IppaSupervisorController {
  constructor(
    private readonly supervisorService: IppaSupervisorService,
    private readonly workFlowService: IppaWorkflowService,
    private readonly securityService: SecurityService
  ) {}

  /**
   * Get submissions requiring approval
   */
  @Get('to-approve')
  @ApiOperation({ summary: 'Get submissions requiring approval' })
  async getToApproveSubmissions(
    @Request() req,
    @Query('email') email: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('status') status?: string,
    @Query('financialYear') financialYear?: string,
    @Query('search') search?: string,
  ) {
    try {
    //   const approverEmail = req.user.email;
      const filters: any = { status, financialYear, search };

      const result = await this.supervisorService.getToApproveSubmissions(
        email,
        page,
        limit,
        filters,
      );

      return {
        success: true,
        data: result,
        message: 'Submissions retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }

  /**
   * Get submission details for review
   */
  @Get('submission/:id/review')
  @ApiOperation({ summary: 'Get submission details for review' })
  async getSubmissionForReview(
    @Param('id') submissionId: string,
    @Request() req,
  ) {
    try {
      const reviewerEmail = req.user.email;

      const submission = await this.supervisorService.getSubmissionForReview(
        submissionId,
        reviewerEmail,
      );

      return {
        success: true,
        data: submission,
        message: 'Submission details retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }

  /**
   * Supervisor approves submission
   */
  @Post('approve')
  @ApiOperation({ summary: 'Supervisor approves submission with ratings' })
  async supervisorApprove(
    @Body() encryptedData: any
    // approvalData: {
    //   submissionId: string;
    //   supervisorEmail: string;
    //   objectives: Array<{
    //     index: number;
    //     supervisorRating: number;
    //     supervisorComments: string;
    //     supervisorWeightedPoints: number;
    //   }>;
    //   supervisorRemarks: string;
    //   supervisorAttachments?: Array<{
    //     fileName: string;
    //     filePath: string;
    //     fileSize: number;
    //     mimeType: string;
    //     description?: string;
    //   }>;
    // },
 
  ) {
    try {
      // Ensure the requester email matches the approval data
      // approvalData.supervisorEmail = req.user.email;

      const result = await this.supervisorService.supervisorApprove(encryptedData);

      const responseData =  {
        success: true,
        data: {
          submissionId: result._id,
          status: result.status,
          approvalWorkflow: result.approvalWorkflow,
          nextReviewer: result.currentReviewerId,
        },
        message: 'IPPA approved by supervisor successfully',
      };

      return { data: this.securityService.encrypt(responseData) };

    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }

  /**
   * HOD approves submission
   */
  @Post('hod-approve')
  @ApiOperation({ summary: 'HOD approves submission (optional ratings)' })
  async hodApprove(
    @Body()
    approvalData: {
      submissionId: string;
      hodEmail: string;
      objectives?: Array<{
        index: number;
        hodRating?: number;
        hodComments?: string;
        hodWeightedPoints?: number;
      }>;
      hodRemarks?: string;
      hodAttachments?: Array<{
        fileName: string;
        filePath: string;
        fileSize: number;
        mimeType: string;
        description?: string;
      }>;
    },
    @Request() req,
  ) {
    try {
      approvalData.hodEmail = req.user.email;

      const result = await this.supervisorService.hodApprove(
        approvalData as any,
      );

      return {
        success: true,
        data: {
          submissionId: result._id,
          status: result.status,
          approvalWorkflow: result.approvalWorkflow,
          nextReviewer: result.currentReviewerId,
        },
        message: 'IPPA approved by HOD successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }

  /**
   * HR completes submission
   */
  @Post('hr-complete')
  @ApiOperation({ summary: 'HR completes the IPPA submission' })
  async hrComplete(
    @Body()
    approvalData: {
      submissionId: string;
      hrEmail: string;
      hrRemarks?: string;
      hrAttachments?: Array<{
        fileName: string;
        filePath: string;
        fileSize: number;
        mimeType: string;
        description?: string;
      }>;
      isCompleted: boolean;
    },
    @Request() req,
  ) {
    try {
      approvalData.hrEmail = req.user.email;

      const result = await this.supervisorService.hrComplete(
        approvalData as any,
      );

      return {
        success: true,
        data: {
          submissionId: result._id,
          status: result.status,
          completedAt: result.completedAt,
          approvalWorkflow: result.approvalWorkflow,
        },
        message: 'IPPA completed by HR successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }

  /**
   * Get approval dashboard
   */
  @Get('dashboard')
  @ApiOperation({ summary: 'Get approval dashboard for supervisor/HOD/HR' })
  async getApprovalDashboard(@Request() req) {
    try {
      const approverEmail = req.user.email;

      // Get pending approvals summary
      const pendingApprovals =
        await this.supervisorService.getToApproveSubmissions(
          approverEmail,
          1,
          50, // Get more for dashboard
        );

      const dashboardData = {
        pendingCount: pendingApprovals.totalCount,
        overdueCoun: pendingApprovals.submissions.filter((s) => s.isOverdue)
          .length,
        approverInfo: pendingApprovals.approverInfo,
        recentSubmissions: pendingApprovals.submissions.slice(0, 5),
        statistics: {
          thisMonth: {
            approved: 0, // Calculate from submissions
            pending: pendingApprovals.totalCount,
            completed: 0,
          },
          thisYear: {
            totalReviewed: 0,
            averageRating: 0,
            averageApprovalTime: 0,
          },
        },
      };

      return {
        success: true,
        data: dashboardData,
        message: 'Dashboard data retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }

  /**
   * Get pending approvals count
   */
  @Get('pending-count')
  @ApiOperation({ summary: 'Get pending approvals count for user' })
  async getPendingApprovalsCount(@Request() req) {
    try {
      const approverEmail = req.user.email;

      const result = await this.supervisorService.getToApproveSubmissions(
        approverEmail,
        1,
        1, // Just get count
      );

      return {
        success: true,
        data: {
          totalPending: result.totalCount,
          overdue: result.submissions.filter((s) => s.isOverdue).length,
        },
        message: 'Pending count retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }

  /**
   * Send approval reminder
   */
  @Post('send-reminder')
  @ApiOperation({ summary: 'Send reminder to pending approver' })
  async sendApprovalReminder(
    @Body() body: { submissionId: string },
    @Request() req,
  ) {
    try {
      // Implementation for sending email reminders
      // This would integrate with your email service

      return {
        success: true,
        data: null,
        message: 'Reminder sent successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }

  /**
   * Get approval statistics
   */
  @Get('statistics')
  @ApiOperation({ summary: 'Get approval statistics for reporting' })
  async getApprovalStatistics(
    @Request() req,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('department') department?: string,
    @Query('financialYear') financialYear?: string,
  ) {
    try {
      // Implementation for getting approval statistics
      // This would aggregate data from IPPA submissions

      const statistics = {
        totalReviewed: 0,
        averageApprovalTime: 0,
        averageRating: 0,
        statusBreakdown: {
          pending: 0,
          approved: 0,
          completed: 0,
        },
        departmentBreakdown: {},
        monthlyTrend: [],
      };

      return {
        success: true,
        data: statistics,
        message: 'Statistics retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }

  /**
   * Upload attachment for review
   */
  @Post('upload-attachment')
  @ApiOperation({ summary: 'Upload attachment for review' })
  async uploadReviewAttachment(
    @Body() formData: any, // This would be handled by multer middleware
    @Request() req,
  ) {
    try {
      // Implementation for file upload
      // This would use your existing file upload service

      return {
        success: true,
        data: {
          fileName: 'uploaded-file.pdf',
          filePath: '/uploads/review/uploaded-file.pdf',
          fileSize: 1024000,
          mimeType: 'application/pdf',
        },
        message: 'File uploaded successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }

  /**
   * Get all departments with HOD configuration
   */
  @Get('list-with-hod-config')
  @ApiOperation({ summary: 'Get all departments with HOD configuration' })
  async getDepartmentsWithHODConfig(@Request() req) {
    try {
      // Implementation to get all departments with their HOD configurations
      const departments =
        await this.workFlowService.getAllDepartmentsWithHODConfig();

      return {
        success: true,
        data: departments,
        message: 'Departments configuration retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }
}

// User role controller for IPPA
@ApiTags('IPPA User Role')
@Controller('ippa/user')
// @UseGuards(JwtAuthGuard)
export class IppaUserRoleController {
  constructor(private readonly supervisorService: IppaSupervisorService,
      private readonly securityService: SecurityService
  ) {}

  /**
   * Get user's role and permissions for IPPA
   */
  @Post('role')
  @ApiOperation({ summary: 'Get user IPPA role and permissions' })
  async getUserIPPARole(@Body() encryptedData: any) {
    try {
      // const userEmail = req.user.email;
      // const userEmail = email;

      // Implementation to determine user role in IPPA system
      const userRole = await this.supervisorService.getUserIPPARole(encryptedData);

       const responseData = {
        success: true,
        data: {
          email: userRole.email,
          name: userRole.fullName,
          department: userRole.department,
          roles: userRole.roles, // ['employee', 'supervisor', 'hod', 'hr']
          permissions: userRole.permissions,
          isLineManager: userRole.isLineManager,
          isHOD: userRole.isHOD,
          isHR: userRole.isHR,
          managedDepartments: userRole.managedDepartments,
          reportees: userRole.reportees,
        },
        message: 'User role retrieved successfully',
      };

      return { data: this.securityService.encrypt(responseData) };

    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }
}

// Workflow history controller
@ApiTags('IPPA Workflow')
@Controller('ippa/workflow')
@UseGuards(JwtAuthGuard)
export class IppaWorkflowController {
  constructor(private readonly workflowService: IppaWorkflowService) {}

  /**
   * Get workflow history for submission
   */
  @Get(':submissionId/history')
  @ApiOperation({ summary: 'Get workflow history for submission' })
  async getWorkflowHistory(
    @Param('submissionId') submissionId: string,
    @Request() req,
  ) {
    try {
      const workflowHistory =
        await this.workflowService.getWorkflowHistory(submissionId);

      return {
        success: true,
        data: {
          submissionId,
          workflow: workflowHistory.steps,
          currentStep: workflowHistory.currentStep,
          statusInfo: workflowHistory.statusInfo,
          timeline: workflowHistory.timeline,
          estimatedCompletion: workflowHistory.estimatedCompletion,
        },
        message: 'Workflow history retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }

  /**
   * Get workflow template for department
   */
  @Get('template/:departmentCode')
  @ApiOperation({ summary: 'Get workflow template for department' })
  async getWorkflowTemplate(@Param('departmentCode') departmentCode: string) {
    try {
      const template =
        await this.workflowService.getWorkflowTemplate(departmentCode);

      return {
        success: true,
        data: template,
        message: 'Workflow template retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }
}

// Department configuration controller
@ApiTags('IPPA Department Config')
@Controller('ippa/department')
@UseGuards(JwtAuthGuard)
export class IppaDepartmentController {
  constructor(private readonly workflowService: IppaWorkflowService) {}

  /**
   * Get department HOD configuration
   */
  @Get(':departmentCode/hod-config')
  @ApiOperation({ summary: 'Get department HOD configuration' })
  async getDepartmentHODConfig(
    @Param('departmentCode') departmentCode: string,
  ) {
    try {
      // Implementation to get department configuration
      const department =
        await this.workflowService.getDepartmentConfig(departmentCode);

      return {
        success: true,
        data: {
          departmentCode: department.code,
          description: department.description,
          division: department.division,
          enableHodApproval: department.enableHodApproval,
          hodPersonId: department.hodPersonId,
          hodWorkflowOrder: department.hodWorkflowOrder,
        },
        message: 'Department configuration retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }

  /**
   * Update department HOD configuration
   */
  @Put(':departmentCode/hod-config')
  @ApiOperation({ summary: 'Update department HOD configuration' })
  async updateDepartmentHODConfig(
    @Param('departmentCode') departmentCode: string,
    @Body()
    config: {
      enableHodApproval: boolean;
      hodPersonId?: string;
      hodWorkflowOrder?: number;
    },
    @Request() req,
  ) {
    try {
      // Check if user has admin privileges
      // Implementation for updating department configuration

      return {
        success: true,
        data: {
          departmentCode,
          updatedConfig: config,
          updatedBy: req.user.email,
          updatedAt: new Date(),
        },
        message: 'Department configuration updated successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        data: null,
      };
    }
  }
}
