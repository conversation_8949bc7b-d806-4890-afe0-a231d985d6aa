import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreatePermissionDTO } from '../dto/create-permission.dto';
import { Permission, PermissionDocument } from '../schema/permission.schema';

@Injectable()
export class PermissionService {
  constructor(@InjectModel(Permission.name) private permissionModel: Model<PermissionDocument>) { }

  async create(createPermissionDto: CreatePermissionDTO): Promise<Permission> {
    const permission = new this.permissionModel(createPermissionDto);
    return await permission.save();
  }

  async createPermission(createPermissionDto: CreatePermissionDTO): Promise<Permission> {
    // Check if permission with same action and resource already exists
    const existingPermission = await this.permissionModel.findOne({
      action: createPermissionDto.action,
      resource: createPermissionDto.resource
    });

    if (existingPermission) {
      throw new ConflictException('Permission with this action and resource already exists');
    }

    const permission = new this.permissionModel(createPermissionDto);
    return permission.save();
  }

  async findAll(): Promise<Permission[]> {
    return this.permissionModel.find().exec();
  }

  async findById(id: string): Promise<Permission> {
    const permission = await this.permissionModel.findById(id).exec();

    if (!permission) {
      throw new NotFoundException('Permission not found');
    }

    return permission;
  }

  async updatePermission(id: string, updatePermissionDto: CreatePermissionDTO): Promise<Permission> {
    // Check if new action/resource combination conflicts with existing permissions
    const existingPermission = await this.permissionModel.findOne({
      action: updatePermissionDto.action,
      resource: updatePermissionDto.resource,
      _id: { $ne: id }
    });

    if (existingPermission) {
      throw new ConflictException('Permission with this action and resource already exists');
    }

    const permission = await this.permissionModel.findByIdAndUpdate(
      id,
      updatePermissionDto,
      { new: true }
    );

    if (!permission) {
      throw new NotFoundException('Permission not found');
    }

    return permission;
  }

  async deletePermission(id: string): Promise<Permission> {
    const permission = await this.permissionModel.findByIdAndDelete(id);
    if (!permission) {
      throw new NotFoundException('Permission not found');
    }
    return permission;
  }

  async findByActionAndResource(action: string, resource: string): Promise<Permission | null> {
    return this.permissionModel.findOne({ action, resource }).exec();
  }

  async bulkCreatePermissions(permissions: CreatePermissionDTO[]): Promise<Permission[]> {
    const createdPermissions : Permission[] = [];

    for (const permissionDto of permissions) {
      try {
        const permission = await this.createPermission(permissionDto);
        createdPermissions.push(permission);
      } catch (error) {
        if (error instanceof ConflictException) {
          // Skip if permission already exists
          continue;
        }
        throw error;
      }
    }

    return createdPermissions;
  }
}
