import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type BillingDocument = Billing & Document;

// Define schemas for nested objects first
@Schema({ timestamps: true, collection: 'billing_line_item' })
class LineItem {
  @Prop({ type: Types.ObjectId, ref: 'Product', required: true })
  product: Types.ObjectId;

  @Prop()
  description: string;

  @Prop({ required: true })
  unit: string;

  @Prop({ required: true })
  quantity: number;

  @Prop({ required: true })
  price: number;

  @Prop({ required: true })
  tax: number;

  @Prop({ required: true })
  total: number;
}

@Schema({ timestamps: true, collection: 'billing_attachment' })
class Attachment {
  @Prop()
  fileName: string;

  @Prop()
  fileType: string;

  @Prop()
  fileUrl: string;

  @Prop()
  description: string;
}

@Schema({ timestamps: true, collection: 'billing_approval' })
class Approval {
  @Prop({ required: true, type: Types.ObjectId, ref: 'User' })
  userId: Types.ObjectId;
  
  @Prop({ required: true })
  approverRole: string;

  @Prop({ default: 'pending' })
  status: string;

  @Prop()
  approvedAt?: Date;

  @Prop()
  comments?: string;
}

// Create schemas for nested classes
export const LineItemSchema = SchemaFactory.createForClass(LineItem);
export const AttachmentSchema = SchemaFactory.createForClass(Attachment);
export const ApprovalSchema = SchemaFactory.createForClass(Approval);

// Define main billing schema after the nested schemas are available
@Schema({ timestamps: true, collection: 'billing_main'})
export class Billing extends Document {
  @Prop({ required: true })
  billingId: string;

  @Prop({ type: Types.ObjectId, ref: 'Department', required: true })
  department: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Project', required: true })
  project: Types.ObjectId;

  @Prop({ required: true })
  billingDate: Date;

  @Prop({ required: true })
  currency: string;

  @Prop({ required: true })
  bankAccount: string;

  @Prop({ required: true })
  paymentTerms: string;

  @Prop({ type: [LineItemSchema] })
  lineItems: LineItem[];

  @Prop({ type: [AttachmentSchema] })
  attachments: Attachment[];

  @Prop({ type: [ApprovalSchema] })
  approvals: Approval[];

  // Store the current approver index - which approver's turn is it
  @Prop({ default: 0 })
  currentApproverIndex: number;

  // Track if we're in the regular approval flow or finance approval flow
  @Prop({ type: Boolean, default: false })
  isFinanceApprovalActive: boolean;

  @Prop({ default: 'pending' })
  status: string;

  @Prop()
  saleOrderId?: string;

  @Prop()
  deliveryOrderId?: string;

  @Prop()
  invoiceId?: string;

  @Prop()
  saleOrderFile?: string;

  @Prop()
  deliveryOrderFile?: string;

  @Prop()
  invoiceFile?: string;

  @Prop()
  isSOCreated?: boolean;

  @Prop()
  SOCreatedAt?: Date;

  @Prop()
  isDOCreated?: boolean;

  @Prop()
  DOCreatedAt?: Date;

  @Prop()
  isInvoiceCreated?: boolean;

  @Prop()
  InvoiceCreatedAt?: Date;

  @Prop()
  isInvoicePosted?: boolean;

  @Prop()
  InvoicePostedAt?: Date;

  @Prop({ type: [ApprovalSchema], default: [] })
  financeApprovals: Approval[];
 
  // Add a separate status field for finance approval
  @Prop({ default: 'pending' })
  financeApprovalStatus: string; // 'pending', 'approved', 'rejected'

  @Prop()
  completedAt?: Date;

  // Track the workflow stage (for UI visualization)
  @Prop({ enum: ['billing-advice', 'sale-order', 'delivery-order', 'invoice', 'finance', 'collection'], default: 'billing-advice' })
  workflowStage: string;

  @Prop({ required: true, type: Types.ObjectId, ref: 'User' })
  createdBy: string;
}

// Create main schema after all components are defined
export const BillingSchema = SchemaFactory.createForClass(Billing);