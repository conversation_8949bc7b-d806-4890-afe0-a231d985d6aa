import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true, collection: 'department' })
export class Department extends Document {
  @Prop({ required: true })
  code: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  dimensionType: string;

  @Prop({ required: true })
  isActive: boolean;

  // Add reference to Manager
  @Prop({ type: Types.ObjectId, ref: 'User' })
  manager: Types.ObjectId; 

  // Add reference to Head of Department
  @Prop({ type: Types.ObjectId, ref: 'User' })
  hod: Types.ObjectId; 

  // Add reference to group CEO for the department
  @Prop({ type: Types.ObjectId, ref: 'User' })
  ceo: Types.ObjectId; 
}

export const DepartmentSchema = SchemaFactory.createForClass(Department);
