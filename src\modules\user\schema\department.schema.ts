import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
export type DepartmentDocument = Department & Document;

@Schema({ timestamps: true, collection: 'department' })
export class Department extends Document {
  @Prop({ required: true })
  code: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  dimensionType: string;

  @Prop({ required: true, index: true })
  division: string; // GWS, etc.

  @Prop({ type: Types.ObjectId, ref: 'User', index: true })
  hodPersonId: Types.ObjectId; // Head of Department person

  @Prop({ default: false, index: true })
  enableHodApproval: boolean; // Configuration for HOD approval

  @Prop({ default: 1 })
  hodWorkflowOrder: number; // Order in workflow (before HR)

  @Prop({ required: true })
  isActive: boolean;

  // Add reference to Manager
  @Prop({ type: Types.ObjectId, ref: 'User' })
  manager: Types.ObjectId; 

  // Add reference to Head of Department
  @Prop({ type: Types.ObjectId, ref: 'User' })
  hod: Types.ObjectId; 

  // Add reference to group CEO for the department
  @Prop({ type: Types.ObjectId, ref: 'User' })
  ceo: Types.ObjectId; 
}

export const DepartmentSchema = SchemaFactory.createForClass(Department);

DepartmentSchema.index({ division: 1, isActive: 1 });
DepartmentSchema.index({ enableHodApproval: 1, isActive: 1 });
