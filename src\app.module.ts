import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { DatabaseConfigService } from './config/database.config';
import { HealthModule } from './modules/health/health.module';
import { TestModule } from './modules/test/test.module';
import { UserModule } from './modules/user/user.module';
import { SeahModule } from './modules/seah/seah.module';
import { MigrationModule } from './modules/migration/migration.module';
import { PaymentAdviceModule } from './modules/pa/pa.module';
import { FinanceModule } from './modules/finance/finance.module';
import { ESignModule } from './modules/esign/esign.module';
import { CommonModule } from './modules/common/common.module';
import { AutoBillingModule } from './modules/auto-billing/auto-billing.module';
import { AuthModule } from './modules/auth/auth.module';
import { ScheduleModule } from '@nestjs/schedule';
import { FaceModule } from './modules/face/face.module';
import { IPPAModule } from './modules/ippa/ippa.module';

@Module({
  imports: [
      // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env', '.env.local'],
    }),
    
    // DocumentDB connection
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const dbConfigService = new DatabaseConfigService(configService);
        return dbConfigService.createMongooseOptions();
      },
      inject: [ConfigService],
    }),
    ScheduleModule.forRoot(),
    CommonModule,
    FaceModule,
    HealthModule,
    TestModule,
    UserModule,
    SeahModule,
    MigrationModule,
    PaymentAdviceModule,
    FinanceModule,
    ESignModule,
    AutoBillingModule,
    IPPAModule,
    AuthModule
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
