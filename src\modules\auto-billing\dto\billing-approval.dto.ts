import { IsString, <PERSON>NotEmpty, <PERSON>E<PERSON>, IsOptional } from 'class-validator';

export enum ApprovalStatus {
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PENDING = 'pending',
}

export class ApprovalActionDto {

  @IsString()
  @IsNotEmpty()
  billingId: string;

  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsEnum(ApprovalStatus)
  @IsNotEmpty()
  status: ApprovalStatus;

  @IsString()
  @IsOptional()
  comments?: string;
}