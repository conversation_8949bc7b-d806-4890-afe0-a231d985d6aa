import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { S3Service } from './services/s3.service';
import { SchedulerService } from './services/scheduler.service';
import { SecurityService } from '../user/service/security.service';
import { HttpModule, HttpService } from '@nestjs/axios';
import { EmailScheduler, EmailSchedulerSchema } from './schema/pa-email.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: EmailScheduler.name, schema: EmailSchedulerSchema },
    ]),
    HttpModule,
  ],
  controllers: [],
  providers: [S3Service, SchedulerService, SecurityService],
  exports: [S3Service, SchedulerService, SecurityService, MongooseModule, HttpModule],
})
export class CommonModule {}
