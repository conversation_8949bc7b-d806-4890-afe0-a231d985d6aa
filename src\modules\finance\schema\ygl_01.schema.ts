import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type YGL01Document = YGL01 & Document;

@Schema({ timestamps: true, collection: 'ygl_01' })
export class YGL01 {

  @Prop()
  CompanyCode: string;

  @Prop()
  CompanyName: string;

  @Prop()
  LedgerType: string;

  @Prop()
  StartDate: string;

  @Prop()
  EndDate: string;

  @Prop()
  GLCode: string;

  @Prop()
  GLDescription: string;

  @Prop()
  Type: string;

  @Prop()
  DocumentNo: string;

  @Prop()
  Date: string;

  @Prop()
  Description: string;

  @Prop()
  Reference: string;

  @Prop()
  OffsetAccount: string;

  @Prop()
  SourceDocument: string;

  @Prop()
  ExtraField: string;

  @Prop()
  MM: string;

  @Prop()
  BRC: string;

  @Prop()
  BRCDate: string;

  @Prop()
  DepartmentCode: string;

  @Prop()
  DepartmentName: string;

  @Prop()
  ProjectCode: string;

  @Prop()
  ProjectName: string;

  @Prop()
  BusinessAccount: string;

  @Prop()
  BusinessName: string;

  @Prop()
  Currency: string;

  @Prop()
  DocumentAmount: number;

  @Prop()
  DebitAmount: number;

  @Prop()
  CreditAmount: number;

  @Prop()
  CumulativeBalance: number;

  @Prop()
  Movement: number;

  @Prop()
  User: string;

  @Prop()
  Division: string;

  @Prop()
  SubDivision: string;

  @Prop()
  Line: string;

  @Prop()
  isExcluded: boolean;

  @Prop()
  remark: string;
}
export const YGL01Schema = SchemaFactory.createForClass(YGL01);

// Define indexes separately after schema creation
YGL01Schema.index({ CompanyCode: 1, LedgerType: 1 });
YGL01Schema.index({ DepartmentCode: 1 });
YGL01Schema.index({ GLCode: 1 });
YGL01Schema.index({ Date: -1 });
YGL01Schema.index({ DocumentNo: 1 });
YGL01Schema.index({ ProjectCode: 1 });
YGL01Schema.index({ BusinessAccount: 1, Currency: 1 });
