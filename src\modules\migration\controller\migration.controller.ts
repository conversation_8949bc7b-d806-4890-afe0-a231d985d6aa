import { Controller, Post, Get, UseGuards } from '@nestjs/common';
import { MigrationService } from '../service/migration.service';

@Controller('migration')
// @UseGuards(JwtAuthGuard) // Protect migration endpoints
export class MigrationController {
  constructor(private migrationService: MigrationService) {}

  @Post('user-roles')
  async migrateUserRoles() {
    try {
      const result = await this.migrationService.migrateUserRolesToArray();
      return {
        success: true,
        message: 'Migration completed',
        data: result
      };
    } catch (error) {
      return {
        success: false,
        message: 'Migration failed',
        error: error.message
      };
    }
  }

  @Get('verify')
  async verifyMigration() {
    try {
      const result = await this.migrationService.verifyMigration();
      return {
        success: true,
        message: 'Verification completed',
        data: result
      };
    } catch (error) {
      return {
        success: false,
        message: 'Verification failed',
        error: error.message
      };
    }
  }

  @Post('rollback')
  async rollbackMigration() {
    try {
      const result = await this.migrationService.rollbackMigration();
      return {
        success: true,
        message: 'Rollback completed',
        data: result
      };
    } catch (error) {
      return {
        success: false,
        message: 'Rollback failed',
        error: error.message
      };
    }
  }
}