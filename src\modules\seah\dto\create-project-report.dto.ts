import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class CreateProjectReportDto {
  @IsNotEmpty()
  @IsString()
  readonly title: string;

  @IsNotEmpty()
  @IsString()
  readonly description: string;

  @IsNotEmpty()
  @IsString()
  readonly month: string;

  @IsOptional()
  @IsString()
  readonly pdf_link?: string;

  @IsOptional()
  @IsString()
  readonly doc_link?: string;

  @IsNotEmpty()
  readonly updatedBy: string;  // This should be a valid MongoDB ObjectId (string)
}
