import { ForbiddenException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as XLSX from 'xlsx';
import { Payment, PaymentDocument } from '../schema/pv.schema';

@Injectable()
export class SeahPVService {
  constructor(
    @InjectModel(Payment.name) private paymentModel: Model<PaymentDocument>,
  ) { }

  async importPaymentsFromExcel(buffer: Buffer): Promise<string> {
    // throw new ForbiddenException('Failed to process PV.');

    // Parse the uploaded Excel file
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const sheet = workbook.Sheets[workbook.SheetNames[0]];

    // Read data from the Excel file starting from row 4 and include headers from row 3
    const excelData = XLSX.utils.sheet_to_json(sheet, { header: 1, range: 2 });

    // Get the headers from the 4th row (index 0 of excelData)
    const headers: any = excelData[0];
    const rows: any[] = excelData.slice(1);

    // console.log("headers: ", headers);
    // console.log("rows: ", rows);
    // Store new records to be inserted
    const newPayments: any = [];

    for (const row of rows) {
      if (this.isRowEmpty(row)) {
        console.log("Skipping empty row:", row);
        continue;
      }
      const paymentData : any = {};

      // Map each header to the corresponding value in the row
      headers.forEach((header, index) => {
        paymentData[header] = row[index];
      });

      // Log each row's `PV No.` to debug the issue
      // console.log("PV No. Value: ", paymentData['PV No.']);

      // console.log("data: ", paymentData['PV No.']);


      // Check if this payment already exists in the database using a unique key (e.g., `PV No.` or `Reference No.`)
      // const existingPayment = await this.paymentModel.findOne({ PVNo: paymentData['PV No.'] }).exec();
      // console.log("Existing : ", existingPayment);

      if (row) {
        newPayments.push({
          PVNo: paymentData['PV No.'],
          date: paymentData['Date'],
          BP: paymentData['BP'],
          BPName: paymentData['BP Name'],
          country: paymentData['Country'],
          controlAccount: paymentData['Control A/C'],
          bank: paymentData['Bank'],
          description: paymentData['Description'],
          referenceNo: paymentData['Reference No.'],
          invoiceNo: paymentData['Invoice No.'],
          department: paymentData['Department'],
          project: paymentData['Project'],
          currency: paymentData['Currency'],
          docAmount: paymentData['Doc Amount'],
          rate: paymentData['Rate'],
          converted: paymentData['Converted'],
          status: paymentData['Status'],
          PIC: paymentData['PIC'],
          isExcluded: false,
          remark: '',
        });
      }
      // return `${newPayments.length} new payments imported successfully`;
      // if (existingPayment == null || undefined) { // Ensure PVNo is valid
      // Manually map the values to the MongoDB schema fields
      //}
    }
    await this.paymentModel.insertMany(newPayments);
    return `${newPayments.length} new payments imported successfully`;

    // Insert only the new records into the database
    // if (newPayments.length > 0) {
    // }
  }

  convertToDate(dateString: string): Date {
    // Check if dateString is valid and follows `DD/MM/YYYY` format
    if (!dateString || typeof dateString !== 'string') return new Date();

    // Split the dateString into [DD, MM, YYYY]
    const parts = dateString.split('/');

    // Ensure there are exactly 3 parts (day, month, year)
    if (parts.length !== 3) return new Date();

    // Rearrange to `YYYY-MM-DD` format and create a new Date object
    const formattedDate = `${parts[2]}-${parts[1]}-${parts[0]}`;
    return new Date(formattedDate);
  }

  isRowEmpty(row: any[]): boolean {
    const nonEmptyCells = row.filter(cell => cell !== null && cell !== undefined && cell !== '');
    return nonEmptyCells.length === 0;  // Only process rows with at least one meaningful value
  }

  async findAll(): Promise<any> {
    const payments = await this.paymentModel.aggregate([
      {
        $addFields: {
          parsedDate: {
            $dateFromString: {
              dateString: "$date",
              format: "%d/%m/%Y"  // Specify the format for conversion
            }
          }
        }
      },
      {
        $sort: { parsedDate: -1 }  // Sort by the converted date in descending order
      },
      {
        $project: {
          parsedDate: 0  // Remove the temporary `parsedDate` field from the result
        }
      }
    ]).exec();
  
    return { success: true, data: payments };
  }
  

  // NEW METHOD: Include or Exclude a payment by toggling the `isExcluded` status
  async toggleIsExcludedStatus(id: string, isExcluded: boolean): Promise<Payment> {
    const updatedPayment = await this.paymentModel.findByIdAndUpdate(
      id,
      { isExcluded },
      { new: true }, // Return the updated document
    );

    if (!updatedPayment) {
      throw new Error(`Payment with id ${id} not found`);
    }
    
    return updatedPayment;
  }

  async getTotalNonExcludedPayments(): Promise<any> {
    // Use `$facet` to handle multiple aggregations within a single query
    const result = await this.paymentModel.aggregate([
      { $match: { isExcluded: false } }, // Match only non-excluded payments
      {
        $facet: {
          // Facet 1: Total non-excluded payments
          totalNonExcludedPayments: [{ $group: { _id: null, total: { $sum: '$converted' } } }],
          // Facet 2: Total payments grouped by `status`
          totalPaymentsByStatus: [{ $group: { _id: '$status', total: { $sum: '$converted' } } }],
          // Facet 3: Get the latest `date` in the collection (assuming date format is DD/MM/YYYY)
          latestUpdatedAt: [
            {
              $project: {
                // Convert date string to a Date object using a custom format, then return the original date string
                dateObject: {
                  $dateFromString: { dateString: "$date", format: "%d/%m/%Y" }
                },
                date: 1
              }
            },
            { $sort: { dateObject: -1 } }, // Sort by the dateObject (descending)
            { $limit: 1 }, // Get the latest date only
          ]
        }
      }
    ]);
  
    // Extract results from the facets
    const totalNonExcluded = result[0].totalNonExcludedPayments.length > 0 ? result[0].totalNonExcludedPayments[0].total : 0;
  
    // Format `totalsByStatus` to use snake_case for the keys
    const totalsByStatus = result[0].totalPaymentsByStatus.reduce((acc, item) => {
      const formattedStatus = item._id ? item._id.toLowerCase().replace(/\s+/g, '_') : '';
      acc[formattedStatus] = item.total;
      return acc;
    }, {});
  
    // Get the latest updatedAt date from the result
    const latestUpdatedAt = result[0].latestUpdatedAt.length > 0 ? result[0].latestUpdatedAt[0].date : null;
  
    // Return a combined response object with formatted keys and latestUpdatedAt
    return {
      totalNonExcluded,
      totalsByStatus,
      updatedAt: latestUpdatedAt // This is the latest 'date' from the collection
    };
  }  

  async getTotalPaymentsByStatus(): Promise<any> {
    // Aggregate and group by the `status` field to calculate totals for each status
    const result = await this.paymentModel.aggregate([
      { $match: { isExcluded: false } }, // Optional: Match non-excluded payments if needed
      {
        $group: {
          _id: '$status', // Group by `status` field
          total: { $sum: '$converted' }, // Sum up the `converted` field for each status group
        },
      },
    ]);

    // Format the result into a more readable object
    const totalsByStatus = result.reduce((acc, item) => {
      acc[item._id] = item.total;
      return acc;
    }, {});

    return totalsByStatus;
  }

  async getTotalExcludedPayments(): Promise<any> {
    // Use aggregation to sum up the `docAmount` field where `isExcluded` is false
    const result = await this.paymentModel.aggregate([
      { $match: { isExcluded: true } }, // Match documents where `isExcluded` is false
      { $group: { _id: null, total: { $sum: '$converted' } } }, // Sum up the `docAmount` field
    ]);

    // Return the total sum, or 0 if no matching documents
    return {
      total: result.length > 0 ? result[0].total : 0
    }
  }
}
