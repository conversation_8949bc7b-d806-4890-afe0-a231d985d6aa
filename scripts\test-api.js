#!/usr/bin/env node

const http = require('http');

// Configuration
const BASE_URL = 'http://localhost:3000';
const API_PREFIX = '/api';

// Helper function to make HTTP requests
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    
    const options = {
      hostname: url.hostname,
      port: url.port || 3000,
      path: url.pathname + url.search,
      method: method.toUpperCase(),
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          console.log(`✅ ${method.toUpperCase()} ${path}`);
          console.log(`📊 Status: ${res.statusCode}`);
          console.log(`📄 Response:`, JSON.stringify(jsonBody, null, 2));
          resolve(jsonBody);
        } catch (error) {
          console.log(`✅ ${method.toUpperCase()} ${path}`);
          console.log(`📊 Status: ${res.statusCode}`);
          console.log(`📄 Response:`, body);
          resolve(body);
        }
      });
    });

    req.on('error', (error) => {
      console.error(`❌ Error calling ${method.toUpperCase()} ${path}:`, error.message);
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Test functions
async function testBankReceipts() {
  console.log('🧪 Testing Bank Receipts API...\n');
  
  const testData = {
    paymentAdviceId: "60f7b3b3b3b3b3b3b3b3b3b3", // Replace with real ID
    bankReceipts: [
      {
        receiptNumber: `TEST-${Date.now()}`,
        receiptDate: new Date().toISOString(),
        amount: 5000.00,
        bankName: "Test Bank",
        accountNumber: "**********",
        s3Url: "https://example.com/test-receipt.pdf",
        remark: "CLI Test Payment"
      }
    ]
  };

  try {
    await makeRequest('PATCH', `${API_PREFIX}/payment-advice/bank-receipts`, testData);
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

async function testHealthCheck() {
  console.log('🏥 Testing Health Check...\n');
  
  try {
    await makeRequest('GET', '/health');
  } catch (error) {
    console.error('Health check failed:', error.message);
  }
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  console.log('🚀 UzmaCloud API Tester\n');

  switch (command) {
    case 'bank-receipts':
      await testBankReceipts();
      break;
    
    case 'health':
      await testHealthCheck();
      break;
    
    case 'custom':
      // Custom API call
      const method = args[1] || 'GET';
      const path = args[2] || '/health';
      const data = args[3] ? JSON.parse(args[3]) : null;
      
      console.log(`🔧 Custom API call: ${method.toUpperCase()} ${path}\n`);
      await makeRequest(method, path, data);
      break;
    
    default:
      console.log('📖 Usage:');
      console.log('  node scripts/test-api.js bank-receipts');
      console.log('  node scripts/test-api.js health');
      console.log('  node scripts/test-api.js custom GET /api/payment-advice/list');
      console.log('  node scripts/test-api.js custom PATCH /api/payment-advice/bank-receipts \'{"paymentAdviceId":"..."}\'');
      break;
  }
}

// Run the script
main().catch(console.error);
