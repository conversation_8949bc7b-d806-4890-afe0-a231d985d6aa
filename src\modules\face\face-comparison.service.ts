import { GetFaceLivenessSessionResultsCommand, RekognitionClient } from '@aws-sdk/client-rekognition';
import { Injectable, NotFoundException } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import { v4 as uuidv4 } from 'uuid'; // Install uuid library

@Injectable()
export class FaceComparisonService {
    private s3: AWS.S3;
    private rekognition: AWS.Rekognition;
    private rekognitionClient: RekognitionClient;
  
    constructor() {
      this.s3 = new AWS.S3({
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        region: process.env.AWS_DEFAULT_REGION,
      });
      this.rekognition = new AWS.Rekognition({
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        region: process.env.AWS_DEFAULT_REGION,
      });

      this.rekognitionClient = new RekognitionClient({
        region: 'ap-south-1', // Specify your AWS region
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
        },
      });
    }
  
    async getImageFromS3ByKey(key: string): Promise<Buffer | null> {
      try {
        const params: any = {
          Bucket: process.env.AWS_S3_BUCKET,
          Key: key,
        };
        const data = await this.s3.getObject(params).promise();
        return data.Body as Buffer;
      } catch (error) {
        console.error('Error fetching image from S3:', error);
        return null;
      }
    }
  
    async compareFace(sourceImage: Buffer, targetImage: Buffer): Promise<boolean> {
      const params = {
        SourceImage: {
          Bytes: sourceImage,
        },
        TargetImage: {
          Bytes: targetImage,
        },
        SimilarityThreshold: 80, // Adjust similarity threshold as needed
      };
  
      try {
        const result = await this.rekognition.compareFaces(params).promise();
        return (result.FaceMatches?.length ?? 0) > 0;
      } catch (error) {
        console.error('Error comparing images with Rekognition:', error);
        return false;
      }
    }

    async searchFaces(sourceImage: Buffer): Promise<any> {
      // Call AWS Rekognition to search for the face
      const params: any = {
        CollectionId: process.env.REKOGNITION_COLLECTION_ID,
        Image: {
          Bytes: sourceImage
        },
        MaxFaces: 1,
        FaceMatchThreshold: 80 // Adjust as needed
      };

      const response = await this.rekognition.searchFacesByImage(params).promise();

      // if(!response) {
      //   throw new NotFoundException("Face Recoginition Error")
      // }

      // Check if there is a match
      const match = response.FaceMatches && response.FaceMatches.length > 0;

      var bestMatch: any;
      if(response.FaceMatches) {
        bestMatch = match ? response.FaceMatches[0] : null;
      } else {
        bestMatch = null;
      }

      return {
        match,
        matchedFaceId: bestMatch?.Face?.FaceId,
        similarity: bestMatch?.Similarity
      };
    }

    // Step 1: Create Liveness Session
  async createLivenessSession(): Promise<any> {
    // Generate a random ClientRequestToken
    const clientRequestToken = `LIVENESS-${uuidv4()}`;
    
    const params: any = {
      "ClientRequestToken": clientRequestToken
    };

    try {
      const response = await this.rekognition.createFaceLivenessSession(params).promise();

      var data = { "sessionId": response.SessionId}
      return data; // Return session ID to the frontend
  
    } catch (error) {
      console.error('Error creating liveness session:', error);
      throw new Error('Failed to create liveness session');
    }
  }

  // Step 2: Get Liveness Session Results
  async getLivenessResults(sessionId: string): Promise<any> {
    try {
      // Retrieve liveness results using sessionId
      const command = new GetFaceLivenessSessionResultsCommand({ SessionId: sessionId });
      const response: any = await this.rekognitionClient.send(command);

      // Simplify and return the result
      return {
        confidence: response.Confidence || null,
        status: response.Status || 'UNKNOWN',
        auditImages: response.AuditImages || [],
      };
    } catch (error) {
      console.error('Error getting liveness results:', error);
      throw new Error('Failed to retrieve liveness results');
    }
  }
}
