// pa-message.controller.ts
import { Body, Controller, Get, Param, Post, Put, UseGuards, Request } from '@nestjs/common';
import { CreateMessageDto } from '../dto/create-message.dto';
import { PaymentAdviceMessageService } from '../service/pa-message.service';

@Controller('pa-messages')
export class PaymentAdviceMessageController {
  constructor(private readonly messageService: PaymentAdviceMessageService) {}

  @Post()
  async create(@Body() createMessageDto: CreateMessageDto) {
    return this.messageService.create(createMessageDto);
  }

  @Get(':id')
  async findAllByPaymentAdviceId(@Param('id') id: string) {
    return this.messageService.findAllByPaymentAdviceId(id);
  }

  @Put(':id/read')
  async markAsRead(@Param('id') id: string, @Body() body) {
    return this.messageService.markAsRead(id, body.userId);
  }
  
  @Post('unread-count')
  async getUnreadCount(@Body() body) {
    return { count: await this.messageService.getUnreadMessageCount(body) };
  }
}