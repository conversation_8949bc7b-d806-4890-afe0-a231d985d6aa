import { Injectable, Logger } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(
    @InjectConnection() private readonly mongoConnection: Connection,
  ) { }

  async checkHealth() {
    try {
      const dbStatus = await this.checkDatabaseHealth();
      const isHealthy = dbStatus.connected;

      return {
        status: isHealthy ? 'ok' : 'degraded',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        database: dbStatus,
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          usage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100)
        },
        pid: process.pid
      };
    } catch (error) {
      this.logger.error('Health check failed:', error.message);
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message,
        uptime: process.uptime()
      };
    }
  }

  async checkDetailedHealth() {
    try {
      const dbStatus = await this.checkDatabaseHealth();
      const memUsage = process.memoryUsage();

      return {
        status: dbStatus.connected ? 'ok' : 'degraded',
        timestamp: new Date().toISOString(),
        service: 'uzmacloud-api',
        version: '1.0.0',
        uptime: {
          seconds: process.uptime(),
          human: this.formatUptime(process.uptime()),
        },
        system: {
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch,
          pid: process.pid,
          memory: {
            used: Math.round(memUsage.heapUsed / 1024 / 1024),
            total: Math.round(memUsage.heapTotal / 1024 / 1024),
            external: Math.round(memUsage.external / 1024 / 1024),
            rss: Math.round(memUsage.rss / 1024 / 1024),
            usage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
          },
          cpuUsage: process.cpuUsage(),
        },
        database: {
          ...dbStatus,
          connectionString: process.env.DATABASE_URL ? 'configured' : 'not configured',
        },
        environment: {
          nodeEnv: process.env.NODE_ENV || 'development',
          port: process.env.PORT || 3000,
          frontendUrl: process.env.FRONTEND_URL || 'not configured'
        },
        endpoints: {
          health: '/health',
          healthDetailed: '/health/detailed',
          healthReady: '/health/ready',
          healthLive: '/health/live',
          docs: '/api/docs',
          api: '/api/v1',
        },
      };
    } catch (error) {
      this.logger.error('Detailed health check failed:', error.message);
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message,
        service: 'uzmacloud-api'
      };
    }
  }

  private async checkDatabaseHealth() {
    try {
      if (!this.mongoConnection) {
        return {
          connected: false,
          status: 'no_connection',
          message: 'Database connection not initialized',
        };
      }

      const state = this.mongoConnection.readyState;
      const stateNames = {
        0: 'disconnected',
        1: 'connected',
        2: 'connecting',
        3: 'disconnecting',
      };

      if (state === 1) {
        // Test actual connectivity with a ping
        const startTime = Date.now();
        await this.mongoConnection.db.admin().ping();
        const responseTime = Date.now() - startTime;

        return {
          connected: true,
          status: 'healthy',
          state: stateNames[state],
          host: this.mongoConnection.host,
          name: this.mongoConnection.name,
          responseTime: `${responseTime}ms`,
          collections: await this.getCollectionCount(),
          readyState: state
        };
      }

      return {
        connected: false,
        status: stateNames[state] || 'unknown',
        state: stateNames[state],
        readyState: state
      };
    } catch (error) {
      this.logger.error('Database health check failed:', error.message);
      return {
        connected: false,
        status: 'error',
        message: error.message,
        error: error.name
      };
    }
  }

  private async getCollectionCount(): Promise<number> {
    try {
      const collections = await this.mongoConnection.db.listCollections().toArray();
      return collections.length;
    } catch (error) {
      this.logger.warn('Failed to get collection count:', error.message);
      return 0;
    }
  }

  private formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    const parts: string[] = []; // ✅ Explicitly type as string array
    if (days > 0) parts.push(`${days}d`);
    if (hours > 0) parts.push(`${hours}h`);
    if (minutes > 0) parts.push(`${minutes}m`);
    if (secs > 0) parts.push(`${secs}s`);

    return parts.join(' ') || '0s';
  }
}