import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ProjectCostDetail } from '../schema/project-cost-detail.schema';
import { CreateProjectCostDetailDto, UpdateProjectCostDetailDto } from '../dto/project-cost-detail.dto';

@Injectable()
export class ProjectCostDetailService {
  constructor(
    @InjectModel(ProjectCostDetail.name) private projectCostDetailModel: Model<ProjectCostDetail>,
  ) {}

   // Method to update all project cost details at once
   async updateAllProjectCostDetails(updateCostDetailsDto: ProjectCostDetail[]): Promise<any> {
    const bulkOps = updateCostDetailsDto.map((costDetail) => ({
      updateOne: {
        filter: { title: costDetail.title },
        update: { $set: costDetail },
        upsert: true,  // Optional: This will create a new document if no match is found
      },
    }));
    return this.projectCostDetailModel.bulkWrite(bulkOps);
  }
  
  // Create a new Project Cost Detail (Unique by Title)
  async createProjectCostDetail(createDto: CreateProjectCostDetailDto): Promise<ProjectCostDetail> {
    const { title } = createDto;
    
    // Check if the title already exists
    const existingDetail = await this.projectCostDetailModel.findOne({ title });
    if (existingDetail) {
      throw new BadRequestException(`Project Cost Detail with title "${title}" already exists.`);
    }

    const createdDetail = new this.projectCostDetailModel(createDto);
    return createdDetail.save();
  }

  // Get all Project Cost Details
  async getAllProjectCostDetails(): Promise<ProjectCostDetail[]> {
    return this.projectCostDetailModel.find().exec();
  }

  // Get a Project Cost Detail by Title
  async getProjectCostDetailByTitle(title: string): Promise<ProjectCostDetail> {
    const detail = await this.projectCostDetailModel.findOne({ title }).exec();
    if (!detail) {
      throw new NotFoundException(`Project Cost Detail with title "${title}" not found.`);
    }
    return detail;
  }

  // Update a Project Cost Detail by Title
  async updateProjectCostDetailByTitle(
    title: string,
    updateDto: UpdateProjectCostDetailDto,
  ): Promise<ProjectCostDetail> {
    const updatedDetail = await this.projectCostDetailModel.findOneAndUpdate({ title }, updateDto, {
      new: true,
    });
    if (!updatedDetail) {
      throw new NotFoundException(`Project Cost Detail with title "${title}" not found.`);
    }
    return updatedDetail;
  }

  // Delete a Project Cost Detail by Title
  async deleteProjectCostDetailByTitle(title: string): Promise<ProjectCostDetail | any> {
    const deletedDetail = await this.projectCostDetailModel.findOneAndDelete({ title }).exec();
    if (!deletedDetail) {
      throw new NotFoundException(`Project Cost Detail with title "${title}" not found.`);
    }
    return deletedDetail;
  }
}
