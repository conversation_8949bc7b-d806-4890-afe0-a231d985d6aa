import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common';
import { ApprovalFlow } from '../schema/approval-flow.schema';
import { CreateApprovalFlowDto, GetApprovalFlowQueryDto, UpdateApprovalFlowDto } from '../dto/approval-flow.dto';
import { ApprovalFlowService } from '../service/approval-flow.service';

@Controller('approval-flows')
export class ApprovalFlowController {
  constructor(private readonly approvalFlowService: ApprovalFlowService) {}

  @Post()
  async create(@Body() createDto: CreateApprovalFlowDto): Promise<ApprovalFlow> {
    return this.approvalFlowService.createApprovalFlow(createDto);
  }

  @Get()
  async findAll(@Query() query: GetApprovalFlowQueryDto): Promise<ApprovalFlow[]> {
    return this.approvalFlowService.getAllApprovalFlows(query);
  }

  @Get('department/:department/project/:project/type/:documentType')
  async findByDepartmentAndProject(
    @Param('department') department: string,
    @Param('project') project: string,
    @Param('documentType') documentType: string,
  ): Promise<ApprovalFlow> {
    return this.approvalFlowService.getApprovalFlow(department, project, documentType);
  }

  @Put(':flowId')
  async update(
    @Param('flowId') flowId: string,
    @Body() updateDto: UpdateApprovalFlowDto,
  ): Promise<ApprovalFlow> {
    return this.approvalFlowService.updateApprovalFlow(flowId, updateDto);
  }

  @Delete(':flowId')
  async deactivate(@Param('flowId') flowId: string): Promise<ApprovalFlow> {
    return this.approvalFlowService.deactivateApprovalFlow(flowId);
  }
}