import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PJMAccrual, PJMAccrualDocument } from '../schema/finance_pjm_accrual';

@Injectable()
export class PJMAccrualService {
  constructor(
    @InjectModel(PJMAccrual.name) private readonly pjmAccrualModel: Model<PJMAccrualDocument>,
  ) {}

  async create(pjmAccrual: PJMAccrual): Promise<PJMAccrual> {
    const newAccrual = new this.pjmAccrualModel(pjmAccrual);
    return newAccrual.save();
  }

  async findAll(): Promise<PJMAccrual[]> {
    return this.pjmAccrualModel.find().exec();
  }

  async findOne(id: string): Promise<PJMAccrual | null> {
    return this.pjmAccrualModel.findById(id).exec();
  }

  async update(id: string, updateData: Partial<PJMAccrual>): Promise<PJMAccrual | null> {
    return this.pjmAccrualModel.findByIdAndUpdate(id, updateData, { new: true }).exec();
  }

  async delete(id: string): Promise<PJMAccrual | null> {
    return this.pjmAccrualModel.findByIdAndDelete(id).exec();
  }
}
