import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BillingFinance, BillingFinanceSchema } from './schema/billing-finance.schema';
import { Product, ProductSchema } from './schema/billing-product.schema';
import { Project, ProjectSchema } from './schema/billing-project.schema';
import { Billing, BillingSchema } from './schema/billing.schema';
import { RpaLog, RpaLogSchema } from './schema/rpa-logs.schema';
import { RpaSchedule, RpaScheduleSchema } from './schema/rpa-schedule.schema';
import { BillingFinanceController } from './controller/billing-finance.controller';
import { BillingProductController } from './controller/billing-product.controller';
import { BillingProjectController } from './controller/billing-project-controller';
import { BillingController } from './controller/billing.controller';
import { RpaCallbackController } from './controller/rpa-callback.controller';
import { RpaSchedulerController } from './controller/rpa-schedule.controller';
import { BillingFinanceService } from './service/billing-finance.service';
import { BillingProductService } from './service/billing-product.service';
import { BillingProjectService } from './service/billing-project.service';
import { BillingService } from './service/billing.service';
import { RpaCallbackService } from './service/rpa-callback.service';
import { RpaSchedulerService } from './service/rpa-schedule.service';
import { RpaService } from './service/rpa.service';
import { UserService } from '../user/service/user.service';
import { CommonModule } from '../common/common.module';
import { UserModule } from '../user/user.module';
import { ApprovalFlow, ApprovalFlowSchema } from './schema/approval-flow.schema';
import { ApprovalFlowService } from './service/approval-flow.service';
import { ApprovalFlowController } from './controller/approval-flow.controller';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ApprovalFlow.name, schema: ApprovalFlowSchema},
      { name: BillingFinance.name, schema: BillingFinanceSchema },
      { name: Product.name, schema: ProductSchema},
      { name: Project.name, schema: ProjectSchema},
      { name: Billing.name, schema: BillingSchema },
      { name: RpaLog.name, schema: RpaLogSchema },
      { name: RpaSchedule.name, schema: RpaScheduleSchema }
    ]),
    CommonModule,
    AuthModule,
    UserModule
  ],
  controllers: [ApprovalFlowController, BillingFinanceController, BillingProductController, BillingProjectController, BillingController, RpaCallbackController, RpaSchedulerController],
  providers: [ApprovalFlowService, BillingFinanceService, BillingProductService, BillingProjectService, BillingService, RpaCallbackService, RpaSchedulerService, RpaService, UserService],
  exports: [],
})
export class AutoBillingModule {}