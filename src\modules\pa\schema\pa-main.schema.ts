import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Type } from 'class-transformer';
import { IsArray, IsMongoId, IsNotEmpty, IsString, ValidateNested } from 'class-validator';
import { Document, Types } from 'mongoose';

class PaymentTerm {
  @Prop({ type: Number })
  days: number;

  @Prop({ type: String })
  termType: string;  // e.g., "back_to_back", "advance_payment"
}

class SupportingDocument {
  @Prop({ type: String })
  type: string;

  @Prop({ type: String })
  s3Url: string;
}

class WorkflowParticipantDTO {
  @IsMongoId()
  userId: string;  // User ID of the participant

  @IsString()
  @IsNotEmpty()
  approvalType: string;  // Could be 'Approved By' or 'Reviewed By'
}

class WorkflowParticipant {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;  // User ID of the participant (linked to User collection)

  @Prop({ type: String, required: true })
  approvalType: string;  // Could be 'Approved By' or 'Reviewed By'
}

export type PaymentAdviceDocument = PaymentAdvice & Document;

@Schema({ timestamps: true, collection: 'pa_main' })
export class PaymentAdvice {

  @Prop({ type: String, unique: true })
  serialNumber: string;  // Unique serial number like "UZMA-EPA-000001"

  @Prop()
  companyName: string;

  @Prop()
  companyAddress: string;

  @Prop()
  telNumber: string;

  @Prop()
  faxNumber: string;

  @Prop()
  companyLogo: string;

  // Establish relationship with the Department collection
  @Prop({ type: Types.ObjectId, ref: 'Department', required: true })
  departmentCode: Types.ObjectId;  // Linked to Department collection

  @Prop()
  vendorName: string;

  @Prop()
  vendorAddress: string;

  @Prop()
  eInvoiceCompliance: string;

  @Prop({ type: PaymentTerm })
  paymentTerm: PaymentTerm;

  @Prop()
  currency: string;

  @Prop({ type: SupportingDocument })
  prDoc: SupportingDocument;

  @Prop({ type: SupportingDocument })
  poDoc: SupportingDocument;

  @Prop({ type: SupportingDocument })
  grnDoc: SupportingDocument;

  @Prop([
    {
      invoiceType: {type: String},
      invoiceNumber: { type: String },
      invoiceDate: { type: Date },
      poNumber: { type: String },
      projectCode: { type: String },
      amount: { type: Number },
      attachments: [
        {
          type: { type: String },
          s3Url: { type: String },
        }
      ],
    }
  ])
  documents: Array<{
    invoiceType: string;
    invoiceNumber: string;
    invoiceDate: Date;
    poNumber: string;
    projectCode: string;
    amount: number;
    attachments: Array<{
      type: string;
      s3Url: string;
    }>;
  }>;

  @Prop([
    {
      receiptNumber: { type: String },
      receiptDate: { type: String },
      amount: { type: Number },
      bankName: { type: String },
      accountNumber: { type: String },
      s3Url: { type: String },
      remark: { type: String },
      isPartial: { type: Boolean },
      balance: { type: Number },
      createdAt: { type: Date }, // Record creation date
      updatedAt: { type: Date }, // Track update date
    }
  ])
  bankReceipts: Array<{
    receiptNumber: string;
    receiptDate: string;
    amount: number;
    bankName: string;
    accountNumber: string;
    s3Url: string;
    remark: string;
    isPartial: boolean;
    balance: number;
    createdAt: Date; // Date when the receipt was added
    updatedAt: Date; // Date when the receipt was last updated
  }>;

  @Prop({ type: Boolean, default: false })
  isPartialPayment: boolean; // Flag for partial payment

  @Prop({ type: Number, default: 0 })
  partialPaymentBalance: number; // Remaining balance if partial payment

  @Prop()
  detailsPayment: string;

  @Prop()
  apbBudgeted: string;

  @Prop()
  serviceLocation: string;

  @Prop()
  staffConsultant: string;

  @Prop()
  chargeable: boolean;

  @Prop()
  accountManager: string;

  @Prop()
  costCategory: string;

  @Prop([
    {
      glCode: { type: String },
      amount: { type: Number },
    }
  ])
  chargeBacks: Array<{
    glCode: string;
    amount: number;
  }>;

  @Prop({
    type: {
      percentage: { type: Number },
      trfAttachment: {
        s3Url: { type: String },
      },
      verifiedBy: {
        name: { type: String },
        date: { type: Date },
      }
    }
  })
  withholdingTax: {
    percentage: string;
    trfAttachment: {
      s3Url: string;
    };
    verifiedBy: {
      name: string;
      date: Date;
    };
  };

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  createdBy: Types.ObjectId;  // User who created the Payment Advice

  @Prop([{ type: WorkflowParticipant }])  // Use the defined WorkflowParticipant class here
  workflowParticipants: WorkflowParticipant[];

  @Prop({ type: Types.ObjectId, ref: 'ApprovalWorkflow' })
  approvalWorkflowId: Types.ObjectId;

  @Prop()
  status: string; // e.g., "submitted", "approved", "rejected", "pending"

  @Prop({ type: Date })
  dueDate: Date;

  @Prop({ type: Number })
  amount: number;  // Amount of the payment

  @Prop({ type: Date })
  submittedAt: Date;  // When the PaymentAdvice was submitted

  @Prop({ type: Date })
  completedAt?: Date;  // When the PaymentAdvice workflow was completed

  @Prop({ type: Number })
  totalDuration?: number;  // Total duration (in hours) of the payment advice approval process
}

export const PaMainSchema = SchemaFactory.createForClass(PaymentAdvice);
// Ensure index is created for the serial number field
PaMainSchema.index({ serialNumber: 1 }, { unique: true });