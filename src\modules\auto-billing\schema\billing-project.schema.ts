import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ProjectDocument = Project & Document;

@Schema({ timestamps: true, collection: 'project_master' })
export class Project {
  @Prop({ required: true, unique: true })
  projectId: string;

  @Prop({ required: true })
  projectName: string;

  @Prop({ required: true })
  departmentId: string;  // Reference to department/company

  @Prop()
  description?: string;

  @Prop({ default: true })
  isActive: boolean;

  @Prop()
  startDate?: Date;

  @Prop()
  endDate?: Date;

  @Prop()
  projectManager?: string;

  @Prop({ type: Object })
  additionalDetails?: Record<string, any>;

  @Prop()
  bpCode?: string;

  @Prop()
  bpName?: string;

  @Prop()
  bizActivity?: string;

  @Prop()
  doReportCode?: string;

  @Prop()
  soReportCode?: string;

  @Prop()
  invoiceReportCode?: string;

  @Prop()
  saleSites?: string;

  @Prop()
  shipmentSites?: string;

  @Prop()
  projectCode?: string;
}

export const ProjectSchema = SchemaFactory.createForClass(Project);