import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';

class TableData {
  @Prop({ type: [String] }) // Array of strings for headers
  headers: string[];

  @Prop({ type: [[Object]] }) // Array of arrays with mixed data types
  rows: Array<Array<any>>; // Equivalent to [[Schema.Types.Mixed]]
}

class TopicContent {
  @Prop()
  text?: string;

  @Prop({ type: TableData })
  table?: TableData;
}

class Remark {
  @Prop({ type: Types.ObjectId, ref: 'User' })
  userId: Types.ObjectId;

  @Prop()
  remark: string;

  @Prop({ default: Date.now })
  createdAt: Date;
}

@Schema({ timestamps: true, collection: 'fn_console_report'})
export class Report extends Document {
  @Prop({ required: true, enum: ['monthly', 'quarterly', 'fiscal-year'] })
  submissionFrequency: string;

  @Prop()
  period: {
    year: number;
    month?: number; // Optional for quarterly/fiscal-year
    quarter?: string; // Optional, e.g., "Q1"
  };

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  submittedBy: Types.ObjectId;

  @Prop()
  submittedAt: Date;

  @Prop({ enum: ['draft', 'submitted', 'under-review'], default: 'draft' })
  status: string;

  @Prop({ type: [Remark], default: [] })
  remarks: Remark[];

  @Prop({
    type: [
      {
        topicId: { type: Types.ObjectId, ref: 'Topic' },
        content: {
          text: { type: String },
          table: {
            headers: [String],
            rows: [[Object]], // Nested array for table rows
          },
        },
      },
    ],
    default: [],
  })
  topics: Array<{
    topicId: Types.ObjectId;
    content: TopicContent;
  }>;
}

export const ReportSchema = SchemaFactory.createForClass(Report);
