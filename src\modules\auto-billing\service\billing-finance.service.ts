// In src/billing/service/billing-finance.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { BillingFinance, BillingFinanceDocument } from '../schema/billing-finance.schema';
import { UserService } from 'src/modules/user/service/user.service';

@Injectable()
export class BillingFinanceService {
  constructor(
    @InjectModel(BillingFinance.name) private billingFinanceModel: Model<BillingFinanceDocument>,
    private userService: UserService,
  ) {}

  async createFinanceFlow(financeFlowData: any): Promise<BillingFinance> {
    // Generate a unique ID for the finance flow
    const financeFlowId = await this.generateFinanceFlowId(
      financeFlowData.department,
      financeFlowData.project
    );
    
    // Validate that each userId exists
    if (financeFlowData.approvers && Array.isArray(financeFlowData.approvers)) {
      for (const approver of financeFlowData.approvers) {
        // Ensure the userId is a valid ObjectId
        if (!(approver.userId instanceof Types.ObjectId) && !Types.ObjectId.isValid(approver.userId)) {
          throw new Error(`Invalid userId for approver at position ${approver.order}`);
        }
        
        // Check if user exists
        await this.userService.getUserById(approver.userId);
      }
    }
    
    const financeFlow = new this.billingFinanceModel({
      ...financeFlowData,
      financeFlowId,
    });

    return financeFlow.save();
  }

  async getFinanceFlow(projectId?: string | null, departmentId?: string): Promise<BillingFinance> {
    // Build the query
    const query: any = { isActive: true };
    
    if (projectId) {
      const projectObjectId = Types.ObjectId.isValid(projectId) ? 
        new Types.ObjectId(projectId) : projectId;
      query.project = projectObjectId;
    }
    
    if (departmentId) {
      const departmentObjectId = Types.ObjectId.isValid(departmentId) ? 
        new Types.ObjectId(departmentId) : departmentId;
      query.department = departmentObjectId;
    }
    
    // First try to find project-specific flow
    let financeFlow;
    
    if (projectId) {
      financeFlow = await this.billingFinanceModel.findOne({
        ...query,
        project: { $exists: true }
      }).populate('approvers.userId', '_id email fullName faceImage designation');
      
      if (financeFlow) {
        return financeFlow;
      }
    }
    
    // Next try department-specific flow
    if (departmentId) {
      financeFlow = await this.billingFinanceModel.findOne({
        isActive: true,
        project: { $exists: false },
        department: departmentId
      }).populate('approvers.userId');
      
      if (financeFlow) {
        return financeFlow;
      }
    }
    
    // Finally, try global flow (no project, no department)
    financeFlow = await this.billingFinanceModel.findOne({
      isActive: true,
      project: { $exists: false },
      department: { $exists: false }
    }).populate('approvers.userId');
    
    if (!financeFlow) {
      throw new NotFoundException('No finance approval flow found');
    }
    
    return financeFlow;
  }

  async getAllFinanceFlows(filters: any = {}): Promise<BillingFinance[]> {
    const queryFilter: any = {
      isActive: true
    };
    
    if (filters.project) {
      queryFilter.project = new Types.ObjectId(filters.project);
    }
    
    if (filters.department) {
      queryFilter.department = new Types.ObjectId(filters.department);
    }
    
    return this.billingFinanceModel.find(queryFilter)
      .populate('department')
      .populate('project')
      .populate('approvers.userId')
      .sort({ createdAt: -1 });
  }

  async updateFinanceFlow(financeFlowId: string, updateData: any): Promise<BillingFinance> {
    const financeFlow = await this.billingFinanceModel.findOne({ financeFlowId });
    
    if (!financeFlow) {
      throw new NotFoundException('Finance flow not found');
    }
    
    // Validate users if approvers are being updated
    if (updateData.approvers && Array.isArray(updateData.approvers)) {
      for (const approver of updateData.approvers) {
        if (approver.userId && !Types.ObjectId.isValid(approver.userId)) {
          throw new Error(`Invalid userId for approver`);
        }
        
        if (approver.userId) {
          await this.userService.getUserById(approver.userId);
        }
      }
    }
    
    Object.assign(financeFlow, {
      ...updateData,
      updatedAt: new Date(),
    });
    
    return financeFlow.save();
  }

  async deactivateFinanceFlow(financeFlowId: string): Promise<BillingFinance> {
    const financeFlow = await this.billingFinanceModel.findOne({ financeFlowId });
    
    if (!financeFlow) {
      throw new NotFoundException('Finance flow not found');
    }
    
    financeFlow.isActive = false;
    financeFlow.updatedAt = new Date();
    
    return financeFlow.save();
  }

  private async generateFinanceFlowId(departmentId?: string, projectId?: string): Promise<string> {
    let prefix = 'FINFLOW-GLOBAL';
    
    if (departmentId && projectId) {
      // Get department code and project code
      // Assuming you have a way to get these codes...
      // For now, I'll generate a generic ID
      prefix = 'FINFLOW-PROJ';
    } else if (departmentId) {
      prefix = 'FINFLOW-DEPT';
    }
    
    const count = await this.billingFinanceModel.countDocuments({
      financeFlowId: new RegExp(`^${prefix}`)
    });
    
    const sequence = (count + 1).toString().padStart(3, '0');
    return `${prefix}-${sequence}`;
  }
}