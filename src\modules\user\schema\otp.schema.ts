// src/otp/otp.schema.ts
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type OtpDocument = Otp & Document;

@Schema({ timestamps: true , collection: 'otp'})
export class Otp {
  @Prop({ required: true, unique: true })
  email: string;

  @Prop({ required: true })
  otp: string;

  @Prop({ default: false })
  isVerified: boolean;

  @Prop({ required: true })
  expiresAt: Date;

  @Prop({ default: 0 }) // Retry count
  retryCount: number;

  @Prop({ type: Date, default: null }) // Lock expiry time
  lockExpiresAt: Date | null;

}

export const OtpSchema = SchemaFactory.createForClass(Otp);
