# Database Schema Documentation

## 🗄️ Database Overview
- **Database Type**: MongoDB (NoSQL Document Database)
- **Production**: AWS DocumentDB (MongoDB-compatible)
- **Development**: Local MongoDB
- **ODM**: Mongoose v7.6.3

## 📋 Collections Overview

### Core Collections
1. **users** - User accounts and authentication
2. **departments** - Organizational departments
3. **billing_main** - Auto-billing records
4. **pa_main** - Payment advice records
5. **approval_master** - Approval workflow configurations
6. **rpa_schedule** - RPA task scheduling
7. **email_scheduler** - Email queue management

---

## 👥 Users Collection

**Collection**: `users`

```javascript
{
  _id: ObjectId,
  email: String, // Unique, required
  password: String, // Hashed, required
  fullName: String, // Required
  phone: String,
  designation: String,
  department: ObjectId, // Reference to departments
  role: String, // 'admin', 'user', 'manager'
  isActive: Boolean, // Default: true
  faceImage: String, // S3 URL for face recognition
  lastLogin: Date,
  createdAt: Date,
  updatedAt: Date
}
```

**Indexes:**
- `email` (unique)
- `department`
- `isActive`

---

## 🏢 Departments Collection

**Collection**: `departments`

```javascript
{
  _id: ObjectId,
  code: String, // Unique department code
  name: String, // Department name
  description: String,
  manager: ObjectId, // Reference to users
  isActive: Boolean, // Default: true
  createdAt: Date,
  updatedAt: Date
}
```

**Indexes:**
- `code` (unique)
- `isActive`

---

## 🧾 Billing Main Collection

**Collection**: `billing_main`

```javascript
{
  _id: ObjectId,
  billingId: String, // Unique billing identifier
  department: ObjectId, // Reference to departments
  project: ObjectId, // Reference to projects
  billingDate: Date,
  currency: String, // 'MYR', 'USD', etc.
  bankAccount: String,
  paymentTerms: String,
  
  // Line items array
  lineItems: [{
    product: ObjectId, // Reference to products
    description: String,
    unit: String,
    quantity: Number,
    price: Number,
    tax: Number,
    total: Number
  }],
  
  // File attachments
  attachments: [{
    fileName: String,
    fileType: String,
    fileUrl: String, // S3 URL
    description: String
  }],
  
  // Approval workflow
  approvals: [{
    userId: ObjectId, // Reference to users
    approverRole: String,
    status: String, // 'pending', 'approved', 'rejected'
    approvedAt: Date,
    comments: String
  }],
  
  currentApproverIndex: Number, // Default: 0
  isFinanceApprovalActive: Boolean, // Default: false
  status: String, // 'pending', 'ba_approved', 'finance_approved'
  
  // RPA-generated document IDs
  saleOrderId: String,
  deliveryOrderId: String,
  invoiceId: String,
  
  // RPA-generated file URLs
  saleOrderFile: String,
  deliveryOrderFile: String,
  invoiceFile: String,
  
  // RPA status flags
  isSOCreated: Boolean,
  SOCreatedAt: Date,
  isDOCreated: Boolean,
  DOCreatedAt: Date,
  isInvoiceCreated: Boolean,
  InvoiceCreatedAt: Date,
  isInvoicePosted: Boolean,
  InvoicePostedAt: Date,
  
  // Finance approvals (separate from regular approvals)
  financeApprovals: [{
    userId: ObjectId,
    approverRole: String,
    status: String,
    approvedAt: Date,
    comments: String
  }],
  
  financeApprovalStatus: String, // 'pending', 'approved', 'rejected'
  completedAt: Date,
  workflowStage: String, // 'billing-advice', 'sale-order', 'delivery-order', 'invoice', 'finance', 'collection'
  createdBy: ObjectId, // Reference to users
  createdAt: Date,
  updatedAt: Date
}
```

**Indexes:**
- `billingId` (unique)
- `department`
- `project`
- `status`
- `workflowStage`
- `createdBy`

---

## 💰 Payment Advice Main Collection

**Collection**: `pa_main`

```javascript
{
  _id: ObjectId,
  serialNumber: String, // Unique: "UZMA-EPA-000001"
  
  // Company information
  companyName: String,
  companyAddress: String,
  telNumber: String,
  faxNumber: String,
  companyLogo: String, // S3 URL
  
  // Department and vendor
  departmentCode: ObjectId, // Reference to departments
  vendorName: String,
  vendorAddress: String,
  eInvoiceCompliance: String,
  
  // Payment terms
  paymentTerm: {
    method: String, // 'bank_transfer', 'cheque', 'cash'
    bankName: String,
    accountNumber: String,
    accountName: String,
    swiftCode: String
  },
  
  currency: String,
  amount: Number, // Total payment amount
  
  // Line items
  lineItems: [{
    description: String,
    quantity: Number,
    unitPrice: Number,
    totalPrice: Number,
    taxAmount: Number,
    category: String
  }],
  
  // Bank receipts for payment tracking
  bankReceipts: [{
    receiptNumber: String,
    receiptDate: Date,
    amount: Number,
    bankName: String,
    accountNumber: String,
    s3Url: String, // Receipt file URL
    remark: String,
    isPartial: Boolean,
    balance: Number,
    createdAt: Date,
    updatedAt: Date
  }],
  
  // Payment status
  isPartialPayment: Boolean, // Default: false
  partialPaymentBalance: Number, // Default: 0
  
  // Workflow participants
  workflowParticipants: [{
    userId: ObjectId,
    role: String,
    order: Number,
    isActive: Boolean
  }],
  
  approvalWorkflowId: ObjectId, // Reference to approval workflows
  status: String, // 'submitted', 'approved', 'rejected', 'pending'
  dueDate: Date,
  submittedAt: Date,
  completedAt: Date,
  totalDuration: Number, // In hours
  createdBy: ObjectId, // Reference to users
  createdAt: Date,
  updatedAt: Date
}
```

**Indexes:**
- `serialNumber` (unique)
- `departmentCode`
- `status`
- `createdBy`
- `dueDate`

---

## ✅ Approval Master Collection

**Collection**: `approval_master`

```javascript
{
  _id: ObjectId,
  flowId: String, // Unique: "FLOW-DEP-PRJ-DOC-01"
  department: ObjectId, // Reference to departments
  project: ObjectId, // Reference to projects
  documentType: String, // 'billing', 'payment_advice', etc.
  
  // Approvers in sequence
  approvers: [{
    userId: ObjectId, // Reference to users
    role: String, // 'manager', 'director', 'finance'
    order: Number, // Sequence order
    isActive: Boolean // Default: true
  }],
  
  isActive: Boolean, // Default: true
  approvalType: String, // 'sequential', 'parallel'
  description: String,
  createdAt: Date,
  updatedAt: Date
}
```

**Indexes:**
- `flowId` (unique)
- `department`
- `project`
- `documentType`
- `isActive`

---

## 🤖 RPA Schedule Collection

**Collection**: `rpa_schedule`

```javascript
{
  _id: ObjectId,
  taskType: String, // 'sale_order', 'delivery_order', 'invoice', 'finance'
  billingId: String, // Reference to billing record
  billing: ObjectId, // Reference to billing_main
  status: String, // 'queued', 'in_progress', 'completed', 'failed', 'cancelled'
  priority: Number, // 1 = highest priority
  scheduledAt: Date,
  startedAt: Date,
  completedAt: Date,
  errorMessage: String,
  nextTaskId: ObjectId, // Reference to next RPA task
  additionalData: Object, // Flexible data storage
  createdAt: Date,
  updatedAt: Date
}
```

**Indexes:**
- `billingId`
- `status`
- `taskType`
- `scheduledAt`
- `priority`

---

## 📧 Email Scheduler Collection

**Collection**: `email_scheduler`

```javascript
{
  _id: ObjectId,
  to: String, // Recipient email
  cc: [String], // CC recipients
  bcc: [String], // BCC recipients
  subject: String,
  body: String, // HTML content
  templateName: String, // Email template used
  templateData: Object, // Template variables
  status: String, // 'pending', 'sent', 'failed'
  priority: Number, // 1 = highest
  scheduledAt: Date,
  sentAt: Date,
  errorMessage: String,
  retryCount: Number, // Default: 0
  maxRetries: Number, // Default: 3
  createdAt: Date,
  updatedAt: Date
}
```

**Indexes:**
- `status`
- `scheduledAt`
- `priority`
- `to`

---

## 🔗 Relationships

### Key Relationships
1. **Users** → **Departments** (Many-to-One)
2. **Billing** → **Departments** (Many-to-One)
3. **Billing** → **Projects** (Many-to-One)
4. **Billing** → **Users** (Many-to-One via createdBy)
5. **Approval Flows** → **Departments** + **Projects** (Many-to-One each)
6. **RPA Tasks** → **Billing** (Many-to-One)
7. **Payment Advice** → **Departments** (Many-to-One)

### Reference Patterns
```javascript
// Population example in Mongoose
const billing = await BillingModel.findById(id)
  .populate('department', 'code name')
  .populate('project', 'projectName')
  .populate('createdBy', 'fullName email')
  .populate('approvals.userId', 'fullName email');
```

---

## 🔍 Common Queries

### Find Pending Approvals for User
```javascript
db.billing_main.find({
  "approvals.userId": ObjectId("user_id"),
  "approvals.status": "pending"
});
```

### Find Billing by Department
```javascript
db.billing_main.find({
  "department": ObjectId("department_id"),
  "status": { $in: ["pending", "ba_approved"] }
});
```

### Find Overdue Payment Advice
```javascript
db.pa_main.find({
  "dueDate": { $lt: new Date() },
  "status": { $ne: "completed" }
});
```
