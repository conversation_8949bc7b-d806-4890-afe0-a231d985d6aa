// pa-message.controller.ts
import { Body, Controller, Get, Param, Post, Put, UseGuards, Request } from '@nestjs/common';
import { IPPAMessageService } from '../service/ippa-message.service';
import { CreateIPPAMessageDto } from '../dto/create-ippa-message.dto';

@Controller('ippa/messages')
export class IppaMessageController {
  constructor(private readonly messageService: IPPAMessageService) {}

  @Post()
  async create(@Body() createMessageDto: CreateIPPAMessageDto) {
    return this.messageService.create(createMessageDto);
  }

  @Get(':id')
  async findAllByIPPASubmissionId(@Param('id') id: string) {
    return this.messageService.findAllByIPPASubmissionId(id);
  }

  @Put(':id/read')
  async markAsRead(@Param('id') id: string, @Body() body) {
    return this.messageService.markAsRead(id, body.userId);
  }
  
  @Post('unread-count')
  async getUnreadCount(@Body() body) {
    return { count: await this.messageService.getUnreadMessageCount(body) };
  }
}