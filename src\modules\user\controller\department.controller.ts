import { Controller, Get, Param, Post, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { DepartmentService } from '../service/department.service';
import { Department } from '../schema/department.schema';

@Controller('departments')
export class DepartmentController {
  constructor(private readonly departmentService: DepartmentService) { }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    await this.departmentService.uploadDepartments(file.buffer);
    return { message: 'Departments uploaded successfully' };
  }

  // API to get department by code with populated manager, hod, and ceo
  @Get(':code')
  async getDepartmentByCode(@Param('code') code: string): Promise<any> {
    return this.departmentService.getDepartmentByCode(code);
  }

  @Post('ids')
  async getDepartmentIds(): Promise<string[]> {
    return this.departmentService.getAllDepartmentIds();
  }
}
