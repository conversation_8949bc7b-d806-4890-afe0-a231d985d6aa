import {
    Controller,
    Post,
    Get,
    Put,
    Delete,
    Param,
    Body,
    UseInterceptors,
    UploadedFile,
    BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { CreateSignatureDto, SignaturesService } from '../service/signature.service';

@Controller('e-sign/signatures')
export class SignaturesController {
    constructor(private signaturesService: SignaturesService) { }

    @Post()
    @UseInterceptors(FileInterceptor('file'))
    async createSignature(
        @UploadedFile() file: Express.Multer.File,
        @Body() createDto: CreateSignatureDto,
    ) {
        if (!file) {
            throw new BadRequestException('Signature image file is required');
        }

        return this.signaturesService.createSignature(file, createDto);
    }

    @Post('get')
    async getUserSignatures(@Body() body: any) {
        return this.signaturesService.getUserSignatures(body.userId);
    }

    @Get('active')
    async getActiveSignature(@Body() body: any) {
        return this.signaturesService.getActiveSignature(body.userId);
    }

    @Get(':id')
    async getSignature(@Param('id') id: string, @Body() body: any) {
        return this.signaturesService.getSignatureById(id, body.userId);
    }

    @Put(':id/activate')
    async setActiveSignature(@Param('id') id: string, @Body() body: any) {
        return this.signaturesService.setActiveSignature(id, body.userId);
    }

    @Put(':id')
    async updateSignature(
        @Param('id') id: string,
        @Body() updateDto: Partial<CreateSignatureDto>,
    ) {
        if (!updateDto.userId) {
            throw new BadRequestException('userId is required');
        }
        return this.signaturesService.updateSignature(id, updateDto.userId, updateDto);
    }

    @Put(':id/replace-image')
    @UseInterceptors(FileInterceptor('file'))
    async replaceSignatureImage(
        @Param('id') id: string,
        @UploadedFile() file: Express.Multer.File,
        @Body() body: any
    ) {
        if (!file) {
            throw new BadRequestException('Signature image file is required');
        }

        return this.signaturesService.replaceSignatureImage(id, body.userId, file);
    }

    @Delete(':id')
    async deleteSignature(@Param('id') id: string, @Body() body: any) {
        await this.signaturesService.deleteSignature(id, body.userId);
        return { message: 'Signature deleted successfully' };
    }
}