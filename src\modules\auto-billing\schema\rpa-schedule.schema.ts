import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type RpaScheduleDocument = RpaSchedule & Document;

export enum RpaTaskType {
  SALE_ORDER = 'sale_order',
  DELIVERY_ORDER = 'delivery_order',
  INVOICE = 'invoice', 
  FINANCE = 'finance',
}

export enum RpaTaskStatus {
  QUEUED = 'queued',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

@Schema({ timestamps: true, collection: 'rpa_schedule' })
export class RpaSchedule extends Document {
  @Prop({ required: true, enum: RpaTaskType })
  taskType: RpaTaskType;

  @Prop({ required: true })
  billingId: string;

  @Prop({ type: Types.ObjectId, ref: 'Billing', required: true })
  billing: Types.ObjectId;

  @Prop({ required: true, enum: RpaTaskStatus, default: RpaTaskStatus.QUEUED })
  status: RpaTaskStatus;

  @Prop()
  priority: number;

  @Prop()
  scheduledAt: Date;

  @Prop()
  startedAt: Date;

  @Prop()
  completedAt: Date;

  @Prop()
  errorMessage?: string;

  @Prop({ type: Types.ObjectId, ref: 'RpaSchedule' })
  nextTaskId?: Types.ObjectId;

  @Prop({ type: Object })
  additionalData?: Record<string, any>;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop()
  updatedAt: Date;
}

export const RpaScheduleSchema = SchemaFactory.createForClass(RpaSchedule);