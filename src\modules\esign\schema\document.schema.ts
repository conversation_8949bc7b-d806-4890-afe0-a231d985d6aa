import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ESignDocument = ESign & Document;

export enum DocumentStatus {
  PENDING = 'pending',
  SIGNED = 'signed',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export interface DocumentVersion {
  version: number;
  url: string;
  key: string;
  uploadedAt: Date;
  uploadedBy: Types.ObjectId;
  fileSize: number;
  notes?: string;
}

@Schema({ timestamps: true, collection: 'e_sign_main' })
export class ESign {

  @Prop({ required: true })
  serialNumber: string;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  filename: string;

  @Prop({ required: true })
  originalFilename: string;

  @Prop({ required: true })
  mimeType: string;

  @Prop({ required: true })
  fileSize: number;

  @Prop({ type: String, enum: ['sale-invoice'], default: 'medium' })
  documentType: string;

  // S3 URLs and keys for original document
  @Prop({ required: true })
  originalUrl: string;

  @Prop({ required: true })
  originalKey: string;

  // Current signed document (latest version)
  @Prop()
  signedUrl: string;

  @Prop()
  signedKey: string;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  uploadedBy: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Department', required: true })
  departmentId: Types.ObjectId;

  @Prop({ type: String, enum: DocumentStatus, default: DocumentStatus.PENDING })
  status: DocumentStatus;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  signedBy: Types.ObjectId;

  @Prop()
  signedAt: Date;

  @Prop()
  approvedAt: Date;

  @Prop()
  notes: string;

  // Current version numbers
  @Prop({ default: 1 })
  currentVersion: number;

  @Prop({ default: 0 })
  currentSignedVersion: number;

  // Priority level for processing
  @Prop({ type: String, enum: ['low', 'medium', 'high', 'urgent'], default: 'medium' })
  priority: string;

  // Due date for signing/approval
  @Prop()
  dueDate: Date;

  // Tags for better organization
  @Prop([String])
  tags: string[];
}

export const ESignSchema = SchemaFactory.createForClass(ESign);