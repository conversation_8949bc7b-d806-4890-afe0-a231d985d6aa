import { HttpException, HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CreateLoaDto } from '../dto/pa-loa.dto';
import { LOA, LoaDocument } from '../schema/pa-loa.schema';
import { SecurityService } from 'src/modules/user/service/security.service';

@Injectable()
export class LoaService {
    constructor(@InjectModel(LOA.name) private readonly loaModel: Model<LoaDocument>,
    private readonly securityService: SecurityService) { }

    async create(createLoaDto: CreateLoaDto): Promise<LOA> {

        // Convert the approvers from string IDs to ObjectIds

        if (createLoaDto.departmentId) {
            createLoaDto.departmentId = new Types.ObjectId(createLoaDto.departmentId);
        }

        if (createLoaDto.approvalLimits) {
            createLoaDto.approvalLimits.forEach(limit => {
            limit.approvers = limit.approvers.map(approver => new Types.ObjectId(approver));
            });
        }

        if (createLoaDto.preparers) {
            createLoaDto.preparers.forEach(preparer => {
               preparer = new Types.ObjectId(preparer);
            });
        }

        if (createLoaDto.finance) {
            createLoaDto.finance.forEach(fn => {
                fn = new Types.ObjectId(fn);
            });
        }

        // Create the new LOA document using the DTO
        const newLoa = new this.loaModel(createLoaDto);

        // Save and return the new LOA document
        return newLoa.save();
    }

    async findAll(): Promise<LOA[]> {
        return this.loaModel
            .find()
            .populate('departmentId')   // Populate department details
            .populate('preparers')      // Populate preparer details
            .populate('finance')        // Populate finance user details
            .populate('approvalLimits.approvers')  // Populate approver details
            .exec();
    }

    async findOne(id: string): Promise<any> {
        const depId = new Types.ObjectId(id);
    
        // Query for all LOA documents with the matching departmentId
        const loa = await this.loaModel.find({ departmentId: depId }).exec();
    
        // If no LOA documents are found, throw an exception
        if (!loa || loa.length === 0) {
            throw new NotFoundException('LOA configuration not found');
        }
    
        // Extract identifiers and filter for unique values
        const uniqueIdentifiers = [...new Set(loa.map(item => item.identifier))];
    
        return uniqueIdentifiers;
    }

    async update(id: string, updateLoaDto: Partial<CreateLoaDto>): Promise<LOA> {
        const updatedLoa = await this.loaModel.findByIdAndUpdate(id, updateLoaDto, { new: true }).exec();
        if (!updatedLoa) {
            throw new NotFoundException('LOA configuration not found');
        }
        return updatedLoa;
    }

    async delete(id: string): Promise<void> {
        const result = await this.loaModel.findByIdAndDelete(id).exec();
        if (!result) {
            throw new NotFoundException('LOA configuration not found');
        }
    }

    async applyAllHQ(): Promise<any> {
        return '';
    }

    async findApproversForAmount(departmentId: string, amount: number, currency: string, identifier: string): Promise<any> {

        const loa = await this.loaModel
        .findOne({ departmentId: new Types.ObjectId(departmentId), currency: currency.toUpperCase(), identifier: identifier })
        .populate('preparers')  // Populate preparers (userId array)
        .populate('finance')    // Populate finance (userId array)
        .populate({
            path: 'approvalLimits.approvers',  // Populate approvers inside each approvalLimit
            model: 'User',  // Specify the model explicitly if necessary
            select: 'name email fullName designation faceImage'  // Select the fields you need from the User model
        })
        .exec();

        // const loa1 = await this.loaModel
        // .findOne({ departmentId: new Types.ObjectId(departmentId), currency: currency.toUpperCase() })
        // .populate('finance')
        // .exec();

        if (!loa) {
            throw new NotFoundException('LOA configuration not found for the specified department');
        }

        // Find the appropriate approval limit for the given amount
        const approvalLimit = loa.approvalLimits.find(
            (limit) => amount >= limit.minLimit && (limit.maxLimit === null || amount <= limit.maxLimit)
        );

        if (!approvalLimit) {
            throw new NotFoundException('No approvers found for the specified amount');
        }

        var result = {
            type: loa.type,
            identifier: loa.identifier,
            approvalLimit: approvalLimit
        }
        return { data: this.securityService.encrypt(result) };
    }

    async updateFinanceArray(oldId: string, newId: string): Promise<any> {
        try {
          const updatedRecords = await this.loaModel.updateMany(
            { "finance.0": oldId },
            { $set: { "finance.0": newId } }
          );
    
          return updatedRecords;
        } catch (error) {
          throw new HttpException(
            'Error updating records',
            HttpStatus.INTERNAL_SERVER_ERROR
          );
        }
    }
}
