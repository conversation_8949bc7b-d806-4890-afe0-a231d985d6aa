import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type LoaDocument = LOA & Document;

@Schema()
export class ApprovalLimit {
  @Prop({ required: true, type: Number })
  minLimit: number;  // Minimum threshold, e.g., 0, 15001, 20001

  @Prop({ required: true, type: Number })
  maxLimit: number;  // Maximum threshold, e.g., 15000, 20000, Infinity (use `null` for no upper limit)

  @Prop({ required: true, type: [{ type: Types.ObjectId, ref: 'User' }] })
  approvers: Types.ObjectId[];  // Array of approver user IDs, referencing 'User'
}

@Schema({ collection: 'pa_loa' })
export class LOA {
  @Prop({ type: Types.ObjectId, ref: 'Department', required: true })
  departmentId: Types.ObjectId;  // Reference to the 'departments' collection

  @Prop({ required: true })
  loaType: string;  // E.g., FR, PR, PA

  @Prop({ required: true })
  currency: string;  // E.g., USD, MYR

  @Prop({ required: true, enum: ['hq', 'project', 'location'] })
  type: string; // Workflow type (e.g., 'hq', 'project', 'location')

  @Prop({ required: false })
  identifier: string; // Specific project or location identifier

  @Prop({ type: [{ type: Types.ObjectId, ref: 'User' }], required: true })
  preparers: Types.ObjectId[];  // Array of preparer user IDs, referencing 'User'

  @Prop({ type: [{ type: Types.ObjectId, ref: 'User' }], required: true })
  finance: Types.ObjectId[];  // Array of finance user IDs, referencing 'User'

  @Prop({ type: [ApprovalLimit], required: true })
  approvalLimits: ApprovalLimit[];  // Approval limits with thresholds and approvers
}

export const LOASchema = SchemaFactory.createForClass(LOA);
LOASchema.index({ departmentId: 1, type: 1, identifier: 1 }, { unique: true });