import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import * as moment from 'moment'; // Import moment to easily format dates
import { SecurityService } from 'src/modules/user/service/security.service';

@Injectable()
export class S3Service {

  constructor(private readonly securityService: SecurityService) {
    
  }

  private s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_DEFAULT_REGION,
  });

  async uploadFile(file: Express.Multer.File): Promise<string> {
    // Generate the current month in the format `october-2024`
    const currentMonth = moment().format('MMMM-YYYY').toLowerCase();

    const params: any = {
      Bucket: process.env.AWS_S3_SEAH, // Bucket name
      Key: `seah/reports/${currentMonth}/${Date.now()}-${file.originalname}`, // Folder path: seah/reports/october-2024/filename
      Body: file.buffer,
      ContentType: file.mimetype,
    };

    const result = await this.s3.upload(params).promise();
    return result.Location; // Return the S3 file URL
  }

  async uploadRPAFile(file: Express.Multer.File): Promise<string> {
    // Generate the current month in the format `october-2024`
    const currentMonth = moment().format('MMMM-YYYY').toLowerCase();

    const params: any = {
      Bucket: process.env.AWS_S3_SEAH, // Bucket name
      Key: `seah/rpa/${currentMonth}/${Date.now()}-${file.originalname}`, // Folder path: seah/reports/october-2024/filename
      Body: file.buffer,
      ContentType: file.mimetype,
    };

    const result = await this.s3.upload(params).promise();
    return result.Location; // Return the S3 file URL
  }

  async uploadAutoBilling(file: Express.Multer.File): Promise<string> {
    // Generate the current month in the format `october-2024`
    const currentMonth = moment().format('MMMM-YYYY').toLowerCase();

    const params : any = {
      Bucket: process.env.AWS_S3_SEAH, // Bucket name
      Key: `auto-billing/rpa/${currentMonth}/${Date.now()}-${file.originalname}`, // Folder path: seah/reports/october-2024/filename
      Body: file.buffer,
      ContentType: file.mimetype,
    };

    const result = await this.s3.upload(params).promise();
    return result.Location; // Return the S3 file URL
  }

  async uploadESign(file: Express.Multer.File): Promise<any> {

    const params : any  = {
      Bucket: process.env.AWS_S3_SEAH, // Bucket name
      Key: `e-sign/signature/${Date.now()}-${file.originalname}`, // Folder path: seah/reports/october-2024/filename
      Body: file.buffer,
      ContentType: file.mimetype,
    };

    const result = await this.s3.upload(params).promise();
    return {key: params.Key, value: result.Location}; // Return the S3 file URL
  }

  async deleteESign(key: any): Promise<any> {

    const params : any = {
      Bucket: process.env.AWS_S3_SEAH,
      Key: key,
    };

    try {
      await this.s3.deleteObject(params).promise();
      console.log(`Deleted: ${key}`);
    } catch (error) {
      console.error(`Error deleting S3 object: ${key}`, error);
      throw error;
    }
  }

  async uploadESignDoc(file: Express.Multer.File): Promise<any> {
    // Generate the current month in the format `october-2024`
    const currentMonth = moment().format('MMMM-YYYY').toLowerCase();

    const params : any = {
      Bucket: process.env.AWS_S3_SEAH, // Bucket name
      Key: `e-sign/document/${currentMonth}/${Date.now()}-${file.originalname}`, // Folder path: seah/reports/october-2024/filename
      Body: file.buffer,
      ContentType: file.mimetype,
    };

    const result = await this.s3.upload(params).promise();
    return {key: params.Key, value: result.Location};// Return the S3 file URL
  }

  async uploadPaymentAdviceAttachment(file: Express.Multer.File): Promise<any> {
    // Generate the current month in the format `october-2024`
    const currentMonth = moment().format('MMMM-YYYY').toLowerCase();

    const params: any  = {
      Bucket: process.env.AWS_S3_SEAH, // Bucket name
      Key: `payment-advice/attachments/${currentMonth}/${Date.now()}-${file.originalname}`, // Folder path: seah/reports/october-2024/filename
      Body: file.buffer,
      ContentType: file.mimetype,
    };

    const result = await this.s3.upload(params).promise();
    // return result.Location; // Return the S3 file URL
    return { data: this.securityService.encrypt(result.Location)};
  }
}
