import { IsBoolean, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON>Optional, IsString } from 'class-validator';

export class CreateSageReportRPADto {
  @IsString()
  @IsNotEmpty()
  type: string;

  @IsString()
  @IsOptional()
  remark?: string;

  @IsString()
  @IsNotEmpty()
  totalRecord: string;

  @IsNumber()
  @IsNotEmpty()
  totalAmount: number;

  @IsBoolean()
  @IsOptional()
  isPosted?: boolean;

  @IsOptional()
  postedDateTime?: Date;

  @IsBoolean()
  @IsOptional()
  isSuccess?: boolean;
}