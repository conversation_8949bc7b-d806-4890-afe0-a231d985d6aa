import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type PaymentDocument = Payment & Document;

@Schema()
export class Payment {
  // Make this field unique to avoid duplicates
  @Prop()
  PVNo: string;

  @Prop()
  date: string;

  @Prop()
  BP: string;

  @Prop()
  BPName: string;

  @Prop()
  country: string;

  @Prop()
  controlAccount: string;

  @Prop()
  bank: string;

  @Prop()
  description: string;

  @Prop()
  referenceNo: string;

  @Prop()
  invoiceNo: string;

  @Prop()
  department: string;

  @Prop()
  project: string;

  @Prop()
  currency: string;

  @Prop()
  docAmount: number;

  @Prop()
  rate: string;

  @Prop()
  converted: number;

  @Prop()
  status: string;

  @Prop()
  PIC: string;

  @Prop()
  isExcluded: boolean;

  @Prop()
  remark: string;
}

export const PaymentSchema = SchemaFactory.createForClass(Payment);
