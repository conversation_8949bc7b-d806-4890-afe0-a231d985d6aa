import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { Department } from './department.schema'; // Import the Department schema

export type UserDocument = User & Document;

@Schema({ timestamps: true, collection: 'user' })
export class User {

  // Add the _id field explicitly to the User class
  _id: Types.ObjectId;

  @Prop({ required: true })
  username: string;

  @Prop({ required: true })
  fullName: string;

  @Prop({ required: true, unique: true })
  email: string;  // Email of the user

  @Prop({ required: true })
  faceId: string;  // Used for AWS face login

  @Prop({ required: true })
  faceImage: string;  // Link to face image stored in AWS

  @Prop({ required: true })
  department: string;  // User's primary department (optional)

  @Prop({ required: true })
  designation: string;  // User's designation (e.g., Manager, Engineer)

  @Prop({ enum: ['face', 'password'], required: true })
  loginMethod: string;  // Method used for login (face or password)

  @Prop({ type: Types.ObjectId, ref: 'Role', required: true })
  role: Types.ObjectId;  // Reference to the user's role

  @Prop({ type: Types.ObjectId, ref: 'Role', required: true })
  roles: Types.ObjectId[];  // Reference to the user's role

  @Prop({ type: Types.ObjectId, ref: 'User' })
  createdBy: Types.ObjectId;  // Reference to the user who created this user

  // New field for multiple departments
  @Prop({ type: [{ type: Types.ObjectId, ref: 'Department' }] })
  departments: Types.ObjectId[];  // Array of department references
}

export const UserSchema = SchemaFactory.createForClass(User);
