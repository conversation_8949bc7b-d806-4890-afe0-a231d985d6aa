import { IsArray, IsNotEmpty, IsOptional, IsString, IsMongoId, IsNumber, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

class VerifiedByDTO {
  @IsOptional()
  name: string;

  @IsOptional()
  date: Date;
}

class WithholdingTaxDTO {
  @IsNotEmpty()
  percentage: string;

  @IsNotEmpty()
  trfAttachment: { s3Url: string };

  @IsOptional()
  @Type(() => VerifiedByDTO)
  verifiedBy: VerifiedByDTO;
}

export class CreatePaymentAdviceDto {
  @IsString()
  @IsNotEmpty()
  companyName: string;

  @IsString()
  @IsNotEmpty()
  vendorName: string;

  @IsMongoId()
  @IsNotEmpty()
  departmentCode: string;  // Refers to the Department collection

  @IsOptional()
  paymentTerm: {
    days: number;
    termType: string;
  };

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkflowParticipantDTO)
  workflowParticipants: WorkflowParticipantDTO[];

  @IsOptional()
  prDoc: {
    type: string;
    s3Url: string;
  };

  @IsOptional()
  poDoc: {
    type: string;
    s3Url: string;
  };

  @IsOptional()
  grnDoc: {
    type: string;
    s3Url: string;
  };

  @IsArray()
  documents: Array<{
    invoiceNumber: string;
    invoiceDate: Date;
    poNumber: string;
    projectCode: string;
    amount: number;
    attachments: Array<{
      type: string;
      s3Url: string;
    }>;
  }>;

  @IsString()
  detailsPayment: string;

  @IsString()
  serviceLocation: string;

  @IsString()
  staffConsultant: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => WithholdingTaxDTO)
  withholdingTax?: WithholdingTaxDTO;

  @IsOptional()
  chargeBacks: Array<{
    glCode: string;
    amount: number;
  }>;

  @IsString()
  currency: string;

  @IsString()
  status: string;

  @IsString()
  identifier: string;

  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @IsMongoId()
  @IsNotEmpty()
  createdBy: string;
}

export class WorkflowParticipantDTO {
  @IsMongoId()
  @IsNotEmpty()
  userId: string;  // ID of the participant (approver/reviewer)

  @IsString()
  @IsNotEmpty()
  approvalType: string;  // 'Approved By' or 'Reviewed By'
}
