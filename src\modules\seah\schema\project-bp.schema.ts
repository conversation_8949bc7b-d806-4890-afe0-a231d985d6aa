import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ProjectBpDocument = ProjectBP & Document;

@Schema()
export class ProjectBP {
  // Make this field unique to avoid duplicates
  @Prop()
  bpName: string;

  @Prop()
  totalPO: number;

  @Prop()
  totalPayment: number;

  @Prop()
  outstandingAmount: number;

  @Prop()
  discountAmount: number;

  @Prop()
  status: string; // ongoing, completed, cancelled

  @Prop()
  remark: string;

  @Prop({attachments: [
    {
      remark: { type: String },
      type: { type: String },
      s3Url: { type: String },
    }
  ]})
    attachments: Array<{
      remark: string;
      type: string;
      s3Url: string;
    }>;
}

export const ProjectBPSchema = SchemaFactory.createForClass(ProjectBP);
