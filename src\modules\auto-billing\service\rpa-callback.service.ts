import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Billing, BillingDocument } from '../schema/billing.schema';
import { RpaSchedule, RpaScheduleDocument, RpaTaskStatus, RpaTaskType } from '../schema/rpa-schedule.schema';
import { RpaLog, RpaLogDocument } from '../schema/rpa-logs.schema';
import { RpaSchedulerService } from './rpa-schedule.service';
import { BillingService } from './billing.service';
import { User, UserDocument } from 'src/modules/user/schema/user.schema';
import { S3Service } from 'src/modules/common/services/s3.service';
import { EmailScheduler, EmailSchedulerDocument } from 'src/modules/common/schema/pa-email.schema';

@Injectable()
export class RpaCallbackService {
  private readonly logger = new Logger(RpaCallbackService.name);

  constructor(
    @InjectModel(Billing.name) private billingModel: Model<BillingDocument>,
    @InjectModel(RpaSchedule.name) private rpaScheduleModel: Model<RpaScheduleDocument>,
    @InjectModel(RpaLog.name) private rpaLogModel: Model<RpaLogDocument>,
    @InjectModel(EmailScheduler.name) private emailSchedulerModel: Model<EmailSchedulerDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private readonly s3service: S3Service,
    private readonly rpaSchedulerService: RpaSchedulerService,
    private readonly billingService: BillingService
  ) { }

  /**
   * Process a document creation callback from the RPA system
   */
  async processDocumentCallback(callbackData: any, file: Express.Multer.File | null): Promise<any> {
    try {
      this.logger.log(`Processing RPA callback for task ${callbackData.taskId}`);

      if (file) {
        // Upload the file to S3
        const fileUrl = await this.s3service.uploadAutoBilling(file);
        callbackData.fileUrl = fileUrl;
      }

      // Validate required fields
      if (!callbackData.taskId) {
        throw new Error('Missing required field: taskId in callback data');
      }

      // Find the task associated with this callback
      const taskId = new Types.ObjectId(callbackData.taskId);
      const scheduledTask = await this.rpaScheduleModel.findById(taskId);

      if (!scheduledTask) {
        throw new NotFoundException(`Task with ID ${callbackData.taskId} not found`);
      }

      this.logger.log(`Found task ${scheduledTask._id} (${scheduledTask.taskType}) for billing ${scheduledTask.billingId}`);

      // Get the billing document
      const billing = await this.billingModel.findOne({ _id: scheduledTask.billingId });
      if (!billing) {
        throw new NotFoundException(`Billing with ID ${scheduledTask.billingId} not found`);
      }

      // Get the task type
      const taskType = scheduledTask.taskType as RpaTaskType;

      // Check for errors in the callback
      const isSuccessful = callbackData.taskId ? true : false;

      if (isSuccessful) {
        // Update billing document with the result
        await this.updateBillingDocument(billing, taskType, callbackData);

        // Update task status to completed
        await this.updateTaskStatus(scheduledTask, true, callbackData.error);

        // Create log entry
        await this.createOrUpdateLogEntry(taskType, callbackData, scheduledTask._id, true);

        // Schedule the next task in the sequence ONLY if this one was successful
        await this.rpaSchedulerService.scheduleNextTaskAfterCallback(scheduledTask.billingId, taskType);

        this.logger.log(`Successfully processed callback for ${taskType} on billing ${scheduledTask.billingId}`);
      } else {
        // Handle error case
        this.logger.error(`Error in RPA callback for ${taskType}: ${callbackData.error || 'Unknown error'}`);

        // Update task status to failed
        await this.updateTaskStatus(scheduledTask, false, callbackData.error || 'Failed callback');

        // Create error log entry
        await this.createOrUpdateLogEntry(taskType, callbackData, scheduledTask._id, false);
      }

      return {
        billingId: scheduledTask.billingId,
        documentType: scheduledTask.taskType,
        status: isSuccessful ? 'success' : 'failed',
        message: isSuccessful ? 'Document processed successfully' : `Error: ${callbackData.error || 'Unknown error'}`
      };
    } catch (error) {
      this.logger.error(`Error processing callback: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update the billing document with the result information
   */
  private async updateBillingDocument(billing: BillingDocument, taskType: RpaTaskType, callbackData: any): Promise<void> {

    const _id = billing.createdBy;
    const preparer = await this.userModel.findOne({ _id }).populate('_id email fullName faceImage designation');

    if(!preparer) {
      throw new NotFoundException("Prepare not found")
    }

    switch (taskType) {
      case RpaTaskType.SALE_ORDER:
        billing.saleOrderId = callbackData.documentId;
        billing.saleOrderFile = callbackData.fileUrl || billing.saleOrderFile;
        billing.isSOCreated = true;
        billing.SOCreatedAt = new Date();
        billing.workflowStage = 'delivery-order';

        const emailSchedulerSO = new this.emailSchedulerModel({
          to: preparer.email,
          subject: `Sale Order has been created : Billing ID - ${billing.billingId}`,
          body: `<p>Dear ${preparer.fullName}, <br/>
          Sale Order for billing Id ${billing.billingId} has been created succesfully.<br/><br/>
          Sale Order Number - ${billing.saleOrderId}<br/><br/>
          Sale Order Document - <a href="${billing.saleOrderFile}" target="_blank">View Sale Order</a><br/><br/>
          Thank you.<br/><br/>
          Best Regards,<br/>
          UZMA ECO SYSTEM</p>`,
        });
        await emailSchedulerSO.save();
        break;

      case RpaTaskType.DELIVERY_ORDER:
        billing.deliveryOrderId = callbackData.documentId;
        billing.deliveryOrderFile = callbackData.fileUrl || billing.deliveryOrderFile;
        billing.isDOCreated = true;
        billing.DOCreatedAt = new Date();
        billing.workflowStage = 'invoice';

        const emailSchedulerDO = new this.emailSchedulerModel({
          to: preparer.email,
          subject: `Delivery Order has been created : Billing ID - ${billing.billingId}`,
          body: `<p>Dear ${preparer.fullName}, <br/>Delivery Order for billing Id ${billing.billingId} has been created succesfully.<br/><br/>
          Delivery Order Number - ${billing.deliveryOrderId}<br/><br/>
          Delivery Order Document - <a href="${billing.deliveryOrderFile}" target="_blank">View JCT</a>
          <br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
        });
        await emailSchedulerDO.save();
        break;

      case RpaTaskType.INVOICE:
        billing.invoiceId = callbackData.documentId;
        billing.invoiceFile = callbackData.fileUrl || billing.invoiceFile;
        billing.isInvoiceCreated = true;
        billing.InvoiceCreatedAt = new Date();
        billing.isFinanceApprovalActive = true;
        billing.currentApproverIndex = 0
        billing.workflowStage = 'finance';
        // Add finance approvers after invoice is created
        await this.billingService.addFinanceApprovers(billing);

        const emailSchedulerInvoice = new this.emailSchedulerModel({
          to: preparer.email,
          subject: `Invoice reviewing by Finance: Billing ID - ${billing.billingId}`,
          body: `<p>Dear ${preparer.fullName}, <br/>Invoice for billing Id ${billing.billingId} has been prepared and submitted for finance review.<br/><br/>
          Invoice Number - ${billing.invoiceId}<br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
        });
        await emailSchedulerInvoice.save();
        break;

      case RpaTaskType.FINANCE:
        billing.isInvoicePosted = true;
        billing.InvoicePostedAt = new Date();
        billing.status = 'completed';
        break;
    }

    // Add any additional data from the callback
    if (callbackData.additionalInfo) {
      // Add to billing metadata or other fields as needed
    }

    await billing.save();
    this.logger.log(`Updated billing ${billing.billingId} with ${taskType} document: ${callbackData.documentId}`);
  }

  /**
   * Update the task status based on the callback result
   */
  private async updateTaskStatus(task: RpaScheduleDocument, isSuccessful: boolean, errorMessage?: string): Promise<void> {
    if (isSuccessful) {
      task.status = RpaTaskStatus.COMPLETED;
      task.completedAt = new Date();
    } else {
      task.status = RpaTaskStatus.FAILED;
      task.errorMessage = errorMessage || 'Unknown error';
    }

    await task.save();
    this.logger.log(`Updated task ${task._id} status to ${task.status}`);
  }

  /**
   * Create or update a log entry for this callback
   */
  private async createOrUpdateLogEntry(
    taskType: RpaTaskType,
    callbackData: any,
    taskId: any,
    isSuccessful: boolean
  ): Promise<void> {
    // Try to find an existing log for this task
    let log = await this.rpaLogModel.findOne({ taskId });

    if (!log) {
      // Create a new log if none exists
      log = new this.rpaLogModel({
        taskId,
        taskType,
        billingId: callbackData.billingId,
        status: isSuccessful ? RpaTaskStatus.COMPLETED : RpaTaskStatus.FAILED,
        documentId: callbackData.documentId,
        fileUrl: callbackData.fileUrl,
        response: callbackData,
        errorMessage: isSuccessful ? null : (callbackData.error || 'Unknown error')
      });
    } else {
      // Update existing log
      log.status = isSuccessful ? RpaTaskStatus.COMPLETED : RpaTaskStatus.FAILED;
      log.documentId = callbackData.documentId;
      log.fileUrl = callbackData.fileUrl;
      log.response = callbackData;
      log.errorMessage = isSuccessful ? null : (callbackData.error || 'Unknown error');
      log.completedAt = new Date();
    }

    await log.save();
    this.logger.log(`Saved log entry for ${taskType} callback on billing ${callbackData.billingId}`);
  }
}