import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';

@Injectable()
export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    // Configure the transporter
    this.transporter = nodemailer.createTransport({
      host: 'smtp-mail.outlook.com', // SMTP server host
      port: 587,
      auth: {
        user: '<EMAIL>', // SMTP username
        pass: 'qssjrsmfvxwhdkcz ',    // SMTP password
      },
    });
  }

  async sendMail(to: string, subject: string, html?: string): Promise<void> {
    try {
      const mailOptions = {
        from: '"UZMA ECO SYSTEM" <<EMAIL>>', // sender address
        to,                                         // recipient address
        subject,                                    // Subject line
        html,                                       // HTML body (optional)
      };

      await this.transporter.sendMail(mailOptions);
      console.log(`Email sent successfully to ${to}`);
    } catch (error) {
      console.error(`Failed to send email: ${error.message}`);
      throw new Error('Email sending failed');
    }
  }
}
