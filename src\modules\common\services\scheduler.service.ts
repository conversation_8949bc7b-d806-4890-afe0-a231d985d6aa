import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import { RPAApiService } from 'src/modules/seah/service/rpa-api.service';

@Injectable()
export class SchedulerService {
  private readonly logger = new Logger(SchedulerService.name);
  private readonly rpaScheduleEnabled: boolean;
  private readonly rpaScheduleTime: string;

  constructor(
    private readonly configService: ConfigService,
  ) {
    // Check if scheduling is enabled
    this.rpaScheduleEnabled = this.configService.get<boolean>('RPA_SCHEDULE_ENABLED', true);
    
    // Get custom schedule time or use default (3:00 AM)
    this.rpaScheduleTime = this.configService.get<string>('RPA_SCHEDULE_TIME', '* * * * *');
    
    this.logger.log(`RPA scheduling is ${this.rpaScheduleEnabled ? 'enabled' : 'disabled'}`);   
    console.log(`RPA scheduling is ${this.rpaScheduleEnabled? 'enabled' : 'disabled'}`);
    if (this.rpaScheduleEnabled) {
      this.logger.log(`RPA scheduled to run at: ${this.rpaScheduleTime}`);
    }
  }

  // Run daily at the configured time (default: 3:00 AM)
  @Cron(CronExpression.EVERY_HOUR, {
    name: 'rpa-daily-schedule',
  })
  async handleDailyRPACall() {
    if (!this.rpaScheduleEnabled) {
      this.logger.log('RPA scheduling is disabled. Skipping scheduled run.');
      return;
    }

    this.logger.log('Starting scheduled RPA API call...');
    // try {
    //   await this.rpaApiService.runScheduledRPAApiCall('YLIST_PV_01');
    //   await this.rpaApiService.runScheduledRPAApiCall('YLIST_PO_01');
    //   this.logger.log('Scheduled RPA API call completed successfully');
    // } catch (error) {
    //   this.logger.error(`Error in scheduled RPA API call: ${error.message}`, error.stack);
    // }
  }

  // You can add additional scheduled tasks here as needed
}