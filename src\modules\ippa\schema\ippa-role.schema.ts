import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export enum IPPAUserRoleEnum {
  EMPLOYEE = 'employee',
  SUPERVISOR = 'supervisor',
  HR = 'hr',
  HOD = 'hod',
  ADMIN = 'admin'
}

@Schema({ timestamps: true, collection: 'ippa_user_roles' })
export class IPPAUserRole {
  _id: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true, index: true })
  userId: Types.ObjectId;

  @Prop({ type: [String], enum: IPPAUserRoleEnum, required: true })
  roles: IPPAUserRole[];

  // Department access for HODs
  @Prop({ type: [String] })
  departmentAccess: string[];

  // Supervisor scope
  @Prop({ type: [Types.ObjectId], ref: 'IppaUser' })
  supervisedEmployees: Types.ObjectId[];

  @Prop({ required: true, index: true })
  financialYear: string;

  @Prop({ default: true })
  isActive: boolean;
}

export type IPPAUserRoleDocument = IPPAUserRole & Document;
export const IPPAUserRoleSchema = SchemaFactory.createForClass(IPPAUserRole);