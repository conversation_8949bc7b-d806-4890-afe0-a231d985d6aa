import { Controller, Get, Post } from '@nestjs/common';
import { TestsService } from './test.service';

@Controller('api/v1/tests')
export class TestsController {
  constructor(private readonly usersService: TestsService) {}

  @Get('profile')
  getProfile() {
    return this.usersService.getProfile();
  }

  @Get()
  findAll() {
    return this.usersService.findAll();
  }

  @Get('stats')
  getStats() {
    return this.usersService.getStats();
  }

  @Post('sample')
  createSample() {
    return this.usersService.createSampleUser();
  }
}