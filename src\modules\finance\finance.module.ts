import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { FinanceBGLog, FinanceBGLogSchema } from './schema/finance_bg_logs';
import { FinanceBG, FinanceBGchema } from './schema/finance_bg';
import { PJMAccrual, PJMAccrualSchema } from './schema/finance_pjm_accrual';
import { PJM, PJMSchema } from './schema/finance_pjm';
import { YGL01, YGL01Schema } from './schema/ygl_01.schema';
import { FinanceBGController } from './controller/finance_bg.controller';
import { PJMController } from './controller/finance_pjm.controller';
import { PJMAccrualController } from './controller/finance_pjm_accrual.controller';
import { YGL01Controller } from './controller/ygl_01.controller';
import { FinanceBGService } from './service/finance_bg.service';
import { PJMAccrualService } from './service/finance_pjm_accrual.service';
import { PJMService } from './service/finance_pjm.service';
import { YGL01Service } from './service/ygl_01.service';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: FinanceBGLog.name, schema: FinanceBGLogSchema },
      { name: FinanceBG.name, schema: FinanceBGchema },
      { name: PJMAccrual.name, schema: PJMAccrualSchema },
      { name: PJM.name, schema: PJMSchema },
      { name: YGL01.name, schema: YGL01Schema },
    ]),
    CommonModule
  ],
  controllers: [FinanceBGController, PJMAccrualController, PJMController, YGL01Controller],
  providers: [FinanceBGService, PJMAccrualService, PJMService, YGL01Service],
  exports: [],
})
export class FinanceModule {}