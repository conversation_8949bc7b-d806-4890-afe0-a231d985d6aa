import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { RpaTaskType, RpaTaskStatus } from './rpa-schedule.schema';

export type RpaLogDocument = RpaLog & Document;

@Schema({ timestamps: true, collection: 'rpa_logs' })
export class RpaLog extends Document {
  @Prop({ type: Types.ObjectId, ref: 'RpaSchedule', required: true })
  taskId: Types.ObjectId;
  
  @Prop({ required: true, enum: RpaTaskType })
  taskType: RpaTaskType;

  @Prop({ required: true })
  billingId: string;

  @Prop({ type: Types.ObjectId, ref: 'Billing', required: true })
  billing: Types.ObjectId;

  @Prop({ required: true, enum: RpaTaskStatus })
  status: RpaTaskStatus;

  @Prop({ type: Object })
  request: Record<string, any>;

  @Prop({ type: Object })
  response: Record<string, any>;

  @Prop()
  endpoint: string;

  @Prop()
  duration: number; // in milliseconds

  @Prop()
  errorMessage?: string;

  @Prop()
  documentId?: string;

  @Prop()
  fileUrl?: string;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop()
  completedAt: Date;
}

export const RpaLogSchema = SchemaFactory.createForClass(RpaLog);