import { Body, Controller, Post, Get, FileTypeValidator, MaxFileSizeValidator, Param, ParseFilePipe, UploadedFile, UseInterceptors, Query } from '@nestjs/common';
import { RPAApiService } from '../service/rpa-api.service';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('rpa')
export class RPAApiController {
  constructor(private readonly rpaApiService: RPAApiService) {}

  @Post('call')
  async callRPAApi(@Body() rpaRequestDto: any) {
    return this.rpaApiService.callRPAApi(rpaRequestDto);
  }

  @Post('trigger-manual')
  async triggerManualRun(@Body('reportCode') reportCode?: string,) {
    await this.rpaApiService.runScheduledRPAApiCall(reportCode);
    // return {
    //   success: true,
    //   message: 'Manual RPA API call triggered successfully',
    // };
  }

  @Post('webhook/:requestId')
  @UseInterceptors(FileInterceptor('file'))
  async receiveRPAFile(
    @Param('requestId') requestId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 }), // 10MB max
          // new FileTypeValidator({ fileType: '.(xlsx|xls)' }),
        ],
      }),
    ) file: Express.Multer.File,
  ) {
    return this.rpaApiService.processRPAFile(requestId, file);
  }

  @Get('webhook/history')
  async getWebhookHistory(
    @Query('limit') limit: number = 10,
    @Query('skip') skip: number = 0,
    @Query('hasError') hasError?: boolean,
  ) {
    return this.rpaApiService.getWebhookHistory(limit, skip, hasError);
  }
  
  @Get('webhook/:requestId')
  async getWebhookByRequestId(@Param('requestId') requestId: string) {
    return this.rpaApiService.getWebhookByRequestId(requestId);
  }
}