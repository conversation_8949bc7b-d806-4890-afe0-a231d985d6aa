import { Controller, Get, Post, Body, Param, Delete, Query } from '@nestjs/common';
import { RpaSchedule } from '../schema/rpa-schedule.schema';
import { RpaTaskType } from '../schema/rpa-schedule.schema';
import { RpaLog } from '../schema/rpa-logs.schema';
import { RpaSchedulerService } from '../service/rpa-schedule.service';

@Controller('rpa-scheduler')
export class RpaSchedulerController {
  constructor(private readonly rpaSchedulerService: RpaSchedulerService) {}

  @Post('schedule')
  async scheduleTask(@Body() body: { 
    taskType: RpaTaskType; 
    billingId: string; 
    priority?: number;
    scheduledAt?: Date;
    additionalData?: any;
  }): Promise<RpaSchedule> {
    return this.rpaSchedulerService.scheduleTask(
      body.taskType,
      body.billingId,
      body.priority,
      body.scheduledAt ? new Date(body.scheduledAt) : undefined,
      body.additionalData
    );
  }

  @Post('schedule-sequence/:billingId')
  async scheduleSequentialTasks(@Param('billingId') billingId: string): Promise<{ success: boolean; message: string }> {
    await this.rpaSchedulerService.initiateGenerateSaleOrder(billingId);
    return { 
      success: true, 
      message: `Sequential tasks scheduled for billing ${billingId}` 
    };
  }

  @Get('tasks/billing/:billingId')
  async getTasksByBillingId(@Param('billingId') billingId: string): Promise<RpaSchedule[]> {
    return this.rpaSchedulerService.getTasksByBillingId(billingId);
  }

  @Get('logs/billing/:billingId')
  async getLogsByBillingId(@Param('billingId') billingId: string): Promise<RpaLog[]> {
    return this.rpaSchedulerService.getLogsByBillingId(billingId);
  }

  @Get('task/:taskId')
  async getTaskStatus(@Param('taskId') taskId: string): Promise<any> {
    return this.rpaSchedulerService.getTaskStatus(taskId);
  }

  @Delete('task/:taskId')
  async cancelTask(@Param('taskId') taskId: string): Promise<RpaSchedule> {
    return this.rpaSchedulerService.cancelTask(taskId);
  }
}