import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PJM, PJMDocument } from '../schema/finance_pjm';

@Injectable()
export class PJMService {
  constructor(@InjectModel(PJM.name) private readonly pjmModel: Model<PJMDocument>) {}

  async create(pjm: PJM): Promise<PJM> {
    const createdPJM = new this.pjmModel(pjm);
    return createdPJM.save();
  }

  async findAll(): Promise<PJM[]> {
    return this.pjmModel.find().exec();
  }

  async findOne(id: string): Promise<PJM | null> {
    return this.pjmModel.findById(id).exec();
  }

  async update(id: string, updateData: Partial<PJM>): Promise<PJM | null> {
    return this.pjmModel.findByIdAndUpdate(id, updateData, { new: true }).exec();
  }

  async delete(id: string): Promise<PJM | null> {
    return this.pjmModel.findByIdAndDelete(id).exec();
  }
}
