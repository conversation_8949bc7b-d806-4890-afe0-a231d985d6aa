import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Billing, BillingDocument } from '../schema/billing.schema';
import { RpaTaskType } from '../schema/rpa-schedule.schema';
import { RpaSchedulerService } from './rpa-schedule.service';
import { BillingFinanceService } from './billing-finance.service';
import { User, UserDocument } from 'src/modules/user/schema/user.schema';
import { ApprovalFlowService } from 'src/modules/auto-billing/service/approval-flow.service';
import { EmailScheduler, EmailSchedulerDocument } from 'src/modules/common/schema/pa-email.schema';

@Injectable()
export class BillingService {
  constructor(
    @InjectModel(Billing.name) private billingModel: Model<BillingDocument>,
    @InjectModel(EmailScheduler.name) private emailSchedulerModel: Model<EmailSchedulerDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private billingFinanceService: BillingFinanceService,
    private approvalFlowService: ApprovalFlowService,
    private rpaSchedulerService: RpaSchedulerService,
  ) { }

  async createBillingAdvice(billingData: any): Promise<Billing> {
    const billingId = await this.generateBillingId();

    // Get the approval flow for this department and project
    let approvals: any[] = [];
    try {
      const approvalFlow = await this.approvalFlowService.getApprovalFlow(
        billingData.department,
        billingData.project,
        'billing'
      );

      // Map the approvers from the flow to the billing approvals format
      approvals = approvalFlow.approvers
        .filter(approver => approver.isActive)
        .sort((a, b) => a.order - b.order)
        .map(approver => ({
          userId: approver.userId,
          approverRole: approver.role,
          status: 'pending'
        }));
    } catch (error) {
      console.warn(`No approval flow found for ${billingData.department}/${billingData.project}. Using empty approval list.`);
    }

    const billing = new this.billingModel({
      ...billingData,
      billingId,
      approvals,
    });

    const newBilling = await billing.save();

    const _id = newBilling.createdBy;

    // Send email to preparers
    const user = await this.userModel.findOne({ _id }).populate('_id email fullName faceImage designation');

    if(!user) {
      throw new NotFoundException("user not found")
    }

    const emailScheduler = new this.emailSchedulerModel({
      to: user.email,
      subject: `Auto Billing Created: ${newBilling.billingId}`,
      body: `<p>Dear ${user.fullName}, <br/>Your auto billing - ${newBilling.billingId} has been created succesfully.</b><br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
    });
    await emailScheduler.save();

    // Send email to first approver
    const approverId = newBilling.approvals[0].userId;
    const approver = await this.userModel.findOne({ _id: approverId }).populate('_id email fullName faceImage designation');

    if(!approver) {
      throw new NotFoundException("Approver not found")
    }

    const approverScheduler = new this.emailSchedulerModel({
      to: approver.email,
      subject: `Billing Advice to be signed - Billing ID: ${newBilling.billingId}`,
      body: `<p>Dear ${approver.fullName}, <br/>Kindly review and sign billing advice Id - ${newBilling.billingId}.</b><br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
    });
    await approverScheduler.save();
    return newBilling;
  }

  // async approveBillingAdvice(billingId: string, userId: string, status: string, comments?: string): Promise<Billing> {
  //   const billing = await this.billingModel.findOne({ _id: billingId });
  //   if (!billing) {
  //     throw new NotFoundException('Billing advice not found');
  //   }

  //   const userObjectId = Types.ObjectId.isValid(userId) ? new Types.ObjectId(userId) : userId;

  //   const approval = billing.approvals.find(a => a.userId.equals(userObjectId));
  //   if (!approval) {
  //     throw new NotFoundException(`Approver with user ID ${userId} not found in this billing advice`);
  //   }

  //   approval.status = status;
  //   approval.approvedAt = new Date();
  //   approval.comments = comments;

  //   // Check if we need to update the overall status
  //   const allApproved = billing.approvals.every(a => a.status === 'approved');
  //   if (allApproved) {
  //     billing.status = 'ba_approved';

  //     // Save billing before starting RPA sequence
  //     const savedBilling = await billing.save();

  //     try {
  //       // Only schedule the Sale Order task initially
  //       await this.rpaSchedulerService.initiateGenerateSaleOrder(billingId);
  //     } catch (error) {
  //     }

  //     return savedBilling;
  //   } else if (status === 'rejected') {
  //     billing.status = 'ba_rejected';
  //   }

  //   return billing.save();
  // }

  // Updated approveBillingAdvice method in billing.service.ts

  async approveBillingAdvice(billingId: string, userId: string, status: string, comments?: string): Promise<Billing> {
    const billing = (await this.billingModel.findOne({ _id: billingId })
    .populate('approvals.userId', '_id email fullName faceImage designation')
    .populate('createdBy', '_id email fullName faceImage designation'));

    if (!billing) {
      throw new NotFoundException('Billing advice not found');
    }

    // Convert userId to ObjectId if it's not already
    const userObjectId = Types.ObjectId.isValid(userId) ? new Types.ObjectId(userId) : userId;

    // Check if we're in finance approval flow
    if (billing.isFinanceApprovalActive) {
      return this.approveFinanceBilling(billingId, userId, status, comments);
    }

    // Verify this is the current approver
    if (billing.currentApproverIndex < 0 || billing.currentApproverIndex >= billing.approvals.length) {
      throw new NotFoundException('No current approver found for this billing advice');
    }

    const currentApprover = billing.approvals[billing.currentApproverIndex];
    if (!currentApprover.userId.equals(userObjectId)) {
      throw new Error('Only the current approver can approve or reject this document');
    }

    // Update the current approver's status
    currentApprover.status = status;
    currentApprover.approvedAt = new Date();
    currentApprover.comments = comments;

    if (status === 'approved') {
      // Move to the next approver if there is one
      if (billing.currentApproverIndex < billing.approvals.length - 1) {

        billing.currentApproverIndex += 1;

        // Send email to next approver
        const userInfo: any = billing.approvals[billing.currentApproverIndex].userId;
        const billingSignEmail = new this.emailSchedulerModel({
          to: userInfo?.email,
          subject: `Sign Billing Advice : Billing ID - ${billing.billingId}`,
          body: `<p>Dear ${userInfo.fullName}, <br/>Kindly review and sign for billing advice Id ${billing.billingId}.br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
        });
        await billingSignEmail.save();

      } else {
        // All approvers have approved
        billing.status = 'ba_approved';

        // Send email to preparer
        const createdByInfo: any = billing.createdBy;
        const billingApprovedEmail = new this.emailSchedulerModel({
          to: createdByInfo?.email,
          subject: `Billing Advice Signed : Billing ID - ${billing.billingId}`,
          body: `<p>Dear ${createdByInfo.fullName}, <br/>Your billing advice - Billing Id ${billing.billingId} has been signed.<br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
        });
        await billingApprovedEmail.save();

        // Check if we should move to finance approvals
        if (billing.financeApprovals && billing.financeApprovals.length > 0) {
          billing.isFinanceApprovalActive = true;
          billing.currentApproverIndex = 0; // Start with the first finance approver
        } else {
          // No finance approvals needed, proceed to next step
          billing.workflowStage = 'sale-order';

          // Save billing before starting RPA sequence
          const savedBilling = await billing.save();

          try {
            // Only schedule the Sale Order task initially
            await this.rpaSchedulerService.initiateGenerateSaleOrder(billingId);
          } catch (error) {
            console.error(`Failed to start RPA sequence for billing ${billingId}: ${error.message}`);
          }

          return savedBilling;
        }
      }
    } else if (status === 'rejected') {
      billing.status = 'ba_rejected';
      // No need to move to next approver if rejected
    }

    return billing.save();
  }

  async createSaleOrder(billingId: string): Promise<Billing> {
    const billing = await this.billingModel.findOne({ _id: billingId });
    if (!billing) {
      throw new NotFoundException('Billing advice not found');
    }

    // Schedule the task instead of executing it directly
    await this.rpaSchedulerService.scheduleTask(
      RpaTaskType.SALE_ORDER,
      billingId,
      1,  // high priority
      new Date() // execute immediately
    );

    return billing;
  }

  async createDeliveryOrder(billingId: string): Promise<Billing> {
    const billing = await this.billingModel.findOne({ _id: billingId });
    if (!billing) {
      throw new NotFoundException('Billing advice not found');
    }

    // Schedule the task instead of executing it directly
    await this.rpaSchedulerService.scheduleTask(
      RpaTaskType.DELIVERY_ORDER,
      billingId,
      2,  // medium priority
      new Date() // execute immediately
    );

    return billing;
  }

  async createInvoice(billingId: string): Promise<Billing> {
    const billing = await this.billingModel.findOne({ _id: billingId });
    if (!billing) {
      throw new NotFoundException('Billing advice not found');
    }

    // Schedule the task instead of executing it directly
    await this.rpaSchedulerService.scheduleTask(
      RpaTaskType.INVOICE,
      billingId,
      3,  // lower priority
      new Date() // execute immediately
    );

    return billing;
  }

  private async generateBillingId(): Promise<string> {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const count = await this.billingModel.countDocuments({
      billingId: new RegExp(`BAID-UZEV-${year}${month}`)
    });
    const sequence = (count + 1).toString().padStart(2, '0');
    return `BAID-UZEV-${year}${month}${sequence}`;
  }

  async getBillingAdvice(billingId: string): Promise<Billing> {
    const billing = await this.billingModel.findOne({ _id: billingId })
      .populate('department')
      .populate('project')
      .populate('approvals.userId')
      .populate('lineItems.product');

    if (!billing) {
      throw new NotFoundException('Billing advice not found');
    }
    return billing;
  }

  async findBillingAdvices(query: any = {}): Promise<Billing[]> {
    const filter: any = {};

    if (query.billingId) {
      filter._id = new Types.ObjectId(query.billingId);
    }

    if (query.status) {
      filter.status = query.status;
    }

    if (query.department) {
      filter.department = new Types.ObjectId(query.department);
    }

    if (query.project) {
      filter.project = new Types.ObjectId(query.project);
    }

    // Date range filter
    if (query.fromDate || query.toDate) {
      filter.billingDate = {};

      if (query.fromDate) {
        filter.billingDate.$gte = new Date(query.fromDate);
      }

      if (query.toDate) {
        filter.billingDate.$lte = new Date(query.toDate);
      }
    }

    return this.billingModel.find(filter)
      .populate('department')
      .populate('project')
      .sort({ billingDate: -1 });
  }

  async getBillingWithRpaTasks(billingId: string): Promise<any> {
    const billing = await this.getBillingAdvice(billingId);
    const tasks = await this.rpaSchedulerService.getTasksByBillingId(billingId);
    const logs = await this.rpaSchedulerService.getLogsByBillingId(billingId);

    return {
      billing,
      tasks,
      logs
    };
  }

  // New method to add finance approvers
  async addFinanceApprovers(billing: any): Promise<Billing> {
    if (!billing) {
      throw new NotFoundException('Billing advice not found');
    }

    try {
      // First try to get project-specific finance flow
      let financeFlow;
      try {
        financeFlow = await this.billingFinanceService.getFinanceFlow(
          billing.project.toString(),
          billing.department.toString()
        );
      } catch (error) {
        // If no project-specific flow, try department flow
        try {
          financeFlow = await this.billingFinanceService.getFinanceFlow(
            null,
            billing.department.toString()
          );
        } catch (error) {
          // If no department flow, try global flow
          financeFlow = await this.billingFinanceService.getFinanceFlow();
        }
      }

      // Map the finance approvers to the billing format
      if (financeFlow && financeFlow.approvers) {
        const financeApprovals = financeFlow.approvers
          .filter(approver => approver.isActive)
          .sort((a, b) => a.order - b.order)
          .map(approver => ({
            userId: approver.userId,
            approverRole: approver.role,
            status: 'pending'
          }));

        // Add the finance approvals to the billing
        billing.financeApprovals = financeApprovals;
        billing.financeApprovalStatus = 'pending';
        billing.status = 'waiting_finance_approval'; // Update overall status

        const savedBilling = billing.save();

        const reviewEmailFinance = new this.emailSchedulerModel({
          to: financeFlow.approvers[0].userId.email,
          subject: `Review Invoice : Billing ID - ${billing.billingId}`,
          body: `<p>Dear ${financeFlow.approvers[0].userId.fullName}, <br/>Kindly review invoice for billing Id ${billing.billingId}.<br/><br/>Invoice Number - ${billing.invoiceId}<br/><br/>
          Invoice Document - <a href="${billing.invoiceFile}" target="_blank">View Invoice</a>
          <br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
        });
        await reviewEmailFinance.save();

        return savedBilling;
      }
    } catch (error) {
      console.warn(`No finance approval flow found. Using default finance approvers.`);
      // You could implement a fallback strategy here
    }

    return billing;
  }

  // Add a method to handle finance approvals
  // async approveFinanceBilling(billingId: string, userId: string, status: string, comments?: string): Promise<Billing> {
  //   const billing = await this.billingModel.findOne({ _id: billingId });
  //   if (!billing) {
  //     throw new NotFoundException('Billing advice not found');
  //   }

  //   // Convert userId to ObjectId if it's not already
  //   const userObjectId = Types.ObjectId.isValid(userId) ? new Types.ObjectId(userId) : userId;

  //   // Find the approval by userId
  //   const approval = billing.financeApprovals.find(a => a.userId.equals(userObjectId));
  //   if (!approval) {
  //     throw new NotFoundException(`Finance approver with user ID ${userId} not found in this billing advice`);
  //   }

  //   approval.status = status;
  //   approval.approvedAt = new Date();
  //   approval.comments = comments;

  //   // Check if we need to update the overall status
  //   const allApproved = billing.financeApprovals.every(a => a.status === 'approved');
  //   if (allApproved) {
  //     billing.financeApprovalStatus = 'approved';
  //     billing.status = 'finance_approved'; // Mark the overall billing as completed

  //     // Schedule a completion task if needed
  //     await this.rpaSchedulerService.scheduleTask(
  //       RpaTaskType.FINANCE, // You would need to add this enum value
  //       billingId,
  //       4,  // low priority
  //       new Date() // execute immediately
  //     );
  //   } else if (status === 'rejected') {
  //     billing.financeApprovalStatus = 'rejected';
  //     billing.status = 'finance_rejected';
  //   }
  //   return billing.save();
  // }

  // Updated approveFinanceBilling method in billing.service.ts

  async approveFinanceBilling(billingId: string, userId: string, status: string, comments?: string): Promise<Billing> {
    const billing = await this.billingModel.findOne({ _id: billingId });
    if (!billing) {
      throw new NotFoundException('Billing advice not found');
    }

    // Check if finance approval is active
    if (!billing.isFinanceApprovalActive) {
      throw new Error('Finance approval is not currently active for this document');
    }

    // Convert userId to ObjectId if it's not already
    const userObjectId = Types.ObjectId.isValid(userId) ? new Types.ObjectId(userId) : userId;

    // Verify this is the current finance approver
    if (billing.currentApproverIndex < 0 || billing.currentApproverIndex >= billing.financeApprovals.length) {
      throw new NotFoundException('No current finance approver found for this billing advice');
    }

    const currentApprover = billing.financeApprovals[billing.currentApproverIndex];
    if (!currentApprover.userId.equals(userObjectId)) {
      throw new Error('Only the current finance approver can approve or reject this document');
    }

    // Update the current approver's status
    currentApprover.status = status;
    currentApprover.approvedAt = new Date();
    currentApprover.comments = comments;

    const _id = billing.createdBy;
    const preparer = await this.userModel.findOne({ _id }).populate('_id email fullName faceImage designation');

    if(!preparer) {
      throw new NotFoundException("Prepare not found");
    }

    if (status === 'approved') {
      // Move to the next finance approver if there is one
      if (billing.currentApproverIndex < billing.financeApprovals.length - 1) {
        billing.currentApproverIndex += 1;
      } else {
        // All finance approvers have approved
        billing.financeApprovalStatus = 'approved';
        billing.status = 'finance_approved';
        billing.isFinanceApprovalActive = false;
        billing.workflowStage = 'collection'; // Set to final step when finance approved

        // Schedule a completion task if needed
        await this.rpaSchedulerService.scheduleTask(
          RpaTaskType.FINANCE,
          billingId,
          4,  // low priority
          new Date() // execute immediately
        );

        // Approve Email
        const emailSchedulerInvoice = new this.emailSchedulerModel({
          to: preparer.email,
          subject: `Invoice approved by Finance: Billing ID - ${billing.billingId}`,
          body: `<p>Dear ${preparer.fullName}, <br/>Invoice for billing Id ${billing.billingId} has been approved.<br/><br/>
          Invoice Number - ${billing.invoiceId}<br/><br/>
          Invoice Document - <a href="${billing.invoiceFile}" target="_blank">View Invoice</a><br/><br/>
          Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
        });
        await emailSchedulerInvoice.save();
      }
    } else if (status === 'rejected') {
      billing.financeApprovalStatus = 'rejected';
      billing.status = 'finance_rejected';
      billing.isFinanceApprovalActive = false;

      // Reject Email      
      const emailSchedulerInvoice = new this.emailSchedulerModel({
        to: preparer.email,
        subject: `Invoice rejected by Finance: Billing ID - ${billing.billingId}`,
        body: `<p>Dear ${preparer.fullName}, <br/>Invoice for billing Id ${billing.billingId} has been rejected.<br/><br/>
        Invoice Number - ${billing.invoiceId}<br/><br/>
        Invoice Document - <a href="${billing.invoiceFile}" target="_blank">View Invoice</a><br/><br/>
        Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
      });
      await emailSchedulerInvoice.save();
    }

    return billing.save();
  }
  // Add this method to the BillingService class in billing.service.ts

  // Updated getUserBillingAdvicesPaginated method in billing.service.ts

  async getUserBillingAdvicesPaginated(
    userId: string,
    companyId: string,
    filters: any = {},
    page: number = 1,
    limit: number = 10
  ): Promise<any> {
    if (!userId || !companyId) {
      throw new Error('User ID and Company ID are required');
    }

    // Convert IDs to ObjectId
    const userObjectId = Types.ObjectId.isValid(userId) ? new Types.ObjectId(userId) : userId;
    // const companyObjectId = Types.ObjectId.isValid(companyId) ? new Types.ObjectId(companyId) : companyId;

    // Base query for this company
    const baseQuery = { department: companyId };

    // Apply additional filters if provided
    if (filters.searchText) {
      baseQuery['$or'] = [
        { billingId: { $regex: filters.searchText, $options: 'i' } },
        { 'project.name': { $regex: filters.searchText, $options: 'i' } }
      ];
    }

    if (filters.dateFrom) {
      baseQuery['billingDate'] = { $gte: new Date(filters.dateFrom) };
    }

    if (filters.dateTo) {
      if (baseQuery['billingDate']) {
        baseQuery['billingDate']['$lte'] = new Date(filters.dateTo);
      } else {
        baseQuery['billingDate'] = { $lte: new Date(filters.dateTo) };
      }
    }

    // Find all billings for this company with applied filters
    const allBillings = await this.billingModel.find(baseQuery)
      .populate('department')
      .populate('project')
      .populate('approvals.userId', '_id email fullName faceImage designation')
      .populate('financeApprovals.userId')
      .populate('lineItems.product')
      .populate('createdBy', '_id email fullName faceImage designation')
      .exec();

    // Categorize billings
    const toSign: any[] = [];
    const inProgress: any[] = [];
    const completed: any[] = [];

    for (const billing of allBillings) {
      // Determine if user is the current approver
      const isCurrentRegularApprover = !billing.isFinanceApprovalActive &&
        billing.currentApproverIndex >= 0 &&
        billing.currentApproverIndex < billing.approvals.length &&
        billing.approvals[billing.currentApproverIndex].userId &&
        billing.approvals[billing.currentApproverIndex].userId._id.toString() === userObjectId.toString();

      const isCurrentFinanceApprover = billing.isFinanceApprovalActive &&
        billing.currentApproverIndex >= 0 &&
        billing.currentApproverIndex < billing.financeApprovals.length &&
        billing.financeApprovals[billing.currentApproverIndex].userId &&
        billing.financeApprovals[billing.currentApproverIndex].userId._id.toString() === userObjectId.toString();

      // Document is in "to sign" if user is the CURRENT approver with pending status
      if ((isCurrentRegularApprover && billing.approvals[billing.currentApproverIndex].status === 'pending') ||
        (isCurrentFinanceApprover && billing.financeApprovals[billing.currentApproverIndex].status === 'pending')) {
        toSign.push(billing);
      }
      // Document is "completed" if overall status is completed/finance_approved
      else if (billing.status === 'finance_approved' || billing.status === 'completed') {
        completed.push(billing);
      }
      // All other cases go to "in progress"
      else {
        inProgress.push(billing);
      }
    }

    // Get the category based on the tab
    let categoryData;
    let totalItems;

    switch (filters.tab) {
      case 0:
        categoryData = toSign;
        totalItems = toSign.length;
        break;
      case 1:
        categoryData = inProgress;
        totalItems = inProgress.length;
        break;
      case 2:
        categoryData = completed;
        totalItems = completed.length;
        break;
      default:
        categoryData = toSign;
        totalItems = toSign.length;
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const paginatedData = categoryData.slice(startIndex, endIndex);

    // Determine workflow stage for UI display
    // const determineWorkflowStage = (billing) => {
    //   if (billing.status.includes('completed') || billing.status.includes('finance_approved')) {
    //     return 'collection';
    //   } else if (billing.status.includes('invoice')) {
    //     return 'invoice';
    //   } else if (billing.status.includes('delivery')) {
    //     return 'delivery-order';
    //   } else if (billing.status.includes('sale')) {
    //     return 'sale-order';
    //   }
    //   return 'billing-advice';
    // };

    // Map the data to the expected format for the frontend
    const formattedData = paginatedData.map(billing => {
      // Determine priority based on your business logic
      let priority: 'high' | 'medium' | 'low' = 'medium';
      const today = new Date();
      const billingDate = new Date(billing.billingDate);
      const daysDiff = Math.floor((today.getTime() - billingDate.getTime()) / (1000 * 3600 * 24));

      if (daysDiff > 30) {
        priority = 'high';
      } else if (daysDiff > 15) {
        priority = 'medium';
      } else {
        priority = 'low';
      }

      // Get assignee name
      const assignee = billing.isFinanceApprovalActive
        ? (billing.financeApprovals.length > 0 && billing.financeApprovals[billing.currentApproverIndex]?.userId
          ? billing.financeApprovals[billing.currentApproverIndex].userId.fullName || 'Unknown'
          : 'Unassigned')
        : (billing.approvals.length > 0 && billing.approvals[billing.currentApproverIndex]?.userId
          ? billing.approvals[billing.currentApproverIndex].userId.fullName || 'Unknown'
          : 'Unassigned');

      return {
        id: billing.billingId,
        _id: billing._id,
        title: `${billing.project ? billing.project.projectName : 'Unknown Project'}`,
        description: `Billing for ${billing.lineItems.map(item => item.description).join(', ')}`,
        type: 'invoice',
        date: billing.billingDate,
        size: `${(Math.random() * 5 + 1).toFixed(1)} MB`, // Placeholder
        priority,
        assignee,
        status: filters.tab === 0 ? 'to-sign' : (filters.tab === 1 ? 'in-progress' : 'completed'),
        attachments: billing.attachments,
        // currentStep: determineWorkflowStage(billing),
        currentStep: billing.workflowStage,
        // Include additional fields as needed
        department: billing.department,
        project: billing.project,
        lineItems: billing.lineItems,
        approvals: billing.approvals,
        financeApprovals: billing.financeApprovals,
        originalStatus: billing.status,
        currentApproverIndex: billing.currentApproverIndex,
        isFinanceApprovalActive: billing.isFinanceApprovalActive,
        createdBy: billing.createdBy,

        isSOCreated: billing.isSOCreated,
        saleOrderId: billing.saleOrderId,
        saleOrderFile: billing.saleOrderFile,
        SOCreatedAt: billing.SOCreatedAt,

        isDOCreated: billing.isDOCreated,
        deliveryOrderId: billing.deliveryOrderId,
        deliveryOrderFile: billing.deliveryOrderFile,
        DOCreatedAt: billing.DOCreatedAt,

        isInvoiceCreated: billing.isInvoiceCreated,
        invoiceId: billing.invoiceId,
        invoiceFile: billing.invoiceFile,
        InvoiceCreatedAt: billing.InvoiceCreatedAt,

        isInvoicePosted: billing.isInvoicePosted,
        InvoicePostedAt: billing.InvoicePostedAt,

      };
    });

    return {
      data: formattedData,
      pagination: {
        totalItems,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
        itemsPerPage: limit,
        startItem: startIndex + 1,
        endItem: Math.min(endIndex, totalItems)
      },
      counts: {
        toSign: toSign.length,
        inProgress: inProgress.length,
        completed: completed.length
      }
    };
  }
}