import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ProjectGallery } from '../schema/project-gallery.schema';
import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import * as path from 'path';

@Injectable()
export class ProjectGalleryService {
  private s3: S3Client;

  constructor(
    @InjectModel(ProjectGallery.name) private readonly projectGalleryModel: Model<ProjectGallery>,
  ) {
    // Initialize S3 client for AWS SDK v3
    this.s3 = new S3Client({
      region: process.env.AWS_DEFAULT_REGION!,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
      },
    });
  }

  // Function to upload file to S3 using AWS SDK v3
  async uploadFileToS3(file: Express.Multer.File, segment: string, title: string, date: string) {
    const folderName = `seah/gallery/${segment}/${date}-${title}`;
    const fileName = `${folderName}/${Date.now().toString()}-${path.basename(file.originalname)}`;

    const params = {
      Bucket: process.env.AWS_S3_SEAH, // Your S3 bucket name
      Key: fileName, // File path in S3
      Body: file.buffer, // File content
      ContentType: file.mimetype, // File MIME type
    };

    try {
      const command = new PutObjectCommand(params);
      const result = await this.s3.send(command);
      return {
        key: fileName, // S3 key (path to the file in the bucket)
        location: `https://${process.env.AWS_S3_SEAH}.s3.${process.env.AWS_DEFAULT_REGION}.amazonaws.com/${fileName}`, // S3 file URL
      };
    } catch (err) {
      throw new BadRequestException(`Failed to upload file to S3: ${err.message}`);
    }
  }

  // Save the gallery metadata to MongoDB
  async saveGallery(
    site: string,
    title: string,
    date: Date,
    files: Express.Multer.File[], // Updated type for the v3 SDK usage
    descriptions: string[],
    order: number,
    userId: string,
  ): Promise<ProjectGallery> {
    // Ensure files and descriptions are properly matched
    // if (files.length !== descriptions.length) {
    //   throw new BadRequestException('Each file must have a corresponding description.');
    // }

    // Upload files to S3 and map each file to the required schema format
    const galleryFiles = await Promise.all(
      files.map(async (file, index) => {
        const s3Result = await this.uploadFileToS3(file, site, title, date.toISOString());
        return {
          fileName: s3Result.key, // S3 key (path to the file in the bucket)
          description: descriptions[index],
          url: s3Result.location, // S3 file URL
        };
      }),
    );

    const galleryData = {
      site,
      title,
      date,
      files: galleryFiles, // Files array with the mapped fileName and URL
      order,
      updatedBy: userId, // User who uploaded/updated the gallery
    };

    const newGallery = new this.projectGalleryModel(galleryData);
    return newGallery.save();
  }

  // Read galleries by segment or fetch all, sorted by 'createdAt' in descending order
  async getGalleries(site?: string): Promise<ProjectGallery[]> {
    if (site) {
      // Fetch galleries by site, sorted by 'createdAt' in descending order
      return this.projectGalleryModel.find({ site }).sort({ createdAt: -1 }).exec();
    }
    // Fetch all galleries, sorted by 'createdAt' in descending order
    return this.projectGalleryModel.find().sort({ createdAt: -1 }).exec();
  }

  // Read gallery by _id, or fetch all, sorted by 'createdAt' in descending order
  async getGalleriesById(id?: string): Promise<ProjectGallery[]> {
    if (id) {
      // Ensure the ID is treated as a MongoDB ObjectId
      return this.projectGalleryModel.find({ _id: new Types.ObjectId(id) }).sort({ createdAt: -1 }).exec();
    }
    // Fetch all galleries, sorted by 'createdAt' in descending order
    return this.projectGalleryModel.find().sort({ createdAt: -1 }).exec();
  }

  // Update an existing gallery by ID
  async updateGallery(
    id: string,
    segment: string,
    title: string,
    date: Date,
    files: Express.MulterS3.File[],
    descriptions: string[],
    order: number,
    userId: string,
  ): Promise<ProjectGallery> {
    const gallery = await this.projectGalleryModel.findById(id);

    if (!gallery) {
      throw new NotFoundException('Gallery not found');
    }

    // Update metadata and files
    gallery.site = segment;
    gallery.title = title;
    gallery.date = date;
    gallery.order = order;
    // gallery.updatedBy = userId;

    // Replace the files if new files are uploaded
    if (files.length > 0 && files.length === descriptions.length) {
      gallery.files = files.map((file, index) => ({
        fileName: file.key,
        description: descriptions[index],
        url: file.location,
      }));
    }

    return gallery.save();
  }

  // Delete a gallery by ID
  async deleteGallery(id: string): Promise<void> {
    const gallery = await this.projectGalleryModel.findById(id);

    if (!gallery) {
      throw new NotFoundException('Gallery not found');
    }

    // Remove files from S3
    for (const file of gallery.files) {
      const deleteParams = {
        Bucket: process.env.AWS_S3_SEAH,
        Key: file.fileName,
      };
      await this.s3.send(new DeleteObjectCommand(deleteParams));
    }

    // Use findByIdAndDelete to remove the document from MongoDB
    await this.projectGalleryModel.findByIdAndDelete(id);
  }
}