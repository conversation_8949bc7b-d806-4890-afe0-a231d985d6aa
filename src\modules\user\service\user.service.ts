import { ConflictException, Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import { User, UserDocument } from '../schema/user.schema';
import { CreateUserDTO } from '../dto/create-user.dto';
import { LoginDTO } from '../dto/login.dto';
import { SecurityService } from './security.service';
import { Role, RoleDocument } from 'src/modules/auth/schema/role.schema';

@Injectable()
export class UserService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(Role.name) private roleModel: Model<RoleDocument>,
    private jwtService: JwtService,
    private securityService: SecurityService
  ) { }

  async createUser(createUserDto: CreateUserDTO): Promise<any> {
    // Check if the email already exists
    const existingUser = await this.userModel.findOne({ email: createUserDto.email });
    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // If email does not exist, proceed to create user
    const user = new this.userModel(createUserDto);
    await user.save();

    const payload = {
      user: {
        userId: user._id,
        userName: user.username,
        userEmail: user.email,
        userFaceId: user.faceId
      }
    };
    const token = this.jwtService.sign(payload);

    return { user, token };
  }

  async findAll(): Promise<User[]> {
    return this.userModel.find().populate('role').exec();  // Populate role when retrieving users
  }

  async findAllExcludingMgmt(): Promise<User[]> {
    // Find roles containing "-HOD" in their name (case-sensitive or case-insensitive)
    const hodRoles = await this.roleModel
      .find({ name: { $regex: '-HOD', $options: 'i' } }) // Use $regex to find names with "-HOD"
      .exec();

      console.log(hodRoles);
  
    if (!hodRoles || hodRoles.length === 0) {
      throw new Error('No roles containing "-HOD" found');
    }
  
    // Extract the IDs of the roles that match the "-HOD" pattern and convert them to strings
    const hodRoleIds = hodRoles.map(role => role._id.toString());
    // console.log("HOD Role IDs to Exclude:", hodRoleIds);
  
    // Exclude users whose role is in the list of "-HOD" role IDs (as strings)
    return this.userModel
      .find({ role: { $in: hodRoleIds } }) // Use string array for $nin filter
      .populate('role') // Populate role details for each user
      .exec();
  }
  
  
  async updateUser(id: string, updateUserDto: CreateUserDTO): Promise<User> {
    const user = await this.userModel.findByIdAndUpdate(id, updateUserDto, { new: true }).exec();
    if(!user) {
      throw new NotFoundException("User not found")
    }
    return user;
  }

  async deleteUser(id: string): Promise<User> {
    const user = await this.userModel.findByIdAndDelete(id).exec();
    if(!user) {
      throw new NotFoundException("User not found")
    }
    return user;
  }

  async validateOTP(email: string): Promise<User | null> {

    // Check login by email and password
    if (email) {
      const user = await this.userModel.findOne({ email })
      .populate({
        path: 'role',
        populate: {
          path: 'permissions',  // Populate permissions inside the role
          model: 'Permission',  // Make sure to use the correct model name for permissions
        },
      })
      .populate({
        path: 'departments',   // Populate the departments field
        model: 'Department',   // Use the correct model name for departments
      });
      
      if (!user) {
        throw new UnauthorizedException('User not found');
      }
      // const passwordValid = await bcrypt.compare(password, user.password);
      // if (!passwordValid) {
      //   throw new UnauthorizedException('Invalid password');
      // }
      return user;
    }

    throw new UnauthorizedException('Invalid login credentials');
  }

  async validateUser(loginDto: LoginDTO): Promise<User | null> {
    const { faceId, email, password } = loginDto;

    // Check login by faceId
    if (faceId) {
      const user = await this.userModel
        .findOne({ faceId })
        .populate({
          path: 'role',
          populate: {
            path: 'permissions',  // Populate permissions inside the role
            model: 'Permission',  // Make sure to use the correct model name for permissions
          },
        })
        .populate({
          path: 'departments',   // Populate the departments field
          model: 'Department',   // Use the correct model name for departments
        });
    
      if (!user) {
        throw new UnauthorizedException('Face ID not found');
      }
      return user;
    }

    // Check login by email and password
    if (email && password) {
      const user = await this.userModel.findOne({ email });
      if (!user) {
        throw new UnauthorizedException('User not found');
      }
      // const passwordValid = await bcrypt.compare(password, user.password);
      // if (!passwordValid) {
      //   throw new UnauthorizedException('Invalid password');
      // }
      return user;
    }

    throw new UnauthorizedException('Invalid login credentials');
  }

  async login(encryptedData: string): Promise<any> {
    const decryptedData = this.securityService.decrypt(encryptedData);
    const user = await this.validateUser(decryptedData);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const payload = {
      user: {
        userId: user._id,
        userName: user.username,
        userEmail: user.email,
        userFaceId: user.faceId
      }
    };
    const token = this.jwtService.sign(payload);

    var data = { user, token };
    return { data: this.securityService.encrypt(data) };
  }

  async getUserById(id: string): Promise<User> {
    const user = await this.userModel.findById(id);
    
    if (!user) {
      throw new NotFoundException(`User with object ID ${id} not found`);
    }
    
    return user;
  }
}
