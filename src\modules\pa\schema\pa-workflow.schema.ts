import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ApprovalWorkflowDocument = ApprovalWorkflow & Document;

@Schema({ timestamps: true, collection: 'pa_workflow' })
export class ApprovalWorkflow {
  @Prop({ type: Types.ObjectId, ref: 'PaymentAdvice', required: true })
  paymentAdviceId: Types.ObjectId;  // Links to the Payment Advice

  @Prop([
    {
      userId: { type: Types.ObjectId, ref: 'User', required: true },  // User responsible for the step
      stepNumber: { type: Number, required: true },  // Step number in the workflow
      approvalType: { type: String, required: true },  // e.g., "Approved By", "Reviewed By"
      role: { type: String, required: true },  // Role of the user in this step (e.g., "CFO", "CEO", "Head of Department")
      status: { type: String, enum: ['pending', 'approved', 'rejected', 'cancelled'], default: 'pending' },  // Current status
      approvalDate: { type: Date },  // Date of approval/rejection
      comments: { type: String },  // Comments from approver
      assignedAt: { type: Date },  // Time the step was assigned to the user
      stepDuration: { type: Number },  // Duration (in hours) the user took to approve/reject the step
      cancelDate: { type: Date }, 
      cancelReason: { type: String }
    }
  ])
  steps: [
    {
      userId: Types.ObjectId;
      stepNumber: number;
      approvalType: string;
      role: string;
      status: string;
      approvalDate?: Date;
      comments?: string;
      assignedAt: Date;
      stepDuration?: number;
      cancelDate?: Date;
      cancelReason?: string;
    }
  ];

  @Prop({ type: Number, default: 1 })
  currentStep: number;  // Tracks the active step in the workflow

  @Prop({ type: Boolean, default: false })
  isCompleted: boolean;  // Marks when the workflow is finished

  @Prop({ type: Date })
  completedAt?: Date;  // Date when the entire workflow is completed

  @Prop({ type: Number })  // Duration of the entire workflow process
  workflowDuration?: number;  // Total duration (in hours) of the workflow from start to completion
}

export const PaWorkflowSchema = SchemaFactory.createForClass(ApprovalWorkflow);
