import { Controller, Post, Body } from '@nestjs/common';
import { EmailService } from '../../pa/service/email.service';

@Controller('email')
export class EmailController {
  constructor(private readonly emailService: EmailService) {}

  @Post('send')
  async sendEmail(@Body() body: { to: string; subject: string; text: string; html?: string }) {
    const { to, subject, text, html } = body;
    await this.emailService.sendMail(to, subject, html);
    return { message: `Email sent successfully to ${to}` };
  }
}
