import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { Types } from 'mongoose';

@Schema({ timestamps: true, collection: 'project_report' }) // Custom collection name
export class ProjectReport extends Document {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  month: string;

  @Prop()
  pdf_link?: string;

  @Prop()
  doc_link?: string;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  updatedBy: Types.ObjectId;
}

export const ProjectReportSchema = SchemaFactory.createForClass(ProjectReport);
