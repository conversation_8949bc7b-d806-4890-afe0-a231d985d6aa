import { Body, Controller, ForbiddenException, Get, HttpException, HttpStatus, Param, Patch, Post, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ProjectPOService } from '../service/project-po.service';
import { ProjectPO } from '../schema/project-po.schema';
import { CreateItemDto } from '../dto/create-project-item.dto';

@Controller('project-po')
export class ProjectPOController {
  constructor(private readonly poService: ProjectPOService) {}

  @Post('create')
  async create(@Body() createItemDto: CreateItemDto) {
    try {
      const result = await this.poService.create(createItemDto);
      return {
        status: 'success',
        message: 'Item created successfully',
        data: result
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to create item',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('import')
  @UseInterceptors(FileInterceptor('file'))
  async importPayments(@UploadedFile() file: Express.Multer.File): Promise<string> {
    // return "Failed to process payment"
    throw new ForbiddenException('Failed to process PV.');
    return await this.poService.importPaymentsFromExcel(file.buffer);
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadPayments(@UploadedFile() file: Express.Multer.File): Promise<string> {
    return await this.poService.importPaymentsFromExcel(file.buffer);
  }

  @Get()
  async getAllPayments(): Promise<any> {
    return await this.poService.findAll();
  }

  @Get('bp')
  async getPaymentByBP(): Promise<any> {
    return await this.poService.getPOsGroupedByBP();
  }

  // New API endpoint to get the sum of payments where `isExcluded` is false
  @Get('total')
  async getTotalNonExcludedPayments(): Promise<any> {
    return await this.poService.getTotalNonExcludedPayments();
  // return await this.poService.getTotalPaymentsByStatus();
  }

  // New API endpoint to get the sum of payments where `isExcluded` is false
  @Get('total-exclude')
  async getTotalExcludedPayments(): Promise<any> {
    return await this.poService.getTotalExcludedPayments();
  }

  // New endpoint to update the `isExcluded` status
  // New endpoint to toggle the `isExcluded` status (Include/Exclude)
  @Patch(':id/toggle-exclusion')
  async toggleIsExcluded(
    @Param('id') id: string,
    @Body('isExcluded') isExcluded: boolean,
  ): Promise<{ success: boolean; data: ProjectPO; message: string }> {
    const updatedPayment = await this.poService.toggleIsExcludedStatus(id, isExcluded);
    return {
      success: true,
      data: updatedPayment,
      message: `Record with ID ${id} successfully updated to isExcluded: ${isExcluded}`,
    };
  }
}
