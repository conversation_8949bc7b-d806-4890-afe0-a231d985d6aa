import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type RoleDocument = Role & Document;

@Schema({ timestamps: true,  collection: 'role' })
export class Role {
  @Prop({ required: true })
  name: string;  // Role name (e.g., <PERSON><PERSON>, Manager)

  @Prop([{ type: Types.ObjectId, ref: 'Permission' }])
  permissions: Types.ObjectId[];  // Array of permissions linked to the role
}

export const RoleSchema = SchemaFactory.createForClass(Role);
