import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PASerialNumber, SerialNumberDocument } from '../schema/pa-serial-no.schema';

@Injectable()
export class PASerialNumberService {
  constructor(
    @InjectModel(PASerialNumber.name) private readonly serialNumberModel: Model<SerialNumberDocument>
  ) {}

  async getNextSerialNumber(prefix: string): Promise<string> {
    const serial = await this.serialNumberModel.findOneAndUpdate(
      { prefix },
      { $inc: { currentNumber: 1 } },
      { new: true, upsert: true }
    );

    const paddedNumber = serial.currentNumber.toString().padStart(6, '0');
    return `${prefix}-${paddedNumber}`;
  }
}
