import { Type } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional, IsBoolean, IsDate, IsObject } from 'class-validator';

export class CreateProjectDto {
  @IsString()
  @IsNotEmpty()
  projectName: string;

  @IsString()
  @IsNotEmpty()
  departmentId: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @Type(() => Date)
  @IsDate()
  startDate: Date;

  @Type(() => Date)
  @IsDate()
  endDate: Date;

  @IsString()
  @IsOptional()
  projectManager?: string;

  @IsObject()
  @IsOptional()
  additionalDetails?: Record<string, any>;
}

export class UpdateProjectDto {
  @IsString()
  @IsOptional()
  projectName?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @Type(() => Date)
  @IsDate()
  startDate: Date;

  @Type(() => Date)
  @IsDate()
  endDate: Date;

  @IsString()
  @IsOptional()
  projectManager?: string;

  @IsObject()
  @IsOptional()
  additionalDetails?: Record<string, any>;
}

export class ProjectResponseDto {
  id: string;
  projectId: string;
  projectName: string;
  departmentId: string;
  description?: string;
  isActive: boolean;
  startDate?: Date;
  endDate?: Date;
  projectManager?: string;
  additionalDetails?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}