// create-ippa-message.dto.ts
import { IsNotEmpty, IsString, IsOptional, IsMongoId } from 'class-validator';
import { Types } from 'mongoose';

export class CreateIPPAMessageDto {
  @IsNotEmpty()
  @IsMongoId()
  ippaSubmissionId: string | Types.ObjectId;

  @IsNotEmpty()
  @IsMongoId()
  senderId: string | Types.ObjectId;

  @IsNotEmpty()
  @IsString()
  senderRole: string;

  @IsNotEmpty()
  @IsString()
  message: string;

  @IsOptional()
  @IsString()
  attachmentUrl?: string;
}