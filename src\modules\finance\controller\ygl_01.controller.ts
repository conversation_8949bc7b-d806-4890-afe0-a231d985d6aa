import { Body, Controller, Get, HttpStatus, Param, ParseIntPipe, Patch, Post, Query, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { YGL01Service } from '../service/ygl_01.service';

@Controller('finance')
export class YGL01Controller {
  constructor(private readonly ygl01Service: YGL01Service) { }

  @Post('YGL01')
  @UseInterceptors(FileInterceptor('file'))
  async importPayments(@UploadedFile() file: Express.Multer.File, @Body() body: { CompanyCode: string; CompanyName: string, LedgerType: string, StartDate: string, EndDate: string }): Promise<string> {
    return await this.ygl01Service.importYGL01(body, file.buffer);
  }

  @Get('YGL01')
  async getAllYGL01(
    @Query('page', new ParseIntPipe({ errorHttpStatusCode: HttpStatus.BAD_REQUEST })) page: number = 1,
    @Query('limit', new ParseIntPipe({ errorHttpStatusCode: HttpStatus.BAD_REQUEST })) limit: number = 10,
  ): Promise<{ data: any[]; total: number; page: number; limit: number }> {
    return await this.ygl01Service.getAllYGL01(page, limit);
  }
}
