import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type TestDocument = Test & Document;

@Schema({ 
  timestamps: true,
  collection: 'tests' // Explicit collection name
})
export class Test {
  @Prop({ type: Types.ObjectId, auto: true })
  _id: Types.ObjectId;

  @Prop({ required: true, unique: true, index: true })
  email: string;

  @Prop({ required: true })
  firstName: string;

  @Prop({ required: true })
  lastName: string;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: 'user', enum: ['user', 'admin', 'moderator'] })
  role: string;

  @Prop({ default: Date.now })
  lastLogin: Date;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;
  
  @Prop({ default: Date.now })
  createdAt: Date;
}

export const TestSchema = SchemaFactory.createForClass(Test);

// Create indexes for better performance
TestSchema.index({ email: 1 });
TestSchema.index({ isActive: 1 });
TestSchema.index({ role: 1 });
TestSchema.index({ createdAt: -1 });