import { Controller, Post, Body } from '@nestjs/common';
import { SecurityService } from '../service/security.service';

@Controller('security')
export class SecurityController {
  constructor(private readonly securityService: SecurityService) {}

  @Post('encrypt')
  async encrypt(@Body() body: any) {
    return this.securityService.encrypt(body.data);
  }

  @Post('decrypt')
  async decrypt(@Body() body: any) {
    return this.securityService.decrypt(body.data);
  }
}
