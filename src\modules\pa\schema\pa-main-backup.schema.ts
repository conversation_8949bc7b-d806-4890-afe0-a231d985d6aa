// import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
// import { Type } from 'class-transformer';
// import { IsArray, IsMongoId, IsNotEmpty, IsString, ValidateNested } from 'class-validator';
// import { Document, Types } from 'mongoose';

// // Define the Payment Term sub-schema
// class PaymentTerm {
//   @Prop({ type: Number })
//   days: number;

//   @Prop({ type: String })
//   termType: string;  // e.g., "back_to_back", "advance_payment"
// }

// class WorkflowParticipantDTO {
//   @IsMongoId()
//   userId: string;  // User ID of the participant

//   @IsString()
//   @IsNotEmpty()
//   approvalType: string;  // Could be 'Approved By' or 'Reviewed By'
// }

// export type PaymentAdviceDocument = PaymentAdvice & Document;

// @Schema({ timestamps: true, collection: 'pa_main' }) // Custom collection name
// export class PaymentAdvice {
//   @Prop()
//   companyName: string;

//   @Prop()
//   companyAddress: string;

//   @Prop()
//   telNumber: string;

//   @Prop()
//   faxNumber: string;

//   @Prop()
//   companyLogo: string;

//   @Prop()
//   departmentCode: string;

//   @Prop()
//   vendorName: string;

//   @Prop()
//   vendorAddress: string;

//   // Explicitly define the type for paymentTerm as the PaymentTerm class
//   @Prop({ type: PaymentTerm })
//   paymentTerm: PaymentTerm;

//   @Prop([{ type: Object }])
//   documents: [
//     {
//       invoiceNumber: string;
//       invoiceDate: Date;
//       poNumber: string;
//       projectCode: string;
//       amount: number;
//       attachments: [
//         {
//           type: string;
//           s3Url: string;
//         }
//       ];
//     }
//   ];

//   @Prop()
//   detailsPayment: string;

//   @Prop()
//   apbBudgeted: string;

//   @Prop()
//   serviceLocation: string;

//   @Prop()
//   staffConsultant: string;

//   @Prop()
//   chargeable: boolean;

//   @Prop()
//   accountManager: string;

//   @Prop()
//   costCategory: string;

//   @Prop([{ type: Object }])
//   chargeBacks: [
//     {
//       glCode: string;
//       amount: number;
//     }
//   ];

//   @Prop({ type: Object })
//   withholdingTax: {
//     percentage: number;
//     trfAttachment: { s3Url: string };
//     verifiedBy: { name: string; date: Date };
//   };

//   @Prop({ type: Types.ObjectId, ref: 'User', required: true })
//   createdBy: Types.ObjectId;  // User who created the Payment Advice

//   @IsArray()
//   @ValidateNested({ each: true })
//   @Type(() => WorkflowParticipantDTO)
//   workflowParticipants: WorkflowParticipantDTO[];  // Array of workflow participants with approval types

//   @Prop({ type: Types.ObjectId, ref: 'ApprovalWorkflow' })
//   approvalWorkflowId: Types.ObjectId;

//   @Prop()
//   status: string; // e.g., "submitted", "approved", "rejected", "pending"

//   @Prop({ type: Date })
//   dueDate: Date;  // Optional field for due date

//   @Prop({ type: Number })
//   amount: number;  // Amount of the payment

// }

// export const PaMainSchema = SchemaFactory.createForClass(PaymentAdvice);
