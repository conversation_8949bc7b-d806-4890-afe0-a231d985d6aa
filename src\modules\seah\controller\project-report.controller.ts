import { Controller, Get, Post, Put, Delete, Param, Body, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { CreateProjectReportDto } from '../dto/create-project-report.dto';
import { UpdateProjectReportDto } from '../dto/update-project-report.dto';
import { ProjectReport } from '../schema/project-report.schema';
import { ProjectReportService } from '../service/project-report.service';
import { S3Service } from 'src/modules/common/services/s3.service';

@Controller('project-report')
export class ProjectReportController {
  constructor(
    private readonly projectReportService: ProjectReportService,
    private readonly s3Service: S3Service,  // Inject S3Service to handle file uploads
  ) {}

  // Create a new report and upload the file to S3
  @Post()
  @UseInterceptors(FileInterceptor('file')) // Handle file upload in the request
  async create(
    @UploadedFile() file: Express.Multer.File,  // Get the uploaded file
    @Body() createProjectReportDto: CreateProjectReportDto,
  ): Promise<ProjectReport> {
    // Upload the file to S3 and get the link
    let pdf_link = '';
    if (file) {
      pdf_link = await this.s3Service.uploadFile(file)
    // pdf_link = 'test';
    }

    // Update the DTO with the uploaded file link
    const reportData = { ...createProjectReportDto, pdf_link };
    return this.projectReportService.create(reportData);
  }

  @Get()
  async findAll(): Promise<ProjectReport[]> {
    return this.projectReportService.findAll();
  }

  @Get(':month')
  async findByMonth(@Param('month') month: string): Promise<ProjectReport[]> {
    return this.projectReportService.findByMonth(month);
  }

  @Put(':id')
  @UseInterceptors(FileInterceptor('file')) // Use FileInterceptor for update to handle file uploads
  async update(
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,  // Get the uploaded file (if any)
    @Body() updateProjectReportDto: UpdateProjectReportDto,
  ): Promise<ProjectReport> {
    let pdf_link = updateProjectReportDto.pdf_link;

    // If a new file is uploaded, update the link
    if (file) {
    //   pdf_link = await this.s3Service.uploadFile(file);
    pdf_link = 'test'
    }

    // Update the DTO with the new file link
    const updatedData = { ...updateProjectReportDto, pdf_link };
    return this.projectReportService.update(id, updatedData);
  }

  @Delete(':id')
  async delete(@Param('id') id: string): Promise<void> {
    return this.projectReportService.delete(id);
  }
}
