import { Controller, Post, Get, Body } from '@nestjs/common';
import { CreatePermissionDTO } from '../dto/create-permission.dto';
import { PermissionService } from '../service/permission.service';

@Controller('permissions')
export class PermissionController {
  constructor(private readonly permissionService: PermissionService) {}

  @Post('create')
  async createPermission(@Body() createPermissionDto: CreatePermissionDTO) {
    return this.permissionService.create(createPermissionDto);
  }

  @Get()
  async findAll() {
    return this.permissionService.findAll();
  }
}
