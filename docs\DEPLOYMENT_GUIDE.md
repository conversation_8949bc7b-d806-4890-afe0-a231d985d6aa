# Deployment Guide

## 🚀 Deployment Overview

UzmaCloud API supports multiple deployment strategies:
- **Local Development** - Docker Compose or direct Node.js
- **Staging/Production** - AWS ECS with Fargate
- **CI/CD** - GitLab CI/CD pipeline

## 📋 Table of Contents
- [Prerequisites](#prerequisites)
- [Environment Configuration](#environment-configuration)
- [Local Development](#local-development)
- [Docker Deployment](#docker-deployment)
- [AWS ECS Deployment](#aws-ecs-deployment)
- [CI/CD Pipeline](#cicd-pipeline)
- [Monitoring & Health Checks](#monitoring--health-checks)

---

## ✅ Prerequisites

### System Requirements
- **Node.js**: v18 or higher
- **Docker**: v20+ (for containerized deployment)
- **AWS CLI**: v2+ (for AWS deployment)
- **Git**: For version control

### AWS Services Required
- **ECS (Elastic Container Service)** - Container orchestration
- **ECR (Elastic Container Registry)** - Docker image storage
- **DocumentDB** - MongoDB-compatible database
- **S3** - File storage
- **Rekognition** - Face recognition service
- **SES** - Email service
- **SSM Parameter Store** - Secure configuration storage

---

## 🔧 Environment Configuration

### Environment Variables

#### Application Settings
```bash
NODE_ENV=production
PORT=3000
LOG_LEVEL=info
```

#### Database Configuration
```bash
# Local MongoDB
DATABASE_URL=mongodb://localhost:27017/uzmacloud

# AWS DocumentDB (Production)
DATABASE_URL=mongodb://username:<EMAIL>:27017/uzmacloud?ssl=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false
```

#### AWS Services
```bash
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=ap-southeast-1
AWS_S3_BUCKET=uzmacloud-files
AWS_S3_SEAH=uzmacloud-seah
REKOGNITION_COLLECTION_ID=uzmacloud-faces
```

#### Security
```bash
JWT_SECRET=your-super-secure-jwt-secret-key
ENCRYPTION_SECRET=your-encryption-secret-key
```

#### External APIs
```bash
RPA_ENDPOINT=https://your-rpa-service.com/api
RPA_API_TOKEN=your-rpa-api-token
RPA_CALLBACK_TOKEN=your-callback-token
```

#### Email Configuration
```bash
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your-app-password
```

---

## 💻 Local Development

### Direct Node.js Setup
```bash
# Clone repository
git clone <repository-url>
cd uzmacloud-api

# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Edit .env with your local settings

# Start MongoDB (if using local)
mongod --dbpath /path/to/your/db

# Run in development mode
npm run start:dev
```

### Using Docker Compose
```yaml
# docker-compose.yml
version: '3.8'
services:
  api:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=mongodb://mongo:27017/uzmacloud
    depends_on:
      - mongo
    volumes:
      - .:/app
      - /app/node_modules

  mongo:
    image: mongo:7
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

volumes:
  mongo_data:
```

```bash
# Start with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f api

# Stop services
docker-compose down
```

---

## 🐳 Docker Deployment

### Build Docker Image
```bash
# Build production image
docker build -t uzmacloud-api:latest .

# Tag for registry
docker tag uzmacloud-api:latest your-registry/uzmacloud-api:latest

# Push to registry
docker push your-registry/uzmacloud-api:latest
```

### Run Container
```bash
# Run with environment file
docker run -d \
  --name uzmacloud-api \
  --env-file .env.production \
  -p 3000:3000 \
  uzmacloud-api:latest

# Run with inline environment variables
docker run -d \
  --name uzmacloud-api \
  -e NODE_ENV=production \
  -e DATABASE_URL=mongodb://... \
  -e AWS_ACCESS_KEY_ID=... \
  -p 3000:3000 \
  uzmacloud-api:latest
```

### Health Check
```bash
# Check container health
docker ps
docker logs uzmacloud-api

# Test API health
curl http://localhost:3000/health
```

---

## ☁️ AWS ECS Deployment

### 1. Create ECR Repository
```bash
# Create ECR repository
aws ecr create-repository --repository-name uzmacloud-api

# Get login token
aws ecr get-login-password --region ap-southeast-1 | \
  docker login --username AWS --password-stdin \
  123456789012.dkr.ecr.ap-southeast-1.amazonaws.com
```

### 2. Build and Push Image
```bash
# Build for production
docker build -t uzmacloud-api .

# Tag for ECR
docker tag uzmacloud-api:latest \
  123456789012.dkr.ecr.ap-southeast-1.amazonaws.com/uzmacloud-api:latest

# Push to ECR
docker push 123456789012.dkr.ecr.ap-southeast-1.amazonaws.com/uzmacloud-api:latest
```

### 3. Create Task Definition
```json
{
  "family": "uzmacloud-prod-api",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "api",
      "image": "123456789012.dkr.ecr.ap-southeast-1.amazonaws.com/uzmacloud-api:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        },
        {
          "name": "PORT",
          "value": "3000"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_URL",
          "valueFrom": "/prod/docdb/connection_string"
        },
        {
          "name": "JWT_SECRET",
          "valueFrom": "/prod/api/jwt_secret"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/uzmacloud-api",
          "awslogs-region": "ap-southeast-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": [
          "CMD-SHELL",
          "curl -f http://localhost:3000/health || exit 1"
        ],
        "interval": 30,
        "timeout": 10,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
```

### 4. Create ECS Service
```bash
# Register task definition
aws ecs register-task-definition --cli-input-json file://task-definition.json

# Create service
aws ecs create-service \
  --cluster prod-cluster \
  --service-name prod-api-service \
  --task-definition uzmacloud-prod-api:1 \
  --desired-count 2 \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[subnet-12345,subnet-67890],securityGroups=[sg-12345],assignPublicIp=ENABLED}"
```

---

## 🔄 CI/CD Pipeline

### GitLab CI/CD Configuration
The project includes `.gitlab-ci.yml` with automated deployment:

#### Pipeline Stages
1. **Test** - Run linting and tests
2. **Build** - Build Docker image and push to ECR
3. **Deploy** - Deploy to ECS with zero-downtime
4. **Verify** - Health check verification

#### Key Features
- **Automated builds** on main branch
- **Zero-downtime deployment** with ECS rolling updates
- **Secure secrets management** with AWS SSM
- **Health check verification** after deployment
- **Rollback capability** if deployment fails

#### Manual Deployment
```bash
# Trigger manual deployment
git push origin main

# Or use GitLab UI to manually trigger pipeline
```

---

## 📊 Monitoring & Health Checks

### Application Health Endpoints
```bash
# Basic health check
curl http://your-domain.com/health

# Detailed health check
curl http://your-domain.com/health/detailed
```

### AWS CloudWatch Integration
- **Container logs** automatically sent to CloudWatch
- **Custom metrics** for API performance
- **Alarms** for error rates and response times

### Monitoring Setup
```bash
# Create CloudWatch log group
aws logs create-log-group --log-group-name /ecs/uzmacloud-api

# Set up alarms
aws cloudwatch put-metric-alarm \
  --alarm-name "UzmaCloud-API-HighErrorRate" \
  --alarm-description "High error rate detected" \
  --metric-name "HTTPCode_Target_5XX_Count" \
  --namespace "AWS/ApplicationELB" \
  --statistic Sum \
  --period 300 \
  --threshold 10 \
  --comparison-operator GreaterThanThreshold
```

---

## 🔧 Troubleshooting

### Common Issues

#### Database Connection
```bash
# Test DocumentDB connection
mongo --ssl --host cluster.docdb.amazonaws.com:27017 \
  --username admin --password

# Check connection string format
********************************:port/database?ssl=true&replicaSet=rs0
```

#### Container Issues
```bash
# Check container logs
docker logs uzmacloud-api

# Check ECS service events
aws ecs describe-services --cluster prod-cluster --services prod-api-service
```

#### Health Check Failures
```bash
# Test health endpoint locally
curl -v http://localhost:3000/health

# Check application logs for errors
docker logs uzmacloud-api | grep ERROR
```

### Performance Optimization
- **Enable gzip compression** in production
- **Use connection pooling** for database
- **Implement caching** for frequently accessed data
- **Monitor memory usage** and adjust container resources

---

## 🔒 Security Considerations

### Production Security Checklist
- ✅ Use HTTPS/TLS encryption
- ✅ Store secrets in AWS SSM Parameter Store
- ✅ Enable VPC security groups
- ✅ Use IAM roles with minimal permissions
- ✅ Enable CloudTrail for audit logging
- ✅ Regular security updates for dependencies
- ✅ Input validation and sanitization
- ✅ Rate limiting for API endpoints

### Environment Separation
- **Development** - Local/Docker with test data
- **Staging** - AWS environment with production-like setup
- **Production** - Full AWS stack with monitoring and backups
