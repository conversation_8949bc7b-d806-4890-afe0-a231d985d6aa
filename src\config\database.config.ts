import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MongooseModuleOptions } from '@nestjs/mongoose';

@Injectable()
export class DatabaseConfigService {
  constructor(private configService: ConfigService) {}

  createMongooseOptions(): MongooseModuleOptions {
    const databaseUrl = this.configService.get<string>('DATABASE_URL');
    
    if (!databaseUrl) {
      console.warn('⚠️  DATABASE_URL not provided, using local MongoDB');
      return {
        uri: 'mongodb://localhost:27017/uzmacloud-dev',
      };
    }

    console.log('🔗 Connecting to DocumentDB cluster...');
    
    // DocumentDB specific configuration
    return {
      uri: databaseUrl,
      connectionFactory: (connection) => {
        connection.on('connected', () => {
          console.log('✅ Connected to DocumentDB cluster successfully');
        });
        
        connection.on('error', (error) => {
          console.error('❌ DocumentDB connection error:', error.message);
        });
        
        connection.on('disconnected', () => {
          console.log('📡 Disconnected from DocumentDB cluster');
        });
        
        return connection;
      },
      // DocumentDB connection options
      autoCreate: true, // ✅ ensure schema auto-registration
      autoIndex: true,
      tls: true,
      tlsAllowInvalidCertificates: true, // DocumentDB uses self-signed certificates
      retryWrites: false, // DocumentDB doesn't support retryable writes
      readPreference: 'primary',
      connectTimeoutMS: 30000,
      socketTimeoutMS: 30000,
      serverSelectionTimeoutMS: 30000,
      maxPoolSize: 10,
      minPoolSize: 2,
      maxIdleTimeMS: 30000,
      heartbeatFrequencyMS: 10000,
    };
  }
}