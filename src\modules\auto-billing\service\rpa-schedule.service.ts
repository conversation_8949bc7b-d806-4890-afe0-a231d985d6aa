import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Cron } from '@nestjs/schedule';
import { RpaSchedule, RpaScheduleDocument, RpaTaskStatus, RpaTaskType } from '../schema/rpa-schedule.schema';
import { Billing, BillingDocument } from '../schema/billing.schema';
import { RpaService } from './rpa.service';
import { RpaLog, RpaLogDocument } from '../schema/rpa-logs.schema';

@Injectable()
export class RpaSchedulerService {
  private readonly logger = new Logger(RpaSchedulerService.name);
  private isProcessing = false;

  constructor(
    @InjectModel(RpaSchedule.name) private rpaScheduleModel: Model<RpaScheduleDocument>,
    @InjectModel(RpaLog.name) private rpaLogModel: Model<RpaLogDocument>,
    @InjectModel(Billing.name) private billingModel: Model<BillingDocument>,
    private rpaService: RpaService,
  ) {}

  /**
   * Schedule a single RPA task
   */
  async scheduleTask(
    taskType: RpaTaskType,
    billingId: string,
    priority: number = 1,
    scheduledAt?: Date,
    additionalData?: any
  ): Promise<RpaScheduleDocument> {
    // Find the billing document to get its ID
    const billing = await this.billingModel.findOne({ _id: billingId });
    if (!billing) {
      throw new Error(`Billing with ID ${billingId} not found`);
    }

    // Check for prerequisites
    await this.validateTaskPrerequisites(taskType, billing);

    // Create a new scheduled task
    const task = new this.rpaScheduleModel({
      taskType,
      billingId,
      billing: billing._id,
      status: RpaTaskStatus.QUEUED,
      priority,
      scheduledAt: scheduledAt || new Date(),
      additionalData
    });

    await task.save();
    this.logger.log(`Scheduled new ${taskType} task for billing ${billingId}`);
    
    // Trigger immediate processing if not already running
    if (!this.isProcessing) {
      // Use setTimeout to allow this method to return before processing starts
      setTimeout(() => this.processNextTask(), 0);
    }

    return task;
  }

  /**
   * Keep the original method name but implement sequential logic
   */
  async initiateGenerateSaleOrder(billingId: string): Promise<void> {
    // Find the billing document
    const billing = await this.billingModel.findOne({ _id: billingId });
    if (!billing) {
      throw new Error(`Billing with ID ${billingId} not found`);
    }
  
    // Check status - only schedule if approved
    if (billing.status !== 'ba_approved') {
      throw new Error(`Cannot schedule RPA tasks for billing ${billingId} - not approved`);
    }
  
    // Only schedule the first task (SALE_ORDER)
    await this.scheduleTask(
      RpaTaskType.SALE_ORDER,
      billingId,
      1, // Highest priority
      new Date() // Schedule immediately
    );
  
    this.logger.log(`Started RPA sequence for billing ${billingId} with Sale Order task`);
  }

   /**
   * Keep the original method name but implement sequential logic
   */
   async initiateGeneratePostInvoice(billingId: string): Promise<void> {
    // Find the billing document
    const billing = await this.billingModel.findOne({ _id: billingId });
    if (!billing) {
      throw new Error(`Billing with ID ${billingId} not found`);
    }
  
    // Only schedule the first task (SALE_ORDER)
    await this.scheduleTask(
      RpaTaskType.FINANCE,
      billingId,
      4, // Lower priority
      new Date() // Schedule immediately
    );
  
    this.logger.log(`Started RPA sequence for billing ${billingId} with Post Invoice task`);
  }

  /**
   * Create and schedule the next task in sequence after a callback is received
   */
  async scheduleNextTaskAfterCallback(
    billingId: any,
    completedTaskType: RpaTaskType
  ): Promise<RpaScheduleDocument | null> {
    // Determine the next task type based on the completed task
    let nextTaskType: RpaTaskType | null = null;
    
    switch (completedTaskType) {
      case RpaTaskType.SALE_ORDER:
        nextTaskType = RpaTaskType.DELIVERY_ORDER;
        break;
      case RpaTaskType.DELIVERY_ORDER:
        nextTaskType = RpaTaskType.INVOICE;
        break;
      case RpaTaskType.INVOICE:
        // End of sequence, no next task
        this.logger.log(`RPA sequence completed for billing ${billingId}`);
        return null;
    }

    if (nextTaskType) {
      try {
        // Schedule the next task
        const nextTask = await this.scheduleTask(
          nextTaskType,
          billingId,
          this.getPriorityForTaskType(nextTaskType),
          new Date() // Schedule immediately
        );
        
        this.logger.log(`Scheduled next task ${nextTaskType} for billing ${billingId} after callback`);
        return nextTask;
      } catch (error) {
        this.logger.error(`Failed to schedule next task for billing ${billingId}: ${error.message}`);
        return null;
      }
    }

    return null;
  }

  /**
   * Get priority based on task type
   */
  private getPriorityForTaskType(taskType: RpaTaskType): number {
    switch (taskType) {
      case RpaTaskType.SALE_ORDER:
        return 1;
      case RpaTaskType.DELIVERY_ORDER:
        return 2;
      case RpaTaskType.INVOICE:
        return 3;
      case RpaTaskType.FINANCE:
        return 4;
      default:
        return 10; // Low priority for unknown types
    }
  }

  @Cron('* * * * *') // Run every minute
  async checkAndProcessTasks() {
    if (!this.isProcessing) {
      await this.processNextTask();
    }
  }

  async processNextTask(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    try {
      this.isProcessing = true;
      
      // Find the next task to process - by status, then priority, then scheduled time
      const nextTask = await this.rpaScheduleModel.findOne({
        status: RpaTaskStatus.QUEUED,
        scheduledAt: { $lte: new Date() }
      }).sort({ priority: 1, scheduledAt: 1 });

      if (!nextTask) {
        this.logger.debug('No tasks to process');
        this.isProcessing = false;
        return;
      }

      this.logger.log(`Processing ${nextTask.taskType} task for billing ${nextTask.billingId}`);
      
      // Mark task as in progress
      nextTask.status = RpaTaskStatus.IN_PROGRESS;
      nextTask.startedAt = new Date();
      await nextTask.save();

      // Get the full billing document
      const billing = await this.billingModel.findById(nextTask.billing)
        .populate('department')
        .populate('project');
        
      if (!billing) {
        throw new Error(`Billing document not found for task ${nextTask._id}`);
      }

      // Create log entry for the request
      const log = new this.rpaLogModel({
        taskId: nextTask._id,
        taskType: nextTask.taskType,
        billingId: nextTask.billingId,
        billing: nextTask.billing,
        status: RpaTaskStatus.IN_PROGRESS,
        request: this.sanitizeBillingForLog(billing.toObject()),
      });

      const startTime = Date.now();
      let result;

      try {
        // Process the task based on its type
        switch (nextTask.taskType) {
          case RpaTaskType.SALE_ORDER:
            result = await this.rpaService.generateSaleOrder(billing, nextTask._id);
            break;
            
          case RpaTaskType.DELIVERY_ORDER:
            result = await this.rpaService.generateDeliveryOrder(billing, nextTask._id);
            break;
            
          case RpaTaskType.INVOICE:
            result = await this.rpaService.generateInvoice(billing, nextTask._id);
            break;

          case RpaTaskType.FINANCE:
            result = await this.rpaService.postFinanceInvoice(billing, nextTask._id);
            break;
        }

        // Calculate duration
        const duration = Date.now() - startTime;
        
        // Update log with response
        log.response = result;
        log.duration = duration;
        
        // Task remains IN_PROGRESS until callback is received
        // We don't update the status here since the callback will do that
        
        this.logger.log(`Initiated ${nextTask.taskType} task for billing ${nextTask.billingId}, waiting for callback`);
        
        // The task status will be updated by the callback service
        // The next task will be scheduled by the callback service
      } catch (error) {
        // Update task status to failed
        nextTask.status = RpaTaskStatus.FAILED;
        nextTask.errorMessage = error.message;
        
        // Update log with error
        log.status = RpaTaskStatus.FAILED;
        log.errorMessage = error.message;
        log.duration = Date.now() - startTime;
        
        this.logger.error(`Failed to process ${nextTask.taskType} task: ${error.message}`, error.stack);
        
        // No next task is scheduled since this one failed
        this.logger.log(`Stopping sequence for billing ${nextTask.billingId} due to failure`);
      }

      // Save the log
      await log.save();
      await nextTask.save();

    } catch (error) {
      this.logger.error(`Error in task processing: ${error.message}`, error.stack);
    } finally {
      this.isProcessing = false;
      
      // Check if there are more tasks to process
      const pendingTasksCount = await this.rpaScheduleModel.countDocuments({
        status: RpaTaskStatus.QUEUED,
        scheduledAt: { $lte: new Date() }
      });
      
      if (pendingTasksCount > 0) {
        // Process the next task after a short delay to avoid tight loops
        setTimeout(() => this.processNextTask(), 1000);
      }
    }
  }

  async getTasksByBillingId(billingId: string): Promise<RpaScheduleDocument[]> {
    return this.rpaScheduleModel.find({ billingId }).sort({ createdAt: -1 });
  }

  async getLogsByBillingId(billingId: string): Promise<RpaLogDocument[]> {
    return this.rpaLogModel.find({ billingId }).sort({ createdAt: -1 });
  }

  async getTaskStatus(taskId: string): Promise<RpaScheduleDocument | null> {
    if (!Types.ObjectId.isValid(taskId)) {
      throw new Error(`Invalid task ID: ${taskId}`);
    }
    return this.rpaScheduleModel.findById(new Types.ObjectId(taskId));
  }

  async cancelTask(taskId: string): Promise<RpaScheduleDocument> {
    if (!Types.ObjectId.isValid(taskId)) {
      throw new Error(`Invalid task ID: ${taskId}`);
    }
    
    const task = await this.rpaScheduleModel.findById(new Types.ObjectId(taskId));
    if (!task) {
      throw new Error(`Task with ID ${taskId} not found`);
    }
    
    if (task.status === RpaTaskStatus.QUEUED) {
      task.status = RpaTaskStatus.CANCELLED;
      await task.save();
      this.logger.log(`Cancelled task ${taskId}`);
      return task;
    }
    
    throw new Error(`Cannot cancel task with status ${task.status}`);
  }

  /**
   * Retry a failed task and restart the sequence from that point
   */
  async retryFailedTask(taskId: string): Promise<RpaScheduleDocument> {
    if (!Types.ObjectId.isValid(taskId)) {
      throw new Error(`Invalid task ID: ${taskId}`);
    }
    
    const task = await this.rpaScheduleModel.findById(new Types.ObjectId(taskId));
    if (!task) {
      throw new Error(`Task with ID ${taskId} not found`);
    }
    
    if (task.status === RpaTaskStatus.FAILED) {
      // Create a new task of the same type
      return this.scheduleTask(
        task.taskType,
        task.billingId,
        this.getPriorityForTaskType(task.taskType),
        new Date(),
        task.additionalData
      );
    }
    
    throw new Error(`Cannot retry task with status ${task.status}`);
  }

  private async validateTaskPrerequisites(taskType: RpaTaskType, billing: Billing): Promise<void> {
    switch (taskType) {
      case RpaTaskType.SALE_ORDER:
        if (billing.status !== 'ba_approved') {
          throw new Error('Cannot create Sale Order for unapproved billing advice');
        }
        break;
      
      case RpaTaskType.DELIVERY_ORDER:
        if (!billing.saleOrderId) {
          throw new Error('Sale Order must be created before Delivery Order');
        }
        break;
      
      case RpaTaskType.INVOICE:
        if (!billing.deliveryOrderId) {
          throw new Error('Delivery Order must be created before Invoice');
        }
        break;

      case RpaTaskType.FINANCE:
        if (!billing.invoiceId) {
          throw new Error('Invoice must be created before Finance Invoice Posting');
        }
        break;
    }
  }

  private sanitizeBillingForLog(billingData: any): any {
    // Create a sanitized copy for logs (remove sensitive data if needed)
    const sanitized = { ...billingData };
    // Example: Remove any sensitive fields
    // delete sanitized.sensitiveField;
    return sanitized;
  }
}