import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from 'src/modules/user/schema/user.schema';

@Schema({ timestamps: true, collection: 'project_insight' }) // Custom collection name
export class ProjectInsight extends Document {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  areaOfConcern: string;

  @Prop({
    type: [
      {
        headers: { type: [String], required: true }, // List of headers (e.g., "Description", "Commulative")
        values: { type: [String], required: true },  // Corresponding list of values
      },
    ],
    required: true,
  })
  progressSummary: {
    headers: string[];
    values: string[];
  }[];

  @Prop({ type: Types.ObjectId, ref: 'User' })
  updatedBy: Types.ObjectId | User;
}

export const ProjectInsightSchema = SchemaFactory.createForClass(ProjectInsight);