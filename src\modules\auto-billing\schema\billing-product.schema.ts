import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ProductDocument = Product & Document;

@Schema({ timestamps: true, collection: 'product_master' })
export class Product {
  @Prop({ required: true, unique: true })
  productId: string;

  @Prop({ required: true })
  productName: string;

  @Prop({ required: true })
  departmentId: string;  // Reference to department/company

  @Prop({ required: true })
  projectId: string;  // Reference to department/company

  @Prop()
  description?: string;

  @Prop({ required: true })
  unit: string;

  @Prop({ required: true, default: 0 })
  price: number;

  @Prop({ default: 0 })
  tax: number;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ type: Object })
  additionalDetails?: Record<string, any>;
}

export const ProductSchema = SchemaFactory.createForClass(Product);