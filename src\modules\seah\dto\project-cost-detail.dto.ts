import { IsString, IsN<PERSON>ber, IsOptional } from 'class-validator';

export class CreateProjectCostDetailDto {
  @IsString()
  title: string;

  @IsNumber()
  approvedBudget: number;

  @IsNumber()
  budgetUsed: number;

  @IsNumber()
  invoice: number;

  @IsNumber()
  @IsOptional()
  costSaving?: number;

  @IsString()
  @IsOptional()
  updatedBy?: string; // User ID (optional for creation)
}

export class UpdateProjectCostDetailDto {
  @IsString()
  @IsOptional()
  title?: string;

  @IsNumber()
  @IsOptional()
  approvedBudget?: number;

  @IsNumber()
  @IsOptional()
  budgetUsed?: number;

  @IsNumber()
  @IsOptional()
  invoice?: number;

  @IsNumber()
  @IsOptional()
  costSaving?: number;

  @IsString()
  @IsOptional()
  updatedBy?: string; // User ID (optional for updates)
}
