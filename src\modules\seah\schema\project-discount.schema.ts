import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ProjectDiscountDocument = ProjectDiscount & Document;

@Schema({ timestamps: true, collection: 'project_discount' })
export class ProjectDiscount {
  // Make this field unique to avoid duplicates
  @Prop()
  originalAmount: number;

  @Prop()
  discountAmount: number;

  @Prop()
  convertedAmount: number;

  @Prop()
  convertedDiscountAmount: number;

  @Prop()
  BPNo: string;

  @Prop()
  BPName: string;

  @Prop()
  description: string;

  @Prop()
  referenceNo: string;

  @Prop()
  poNo: string;

  @Prop()
  invoiceNo: string;

  @Prop()
  department: string;

  @Prop()
  project: string;

  @Prop()
  rate: string;

  @Prop()
  status: string;

  @Prop()
  PIC: string;

  @Prop()
  type: string; // PO, LOA, Consultant

  @Prop()
  isExcluded: boolean;
}

export const ProjectDiscountSchema = SchemaFactory.createForClass(ProjectDiscount);
