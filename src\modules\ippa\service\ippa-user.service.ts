import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { User, UserDocument } from 'src/modules/user/schema/user.schema';
import * as XLSX from 'xlsx';
import { IppaUser, IppaUserDocument } from '../schema/ippa-user.schema';
import { Department } from 'src/modules/user/schema/department.schema';
import { PopulatedIppaUser } from '../dto/ippa-user.interfaces';
import { SecurityService } from 'src/modules/user/service/security.service';

export interface IppaUserExcelData {
  username: string;
  fullName: string;
  email: string;
  company: string;
  group: string;
  businessUnit: string;
  employeeId: string;
  employeeGrade: string;
  position: string;
  positionLevel: string;
  division: string;
  department: string;
  departmentCode: string;
  departmentId: string;
  section: string;
  lineManagerEmail: string; // Email from Excel, will be converted to ObjectId
  workLocation: string;
  joinedDate: string | Date;
  isActive?: boolean;
}

export interface UserCreationResult {
  email: string;
  userId: Types.ObjectId;
  isNewUser: boolean;
  error?: string;
}

@Injectable()
export class IppaUserService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(IppaUser.name) private ippaUserModel: Model<IppaUserDocument>,
    @InjectModel(Department.name) private departmentModel: Model<Department>,
    private securityService: SecurityService,
  ) {}

  // Step 1: Process Excel file and create missing users
  async processExcelAndCreateMissingUsers(
    file: Express.Multer.File,
    createdBy: Types.ObjectId,
    defaultRole: Types.ObjectId, // You'll need to provide a default role
  ): Promise<{
    createdUsers: UserCreationResult[];
    failedUsers: UserCreationResult[];
    totalProcessed: number;
  }> {
    try {
      // Parse Excel file
      const workbook = XLSX.read(file.buffer);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const excelData: IppaUserExcelData[] =
        XLSX.utils.sheet_to_json(worksheet);

      const createdUsers: UserCreationResult[] = [];
      const failedUsers: UserCreationResult[] = [];

      // Get all unique emails from Excel (including line manager emails)
      const allEmails = new Set<string>();
      excelData.forEach((row) => {
        // console.log("row ", row)
        allEmails.add(row.email.toLowerCase());
        // if (row.lineManagerEmail) {
        //   allEmails.add(row.lineManagerEmail.toLowerCase());
        // }
      });

      // Check which users already exist
      const existingUsers = await this.userModel
        .find({
          email: { $in: Array.from(allEmails) },
        })
        .select('email _id');

      const existingEmailMap = new Map(
        existingUsers.map((user) => [user.email.toLowerCase(), user._id]),
      );

      // Process each unique email
      for (const email of allEmails) {
        if (existingEmailMap.has(email)) {
          createdUsers.push({
            email,
            userId: existingEmailMap.get(email)!,
            isNewUser: false,
          });
        } else {
          try {
            // Find the user data from Excel for this email
            const userData = excelData.find(
              (row) => row.email.toLowerCase() === email,
              //   || row.lineManagerEmail?.toLowerCase() === email
            );

            if (userData && userData.email.toLowerCase() === email) {
              // Create new user with data from Excel
              const newUser = await this.createUserFromExcelData(
                userData,
                defaultRole,
                createdBy,
              );
              createdUsers.push({
                email,
                userId: newUser._id,
                isNewUser: true,
              });
            } else {
              console.log('something wrong ', email);
              // This is a line manager email without full data - create minimal user
              //   const newUser = await this.createMinimalUser(email, defaultRole, createdBy);
              //   createdUsers.push({
              //     email,
              //     userId: newUser._id,
              //     isNewUser: true,
              //   });
            }
          } catch (error) {
            failedUsers.push({
              email,
              userId: new Types.ObjectId(),
              isNewUser: false,
              error: error.message,
            });
          }
        }
      }

      return {
        createdUsers,
        failedUsers,
        totalProcessed: allEmails.size,
      };
    } catch (error) {
      throw new BadRequestException(
        `Error processing Excel file: ${error.message}`,
      );
    }
  }

  // async createIppaUsersFromExcel(file: Express.Multer.File): Promise<{
  //   createdIppaUsers: IppaUserDocument[];
  //   failedIppaUsers: { email: string; error: string }[];
  // }> {
  //   try {
  //     // Parse Excel file again
  //     const workbook = XLSX.read(file.buffer);
  //     const sheetName = workbook.SheetNames[0];
  //     const worksheet = workbook.Sheets[sheetName];
  //     const excelData: IppaUserExcelData[] =
  //       XLSX.utils.sheet_to_json(worksheet);

  //     const createdIppaUsers: IppaUserDocument[] = [];
  //     const failedIppaUsers: { email: string; error: string }[] = [];

  //     // Get all user emails (keep original case for reference)
  //     const allEmails: any = [];
  //     excelData.forEach((row) => {
  //       allEmails.push(row.email.trim());
  //     });

  //     // Get existing users by email (case-insensitive search using regex)
  //     const existingUsers = await this.userModel
  //       .find({
  //         email: {
  //           $in: allEmails.map(
  //             (email) =>
  //               new RegExp(
  //                 `^${email.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`,
  //                 'i',
  //               ),
  //           ),
  //         },
  //       })
  //       .select('email _id');

  //     // Create case-insensitive email map
  //     const emailToUserIdMap = new Map();
  //     existingUsers.forEach((user) => {
  //       emailToUserIdMap.set(user.email.toLowerCase().trim(), user._id);
  //     });

  //     // Get all users for line manager name matching (case-insensitive)
  //     const allUsers = await this.userModel
  //       .find({})
  //       .select('_id fullName email')
  //       .lean();

  //     // Get all departments for matching (case-insensitive)
  //     const allDepartments = await this.departmentModel
  //       .find({ isActive: true })
  //       .select('_id code description')
  //       .lean();

  //     // Helper function to normalize text for comparison
  //     const normalizeText = (text: string): string => {
  //       return text ? text.toLowerCase().trim().replace(/\s+/g, ' ') : '';
  //     };

  //     // Helper function to find best matching user by name (case-insensitive)
  //     const findBestMatchingUser = (lineManagerName: string) => {
  //       if (!lineManagerName?.trim()) return null;

  //       const searchName = normalizeText(lineManagerName);
  //       let bestMatch;
  //       let bestScore = 0;

  //       for (const user of allUsers) {
  //         const scores: any[] = [];

  //         // Check full name match
  //         if (user.fullName) {
  //           const fullName = normalizeText(user.fullName);
  //           if (fullName === searchName) return user; // Exact match
  //           if (
  //             fullName.includes(searchName) ||
  //             searchName.includes(fullName)
  //           ) {
  //             scores.push(0.8);
  //           }
  //         }

  //         // Check individual name parts
  //         const nameParts = searchName.split(/\s+/);
  //         const userNameParts = [
  //           normalizeText(user.fullName || ''),
  //           normalizeText(user.email?.split('@')[0] || ''),
  //         ].filter((part) => part.length > 0);

  //         let partialMatches = 0;
  //         for (const part of nameParts) {
  //           for (const userPart of userNameParts) {
  //             if (userPart.includes(part) || part.includes(userPart)) {
  //               partialMatches++;
  //               break;
  //             }
  //           }
  //         }

  //         if (partialMatches > 0) {
  //           scores.push((partialMatches / nameParts.length) * 0.6);
  //         }

  //         // Calculate best score for this user
  //         const userScore = Math.max(...scores);
  //         if (userScore > bestScore && userScore > 0.5) {
  //           // Minimum threshold
  //           bestScore = userScore;
  //           bestMatch = user;
  //         }
  //       }

  //       return bestMatch;
  //     };

  //     // Helper function to parse date from various formats
  //     const parseDate = (dateValue: any): Date | null => {
  //       if (!dateValue) return null;

  //       // If it's already a Date object
  //       if (dateValue instanceof Date) {
  //         return isNaN(dateValue.getTime()) ? null : dateValue;
  //       }

  //       // If it's a number (Excel serial date)
  //       if (typeof dateValue === 'number') {
  //         // Excel dates start from 1900-01-01, but JavaScript Date starts from 1970-01-01
  //         // Excel serial date 1 = 1900-01-01
  //         const excelEpoch = new Date(1900, 0, 1);
  //         const jsDate = new Date(
  //           excelEpoch.getTime() + (dateValue - 1) * 24 * 60 * 60 * 1000,
  //         );
  //         return isNaN(jsDate.getTime()) ? null : jsDate;
  //       }

  //       // If it's a string, try various formats
  //       if (typeof dateValue === 'string') {
  //         const dateStr = dateValue.trim();

  //         // Try parsing with different separators and formats
  //         const formats = [
  //           // DD/MM/YYYY, DD/M/YY, D/MM/YYYY, D/M/YY
  //           /^(\d{1,2})\/(\d{1,2})\/(\d{2,4})$/,
  //           // DD-MM-YYYY, DD-M-YY, D-MM-YYYY, D-M-YY
  //           /^(\d{1,2})-(\d{1,2})-(\d{2,4})$/,
  //           // DD.MM.YYYY, DD.M.YY, D.MM.YYYY, D.M.YY
  //           /^(\d{1,2})\.(\d{1,2})\.(\d{2,4})$/,
  //         ];

  //         for (const format of formats) {
  //           const match = dateStr.match(format);
  //           if (match) {
  //             let [, day, month, year] = match;

  //             // Handle 2-digit years
  //             if (year.length === 2) {
  //               const yearNum = parseInt(year);
  //               // Assume years 00-30 are 2000-2030, 31-99 are 1931-1999
  //               year = yearNum <= 30 ? `20${year}` : `19${year}`;
  //             }

  //             // Create date (month is 0-indexed in JavaScript)
  //             const parsedDate = new Date(
  //               parseInt(year),
  //               parseInt(month) - 1,
  //               parseInt(day),
  //             );

  //             // Validate the date
  //             if (
  //               !isNaN(parsedDate.getTime()) &&
  //               parsedDate.getFullYear() == parseInt(year) &&
  //               parsedDate.getMonth() == parseInt(month) - 1 &&
  //               parsedDate.getDate() == parseInt(day)
  //             ) {
  //               return parsedDate;
  //             }
  //           }
  //         }

  //         // Try native Date parsing as fallback
  //         const nativeDate = new Date(dateStr);
  //         if (!isNaN(nativeDate.getTime())) {
  //           return nativeDate;
  //         }
  //       }

  //       return null;
  //     };

  //     // Helper function to find department by code or description (case-insensitive)
  //     const findDepartmentByCodeOrName = (
  //       departmentCode: string,
  //       departmentName: string,
  //     ) => {
  //       // First try exact code match
  //       if (departmentCode) {
  //         const deptByCode = allDepartments.find(
  //           (dept) =>
  //             normalizeText(dept.code) === normalizeText(departmentCode),
  //         );
  //         if (deptByCode) return deptByCode;
  //       }

  //       // Then try exact description match
  //       if (departmentName) {
  //         const deptByName = allDepartments.find(
  //           (dept) =>
  //             normalizeText(dept.description) === normalizeText(departmentName),
  //         );
  //         if (deptByName) return deptByName;
  //       }

  //       // Finally try partial matches
  //       if (departmentName) {
  //         const normalizedDeptName = normalizeText(departmentName);
  //         const deptByPartialName = allDepartments.find((dept) => {
  //           const normalizedDesc = normalizeText(dept.description);
  //           return (
  //             normalizedDesc.includes(normalizedDeptName) ||
  //             normalizedDeptName.includes(normalizedDesc)
  //           );
  //         });
  //         if (deptByPartialName) return deptByPartialName;
  //       }

  //       return null;
  //     };

  //     // Create IPPA users
  //     for (const rowData of excelData) {
  //       try {
  //         const userId = emailToUserIdMap.get(
  //           rowData.email.toLowerCase().trim(),
  //         );

  //         if (!userId) {
  //           failedIppaUsers.push({
  //             email: rowData.email,
  //             error: 'User not found in user table',
  //           });
  //           continue;
  //         }

  //         let lineManagerId = undefined;
  //         if (rowData.lineManagerEmail) {
  //           // Note: Changed from lineManagerEmail to lineManagerName if needed
  //           const matchingUser = findBestMatchingUser(rowData.lineManagerEmail);
  //           if (matchingUser) {
  //             lineManagerId = matchingUser._id;
  //           } else {
  //             failedIppaUsers.push({
  //               email: rowData.email,
  //               error: `Line manager '${rowData.lineManagerEmail}' not found in user table`,
  //             });
  //             continue;
  //           }
  //         }

  //         // Parse joined date
  //         const parsedJoinedDate = parseDate(rowData.joinedDate);
  //         if (!parsedJoinedDate) {
  //           failedIppaUsers.push({
  //             email: rowData.email,
  //             error: `Invalid joined date format: ${rowData.joinedDate}`,
  //           });
  //           continue;
  //         }

  //         // Find department reference and get departmentId
  //         let departmentRef = undefined;
  //         let departmentId = rowData.departmentId; // Use provided departmentId if available

  //         if (rowData.departmentCode || rowData.department) {
  //           const matchingDepartment = findDepartmentByCodeOrName(
  //             rowData.departmentCode,
  //             rowData.department,
  //           );
  //           if (matchingDepartment) {
  //             departmentRef = matchingDepartment._id;
  //             // If departmentId is not provided, use the department code as fallback
  //             if (!departmentId) {
  //               departmentId = matchingDepartment.code;
  //             }
  //           }
  //         }

  //         // If still no departmentId, use departmentCode or generate one
  //         if (!departmentId) {
  //           departmentId = rowData.departmentCode || 'UNKNOWN';
  //         }

  //         const ippaUser = new this.ippaUserModel({
  //           username: rowData.email.split('@')[0],
  //           fullName: rowData.fullName,
  //           email: rowData.email,
  //           company: rowData.company,
  //           group: rowData.group,
  //           businessUnit: rowData.businessUnit,
  //           employeeId: rowData.employeeId,
  //           employeeGrade: rowData.employeeGrade,
  //           position: rowData.position,
  //           positionLevel: rowData.positionLevel,
  //           division: rowData.division,
  //           department: rowData.department, // Keep original string value
  //           departmentRef: departmentRef, // Add reference to Department collection
  //           departmentCode: rowData.departmentCode,
  //           departmentId: departmentId, // Ensure this is always provided
  //           section: rowData.section,
  //           lineManager: lineManagerId,
  //           workLocation: rowData.workLocation,
  //           joinedDate: parsedJoinedDate, // Use parsed date
  //           isActive: rowData.isActive ?? true,
  //         });

  //         const savedIppaUser = await ippaUser.save();
  //         createdIppaUsers.push(savedIppaUser);
  //       } catch (error) {
  //         failedIppaUsers.push({
  //           email: rowData.email,
  //           error: error.message,
  //         });
  //       }
  //     }

  //     return {
  //       createdIppaUsers,
  //       failedIppaUsers,
  //     };
  //   } catch (error) {
  //     throw new BadRequestException(
  //       `Error creating IPPA users: ${error.message}`,
  //     );
  //   }
  // }

  async createIppaUsersFromExcel(file: Express.Multer.File): Promise<{
    createdIppaUsers: IppaUserDocument[];
    failedIppaUsers: { email: string; error: string }[];
  }> {
    try {
      // Parse Excel file again
      const workbook = XLSX.read(file.buffer);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const excelData: IppaUserExcelData[] =
        XLSX.utils.sheet_to_json(worksheet);

      const createdIppaUsers: IppaUserDocument[] = [];
      const failedIppaUsers: { email: string; error: string }[] = [];

      // Get all user emails (keep original case for reference)
      const allEmails: any = [];
      excelData.forEach((row) => {
        allEmails.push(row.email.trim());
      });

      // Get existing users by email (case-insensitive search using regex)
      const existingUsers = await this.userModel
        .find({
          email: {
            $in: allEmails.map(
              (email) =>
                new RegExp(
                  `^${email.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`,
                  'i',
                ),
            ),
          },
        })
        .select('email _id');

      // Create case-insensitive email map
      const emailToUserIdMap = new Map();
      existingUsers.forEach((user) => {
        emailToUserIdMap.set(user.email.toLowerCase().trim(), user._id);
      });

      // Get all departments for matching (case-insensitive)
      const allDepartments = await this.departmentModel
        .find({ isActive: true })
        .select('_id code description')
        .lean();

      // Helper function to normalize text for comparison
      const normalizeText = (text: string): string => {
        return text ? text.toLowerCase().trim().replace(/\s+/g, ' ') : '';
      };

      // Helper function to find line manager by email
      const findLineManagerByEmail = async (lineManagerEmail: string) => {
        if (!lineManagerEmail?.trim()) return null;

        const normalizedEmail = lineManagerEmail.toLowerCase().trim();

        // Check if email is "BOD" (case-insensitive)
        if (normalizedEmail === 'bod') return null;

        // Find user by email (return single user)
        const user = await this.userModel.findOne({ email: normalizedEmail }).select('_id');
        return user || null;
      };


      // Helper function to parse date from various formats
      const parseDate = (dateValue: any): Date | null => {
        if (!dateValue) return null;

        let day: number, month: number, year: number;

        // Excel serial date
        if (typeof dateValue === 'number') {
          const jsDate = XLSX.SSF.parse_date_code(dateValue); // Parse without timezone
          if (!jsDate) return null;
          year = jsDate.y;
          month = jsDate.m;
          day = jsDate.d;
        }

        // String date formats
        else if (typeof dateValue === 'string') {
          const match = dateValue
            .trim()
            .match(/^(\d{1,2})[\/\-.](\d{1,2})[\/\-.](\d{2,4})$/);
          if (!match) return null;
          day = parseInt(match[1], 10);
          month = parseInt(match[2], 10);
          year = parseInt(match[3], 10);
          if (year < 100) year += year <= 30 ? 2000 : 1900;
        }

        // Already a Date object
        else if (dateValue instanceof Date) {
          return new Date(
            Date.UTC(
              dateValue.getFullYear(),
              dateValue.getMonth(),
              dateValue.getDate(),
            ),
          );
        } else {
          return null;
        }

        // Always return as UTC midnight
        return new Date(Date.UTC(year, month - 1, day));
      };

      // Helper function to find department by code or description (case-insensitive)
      const findDepartmentByCodeOrName = (
        departmentCode: string,
        departmentName: string,
      ) => {
        // First try exact code match
        if (departmentCode) {
          const deptByCode = allDepartments.find(
            (dept) =>
              normalizeText(dept.code) === normalizeText(departmentCode),
          );
          if (deptByCode) return deptByCode;
        }

        // Then try exact description match
        if (departmentName) {
          const deptByName = allDepartments.find(
            (dept) =>
              normalizeText(dept.description) === normalizeText(departmentName),
          );
          if (deptByName) return deptByName;
        }

        // Finally try partial matches
        if (departmentName) {
          const normalizedDeptName = normalizeText(departmentName);
          const deptByPartialName = allDepartments.find((dept) => {
            const normalizedDesc = normalizeText(dept.description);
            return (
              normalizedDesc.includes(normalizedDeptName) ||
              normalizedDeptName.includes(normalizedDesc)
            );
          });
          if (deptByPartialName) return deptByPartialName;
        }

        return null;
      };

      // Create IPPA users
      for (const rowData of excelData) {
        try {
          const userId = emailToUserIdMap.get(
            rowData.email.toLowerCase().trim(),
          );

          if (!userId) {
            failedIppaUsers.push({
              email: rowData.email,
              error: 'User not found in user table',
            });
            continue;
          }

          // Handle line manager email - direct email matching
          let lineManagerId;
          // console.log("manager email ", rowData.lineManagerEmail)
          if (rowData.lineManagerEmail) {
            lineManagerId = await findLineManagerByEmail(rowData.lineManagerEmail);
            // console.log("lineManagerId ", lineManagerId)

            // Note: We don't add to failedIppaUsers if line manager is not found
            // as BOD or missing line managers are valid cases
          }

          // Parse joined date
          const parsedJoinedDate = parseDate(rowData.joinedDate);
          if (!parsedJoinedDate) {
            failedIppaUsers.push({
              email: rowData.email,
              error: `Invalid joined date format: ${rowData.joinedDate}`,
            });
            continue;
          }

          // Find department reference and get departmentId
          let departmentRef = undefined;
          let departmentId = rowData.departmentId; // Use provided departmentId if available

          if (rowData.departmentCode || rowData.department) {
            const matchingDepartment = findDepartmentByCodeOrName(
              rowData.departmentCode,
              rowData.department,
            );
            if (matchingDepartment) {
              departmentRef = matchingDepartment._id;
              // If departmentId is not provided, use the department code as fallback
              if (!departmentId) {
                departmentId = matchingDepartment.code;
              }
            }
          }

          // If still no departmentId, use departmentCode or generate one
          if (!departmentId) {
            departmentId = rowData.departmentCode || 'UNKNOWN';
          }

          const ippaUser = new this.ippaUserModel({
            username: rowData.email.split('@')[0],
            fullName: rowData.fullName,
            email: rowData.email,
            company: rowData.company,
            group: rowData.group,
            businessUnit: rowData.businessUnit,
            employeeId: rowData.employeeId,
            employeeGrade: rowData.employeeGrade,
            position: rowData.position,
            positionLevel: rowData.positionLevel,
            division: rowData.division,
            department: rowData.department, // Keep original string value
            departmentRef: departmentRef, // Add reference to Department collection
            departmentCode: rowData.departmentCode,
            departmentId: departmentId, // Ensure this is always provided
            section: rowData.section,
            lineManager: lineManagerId, // Will be undefined if BOD, null, or empty
            workLocation: rowData.workLocation,
            joinedDate: parsedJoinedDate, // Use parsed date
            isActive: rowData.isActive ?? true,
          });

          const savedIppaUser = await ippaUser.save();
          createdIppaUsers.push(savedIppaUser);
        } catch (error) {
          failedIppaUsers.push({
            email: rowData.email,
            error: error.message,
          });
        }
      }

      return {
        createdIppaUsers,
        failedIppaUsers,
      };
    } catch (error) {
      throw new BadRequestException(
        `Error creating IPPA users: ${error.message}`,
      );
    }
  }

  private async createUserFromExcelData(
    userData: IppaUserExcelData,
    defaultRole: Types.ObjectId,
    createdBy: Types.ObjectId,
  ): Promise<UserDocument> {
    const newUser = new this.userModel({
      username: userData.email.split('@')[0],
      fullName: userData.fullName,
      email: userData.email,
      faceId: `temp_${userData.email}`, // Temporary faceId
      faceImage:
        'https://face-auth-images.s3.ap-southeast-1.amazonaws.com/user.png', // Empty for now
      department: userData.department,
      designation: userData.position,
      loginMethod: 'password', // Default to password
      role: defaultRole,
      roles: [defaultRole],
      createdBy,
    });

    return await newUser.save();
  }

  private async createMinimalUser(
    email: string,
    defaultRole: Types.ObjectId,
    createdBy: Types.ObjectId,
  ): Promise<UserDocument> {
    const username = email.split('@')[0];
    const newUser = new this.userModel({
      username,
      fullName: username, // Use username as fallback
      email,
      faceId: `temp_${email}`,
      faceImage: '',
      department: 'Unknown',
      designation: 'Unknown',
      loginMethod: 'password',
      role: defaultRole,
      roles: [defaultRole],
      createdBy,
    });

    return await newUser.save();
  }

  // Helper method to check if email exists
  async checkEmailExists(email: string): Promise<boolean> {
    const user = await this.userModel.findOne({ email: email.toLowerCase() });
    return !!user;
  }

  // Get user by email
  async getUserByEmail(email: string): Promise<UserDocument | null> {
    return await this.userModel.findOne({ email: email.toLowerCase() });
  }

  /**
   * Get IPPA user by email (case-insensitive)
   * @param email - User email address
   * @returns IPPA user with populated references
   */
  async getIppaUserByEmail(email: string): Promise<any> {
    try {
      // Case-insensitive email search with populated references
      const ippaUser = await this.ippaUserModel
        .findOne({
          email: new RegExp(
            `^${email.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`,
            'i',
          ),
        })
        .populate({
          path: 'lineManager',
          select: '_id fullName email designation',
          model: 'User',
        })
        .populate({
          path: 'departmentRef',
          select: '_id code description manager hod',
          model: 'Department',
          populate: [
            {
              path: 'manager',
              select: '_id fullName email designation',
              model: 'User',
            },
            {
              path: 'hod',
              select: '_id fullName email designation',
              model: 'User',
            },
          ],
        })
        .lean()
        .exec();

      return ippaUser;
      // console.log("IPPA ", ippaUser)
      return { data: this.securityService.encrypt(ippaUser) };
    } catch (error) {
      throw new Error(`Error fetching IPPA user: ${error.message}`);
    }
  }

  /**
   * Search IPPA users by various criteria
   * @param searchCriteria - Search parameters
   * @returns Array of matching IPPA users
   */
  async searchIppaUsers(searchCriteria: {
    email?: string;
    fullName?: string;
    employeeId?: string;
    department?: string;
    company?: string;
    isActive?: boolean;
    limit?: number;
    skip?: number;
  }): Promise<{
    users: IppaUserDocument[];
    total: number;
  }> {
    try {
      const query: any = {};

      // Build search query
      if (searchCriteria.email) {
        query.email = new RegExp(
          searchCriteria.email.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
          'i',
        );
      }

      if (searchCriteria.fullName) {
        query.fullName = new RegExp(
          searchCriteria.fullName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
          'i',
        );
      }

      if (searchCriteria.employeeId) {
        query.employeeId = searchCriteria.employeeId;
      }

      if (searchCriteria.department) {
        query.department = new RegExp(
          searchCriteria.department.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
          'i',
        );
      }

      if (searchCriteria.company) {
        query.company = new RegExp(
          searchCriteria.company.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
          'i',
        );
      }

      if (searchCriteria.isActive !== undefined) {
        query.isActive = searchCriteria.isActive;
      }

      // Get total count
      const total = await this.ippaUserModel.countDocuments(query);

      // Get paginated results
      const users = await this.ippaUserModel
        .find(query)
        .populate({
          path: 'lineManager',
          select: '_id fullName email',
          model: 'User',
        })
        .populate({
          path: 'departmentRef',
          select: '_id code description',
          model: 'Department',
        })
        .limit(searchCriteria.limit || 50)
        .skip(searchCriteria.skip || 0)
        .sort({ createdAt: -1 })
        .lean()
        .exec();

      return { users, total };
    } catch (error) {
      throw new Error(`Error searching IPPA users: ${error.message}`);
    }
  }
}
