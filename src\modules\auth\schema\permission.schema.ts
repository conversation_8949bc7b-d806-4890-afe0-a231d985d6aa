import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type PermissionDocument = Permission & Document;

@Schema({ timestamps: true,  collection: 'permission' })
export class Permission {
  @Prop({ required: true })
  action: string;  // Action name (e.g., read, write, delete)

  @Prop({ required: true })
  resource: string;  // Resource name (e.g., User, PaymentAdvice)
}

export const PermissionSchema = SchemaFactory.createForClass(Permission);
