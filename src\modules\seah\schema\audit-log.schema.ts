import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from 'src/modules/user/schema/user.schema';

@Schema({ timestamps: true, collection: 'audit_log' }) // Custom collection name
export class AuditLog extends Document {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  description: string;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  updatedBy: Types.ObjectId | User;

  @Prop({ required: true })
  device: string;

  @Prop({ required: true })
  location: string;
}

export const AuditLogSchema = SchemaFactory.createForClass(AuditLog);
