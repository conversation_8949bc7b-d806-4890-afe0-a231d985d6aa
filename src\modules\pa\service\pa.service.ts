import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ApproveRejectWorkflowDto } from '../dto/workflow.dto';
import { PaymentAdvice, PaymentAdviceDocument } from '../schema/pa-main.schema';
import {
  ApprovalWorkflow,
  ApprovalWorkflowDocument,
} from '../schema/pa-workflow.schema';
import { UpdateBankReceiptsDto, UpdateBankReceiptsRpaDto } from '../dto/update-receipt.dto';
import { LOA, LoaDocument } from '../schema/pa-loa.schema';
import { PASerialNumberService } from './pa-serial-no.service';
import * as ExcelJS from 'exceljs';
import { Response } from 'express';
import {
  PaymentAdviceMessage,
  PaymentAdviceMessageDocument,
} from '../schema/pa-message.schema';
import { User, UserDocument } from 'src/modules/user/schema/user.schema';
import { SecurityService } from 'src/modules/user/service/security.service';
import {
  EmailScheduler,
  EmailSchedulerDocument,
} from 'src/modules/common/schema/pa-email.schema';

@Injectable()
export class PaymentAdviceService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(LOA.name) private loaModel: Model<LoaDocument>,
    @InjectModel(PaymentAdvice.name)
    private paymentAdviceModel: Model<PaymentAdviceDocument>,
    @InjectModel(ApprovalWorkflow.name)
    private approvalWorkflowModel: Model<ApprovalWorkflowDocument>,
    @InjectModel(EmailScheduler.name)
    private emailSchedulerModel: Model<EmailSchedulerDocument>,
    @InjectModel(PaymentAdviceMessage.name)
    private messageModel: Model<PaymentAdviceMessageDocument>,
    private readonly serialNumberService: PASerialNumberService,
    private readonly securityService: SecurityService,
  ) {}

  // Create a new payment advice
  async createPaymentAdvice(encryptedData: any): Promise<any> {
    const createDto = this.securityService.decrypt(encryptedData);

    const prefix = 'UZMA-EPA';
    const serialNumber =
      await this.serialNumberService.getNextSerialNumber(prefix);

    // const paymentAdvice = new this.paymentAdviceModel(createDto);

    const paymentAdvice = new this.paymentAdviceModel({
      ...createDto,
      serialNumber,
    });
    const savedAdvice = await paymentAdvice.save();

    const loa = await this.loaModel
      .findOne({
        departmentId: new Types.ObjectId(createDto.departmentCode),
        currency: createDto.currency.toUpperCase(),
        identifier: createDto.identifier,
      })
      .populate({
        path: 'preparers',
        model: 'User',
        select: 'name email fullName designation faceImage',
      }) // Populate preparers (userId array)
      .populate({
        path: 'finance',
        model: 'User',
        select: 'name email fullName designation faceImage',
      }) // Populate finance (userId array)
      .populate({
        path: 'approvalLimits.approvers', // Populate approvers inside each approvalLimit
        model: 'User', // Specify the model explicitly if necessary
        select: 'name email fullName designation faceImage', // Select the fields you need from the User model
      })
      .exec();

    if (!loa) {
      throw new NotFoundException('LOA not found');
    }
    // console.log("LOA ", loa);

    // Define the fixed participants (Finance)
    const fixedParticipants = [
      {
        userId: new Types.ObjectId(loa.finance[0]._id), // Replace with actual CFO user ID
        approvalType: '(Payment by)',
        role: 'Payment by',
        status: 'pending',
        assignedAt: null,
      },
    ];

    var preparedByDate = new Date();

    // Combine dynamic and fixed participants
    const workflowParticipants = [
      ...createDto.workflowParticipants.map((participant, index) => ({
        userId: new Types.ObjectId(participant.userId), // Ensure userId is ObjectId
        stepNumber: index + 1,
        approvalType: participant.approvalType,
        role: participant.approvalType, // Adjust role if needed (e.g., pass it in DTO or handle it differently)
        status: 'pending',
        // Set assignedAt only for the first (current) step
        assignedAt: index === 0 ? preparedByDate : null, // Only assign the current step
        approvalDate: index == 0 ? preparedByDate : null,
      })),
      // Add the fixed participants at the end with no assignedAt initially
      ...fixedParticipants.map((participant, index) => ({
        ...participant,
        stepNumber: createDto.workflowParticipants.length + index + 1, // Adjust step numbers accordingly
        assignedAt: null, // Will be assigned when the current step reaches them
      })),
    ];

    // Create the workflow with the participants
    const workflow = new this.approvalWorkflowModel({
      paymentAdviceId: savedAdvice._id,
      steps: workflowParticipants,
      currentStep: 2, // Start with the first step
      isCompleted: false,
    });

    workflow.steps[0].comments = createDto.purpose;
    await workflow.save();

    // Preparer Email
    const participantUserId = new Types.ObjectId(
      createDto.workflowParticipants[0].userId,
    );
    const preparer = loa.preparers.find((prep: any) =>
      prep._id.equals(participantUserId),
    ) as any;

    if (preparer) {
      const email = preparer.email;
      const name = preparer.fullName;
      // console.log(`Found preparer with email: ${email}`);

      // Save email details to the scheduler
      const emailScheduler = new this.emailSchedulerModel({
        to: email,
        subject: `Payment Advice Created: ${serialNumber}`,
        body: `<p>Dear ${name}, <br/>Your payment advice - ${serialNumber} has been created succesfully.<br/><br/>Vendor Name - ${createDto.vendorName}<br/>Amount - <b>${createDto.currency} ${createDto.amount}</b><br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
      });
      await emailScheduler.save();
    }

    // First Approver Email
    if (loa && loa.approvalLimits && loa.approvalLimits.length > 0) {
      const firstApprover = loa.approvalLimits[0]?.approvers[0] as any;
      // console.log("First Approver ", firstApprover);
      if (firstApprover) {
        // Save email details to the scheduler
        const emailScheduler = new this.emailSchedulerModel({
          // to: '<EMAIL>',
          to: firstApprover.email,
          subject: `Payment Advice to be signed: ${serialNumber}`,
          body: `<p>Dear ${firstApprover.fullName}, <br/>You have new payment advice - ${serialNumber} to be signed.<br/><br/>Vendor Name - ${createDto.vendorName}<br/>Amount - <b>${createDto.currency} ${createDto.amount.toFixed(2)}</b><br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
        });
        await emailScheduler.save();
      } else {
        console.log('No approvers found in the first approval limit.');
      }
    } else {
      console.log('No approval limits found.');
    }

    return { data: this.securityService.encrypt(savedAdvice) };
  }

  // async getPaymentAdviceList(encryptedData: any): Promise<any> {
  //   const decryptedData = this.securityService.decrypt(encryptedData);

  //   const { status, departmentId, assignedUserId, page = 1, limit = 10 } = decryptedData;

  //   const filters: any = {};
  //   const skip = (page - 1) * limit;

  //   if (departmentId) {
  //     filters.departmentCode = departmentId;
  //   } else if (assignedUserId) {
  //     const user = await this.userModel.findById(assignedUserId).select('departments').exec();
  //     if (user && user.departments && user.departments.length > 0) {
  //       filters.departmentCode = { $in: user.departments };
  //     }
  //   }

  //   let relevantPaymentAdviceIds: Types.ObjectId[] = [];

  //   if (status === 'to-sign' && assignedUserId) {
  //     const workflows = await this.approvalWorkflowModel
  //       .find({
  //         isCompleted: false,
  //         'steps.userId': new Types.ObjectId(assignedUserId),
  //         'steps.status': 'pending',
  //         $expr: {
  //           $eq: [
  //             '$currentStep',
  //             {
  //               $add: [
  //                 { $indexOfArray: ['$steps.userId', new Types.ObjectId(assignedUserId)] },
  //                 1,
  //               ],
  //             },
  //           ],
  //         },
  //       })
  //       .select('paymentAdviceId')
  //       .exec();

  //     relevantPaymentAdviceIds = workflows.map((workflow) => workflow.paymentAdviceId);
  //     filters._id = { $in: relevantPaymentAdviceIds };
  //     filters.status = 'submitted';
  //   } else if (status !== 'to-sign') {
  //     filters.status = status;
  //   }

  //   const paymentAdvices = await this.paymentAdviceModel
  //     .find(filters)
  //     .sort({ updatedAt: -1 })
  //     .skip(skip)
  //     .limit(Number(limit))
  //     .populate('departmentCode')
  //     .populate('createdBy')
  //     .exec();

  //   const paymentAdviceIds = paymentAdvices.map((advice) => advice._id);
  //   const workflows = await this.approvalWorkflowModel
  //     .find({ paymentAdviceId: { $in: paymentAdviceIds } })
  //     .populate({
  //       path: 'steps.userId',
  //       select: 'fullName email',
  //     })
  //     .exec();

  //   const finalPaymentAdvices = paymentAdvices.map((advice) => {
  //     const relatedWorkflow = workflows.find((workflow) => workflow.paymentAdviceId.toString() === advice._id.toString());
  //     return {
  //       ...advice.toObject(),
  //       workflowSteps: relatedWorkflow ? relatedWorkflow.steps : [],
  //       currentStep: relatedWorkflow ? relatedWorkflow.currentStep : null,
  //       isCompleted: relatedWorkflow ? relatedWorkflow.isCompleted : false,
  //     };
  //   });

  //   const count = await this.paymentAdviceModel.countDocuments(filters);

  //   // Aggregate total amounts by currency
  //   const totalAmountByCurrency = await this.paymentAdviceModel.aggregate([
  //     { $match: filters },
  //     {
  //       $group: {
  //         _id: '$currency',
  //         totalAmount: { $sum: '$amount' },
  //       },
  //     },
  //   ]);

  //   // Sort to ensure 'MYR' is first
  //   totalAmountByCurrency.sort((a, b) => (a._id === 'MYR' ? -1 : b._id === 'MYR' ? 1 : 0));

  //   const responseData = {
  //     data: finalPaymentAdvices,
  //     count,
  //     totalAmountByCurrency,
  //   };

  //   return { data: this.securityService.encrypt(responseData) };
  // }

  async getPaymentAdviceList(encryptedData: any): Promise<any> {
    const decryptedData = this.securityService.decrypt(encryptedData);

    // console.log("decrypt ", decryptedData)

    const {
      status,
      departmentId,
      assignedUserId,
      page = 1,
      limit = 10,
    } = decryptedData;

    const filters: any = {};
    const skip = (page - 1) * limit;

    if (departmentId) {
      filters.departmentCode = departmentId;
    } else if (assignedUserId) {
      const user = await this.userModel
        .findById(assignedUserId)
        .select('departments')
        .exec();
      // console.log("user ", user)
      if (user && user.departments && user.departments.length > 0) {
        // filters.departmentCode = { $in: user.departments };
         filters.departmentCode = { $in: user.departments.map(d => d.toString()) };
      }
    }

    let relevantPaymentAdviceIds: Types.ObjectId[] = [];

    if (status === 'to-sign' && assignedUserId) {
      const workflows = await this.approvalWorkflowModel
        .find({
          isCompleted: false,
          'steps.userId': new Types.ObjectId(assignedUserId),
          'steps.status': 'pending',
          $expr: {
            $eq: [
              '$currentStep',
              {
                $add: [
                  {
                    $indexOfArray: [
                      '$steps.userId',
                      new Types.ObjectId(assignedUserId),
                    ],
                  },
                  1,
                ],
              },
            ],
          },
        })
        .select('paymentAdviceId')
        .exec();

      relevantPaymentAdviceIds = workflows.map(
        (workflow) => workflow.paymentAdviceId,
      );
      filters._id = { $in: relevantPaymentAdviceIds };
      filters.status = 'submitted';
    } else if (status !== 'to-sign') {
      filters.status = status;
    }

    const paymentAdvices = await this.paymentAdviceModel
      .find(filters)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(Number(limit))
      .populate('departmentCode')
      .populate('createdBy')
      .exec();
      // console.log("filter ", filters);
      // console.log("pa ", paymentAdvices)

    const paymentAdviceIds = paymentAdvices.map((advice) => advice._id);
    const workflows = await this.approvalWorkflowModel
      .find({ paymentAdviceId: { $in: paymentAdviceIds } })
      .populate({
        path: 'steps.userId',
        select: 'fullName email',
      })
      .exec();

    // Get unread message counts for each payment advice
    const userObjectId = new Types.ObjectId(assignedUserId);
    const userIdString = assignedUserId.toString();

    const unreadCountPromises = paymentAdviceIds.map(async (paId) => {
      const paIdString = paId.toString();

      // Use both string form and ObjectId form in the $nin array to cover all bases
      const count = await this.messageModel
        .countDocuments({
          paymentAdviceId: paId.toString(),
          readBy: { $nin: [userObjectId, userIdString] }, // Try both formats
          senderId: { $ne: userObjectId }, // Exclude messages sent by the user
        })
        .exec();

      return { paId: paIdString, count };
    });

    // Wait for all count promises to resolve
    const unreadCountResults = await Promise.all(unreadCountPromises);

    // Convert to a lookup object for easy access
    const unreadCountMap = {};
    unreadCountResults.forEach((result) => {
      unreadCountMap[result.paId] = result.count;
    });

    const finalPaymentAdvices = paymentAdvices.map((advice) => {
      const adviceId = advice._id.toString();
      const relatedWorkflow = workflows.find(
        (workflow) => workflow.paymentAdviceId.toString() === adviceId,
      );

      return {
        ...advice.toObject(),
        workflowSteps: relatedWorkflow ? relatedWorkflow.steps : [],
        currentStep: relatedWorkflow ? relatedWorkflow.currentStep : null,
        isCompleted: relatedWorkflow ? relatedWorkflow.isCompleted : false,
        unreadMessageCount: unreadCountMap[adviceId] || 0, // Add unread message count
      };
    });

    const count = await this.paymentAdviceModel.countDocuments(filters);

    // Aggregate total amounts by currency
    const totalAmountByCurrency = await this.paymentAdviceModel.aggregate([
      { $match: filters },
      {
        $group: {
          _id: '$currency',
          totalAmount: { $sum: '$amount' },
        },
      },
    ]);

    // Sort to ensure 'MYR' is first
    totalAmountByCurrency.sort((a, b) =>
      a._id === 'MYR' ? -1 : b._id === 'MYR' ? 1 : 0,
    );

    const responseData = {
      data: finalPaymentAdvices,
      count,
      totalAmountByCurrency,
    };
    // console.log('data ', responseData);

    return { data: this.securityService.encrypt(responseData) };
  }

  async getPaymentAdviceById(encryptedData: any): Promise<any> {
    const id = this.securityService.decrypt(encryptedData);

    // Validate and convert `id` to ObjectId
    const objectId = Types.ObjectId.isValid(id) ? new Types.ObjectId(id) : null;
    if (!objectId) {
      throw new NotFoundException('Invalid Payment Advice ID');
    }

    // Step 1: Fetch PaymentAdvice by ID with populated department and creator
    const paymentAdvice = await this.paymentAdviceModel
      .findById(objectId)
      .populate('departmentCode')
      .populate('createdBy')
      .exec();

    if (!paymentAdvice) {
      throw new NotFoundException('Payment Advice not found');
    }

    // Step 2: Fetch related workflow and populate workflow steps with user details
    const workflow = await this.approvalWorkflowModel
      .findOne({ paymentAdviceId: paymentAdvice._id })
      .populate({
        path: 'steps.userId',
        select: 'fullName email',
      })
      .exec();

    // Step 3: Format the result to include workflow details within the response
    var response = {
      ...paymentAdvice.toObject(),
      workflowSteps: workflow ? workflow.steps : [],
      currentStep: workflow ? workflow.currentStep : null,
      isCompleted: workflow ? workflow.isCompleted : false,
    };

    return { data: this.securityService.encrypt(response) };
  }

  // Approve or reject a payment advice workflow step
  async approveRejectPaymentAdvice(
    dto: ApproveRejectWorkflowDto,
  ): Promise<ApprovalWorkflow> {
    const workflow = await this.approvalWorkflowModel
      .findOne({ paymentAdviceId: new Types.ObjectId(dto.paymentAdviceId) })
      .populate({
        path: 'steps.userId',
        select: '_id fullName email',
      })
      .populate({
        path: 'paymentAdviceId',
        select: '_id departmentCode vendorName serialNumber currency amount',
        populate: {
          path: 'departmentCode', // this is the nested population
          select: '_id code description', // or whatever fields you want from the department
        },
      })
      .exec();
    // console.log("workflow ", workflow);
    // return workflow;

    if (!workflow)
      throw new NotFoundException('Workflow not found for this payment advice');

    const preparerStep = workflow.steps[0];
    const financeStep = workflow.steps[workflow.steps.length-1];
    const currentStep = workflow.steps[workflow.currentStep - 1];
    const nextStep = workflow.steps[workflow.currentStep];

    // console.log("created by ", preparerStep.userId['email']);

    if ((currentStep.userId._id.toString() !== dto.userId) && dto.status !== 'cancelled') {
      throw new NotFoundException('You are not assigned to this approval step');
    }

    currentStep.status = dto.status;
    currentStep.approvalDate = new Date();
    currentStep.comments = dto.comments;

    if (dto.status === 'cancelled' && workflow.currentStep <= workflow.steps.length) {
      // Halt the workflow and set it as cancelled
      currentStep.approvalDate = undefined;
      currentStep.comments = '';
      currentStep.status = 'pending';
      preparerStep.status = 'cancelled';
      preparerStep.cancelDate = new Date();
      preparerStep.cancelReason = dto.cancelReason;
      workflow.isCompleted = true;
      workflow.completedAt = new Date();

      // Update the payment advice status to "cancelled" in the pa_main collection
      await this.paymentAdviceModel.updateOne(
        { _id: workflow.paymentAdviceId },
        { $set: { status: 'cancelled' } },
      );

      for (const step of workflow.steps) {

        // Create and save email notification for each recipient
        const emailScheduler = new this.emailSchedulerModel({
          to: step.userId['email'],
          subject: `Payment Advice Cancelled: ${workflow.paymentAdviceId['serialNumber']}`,
          body: `<p>Dear ${step.userId['fullName']}, <br/>
                     Payment advice - ${workflow.paymentAdviceId['serialNumber']} has been cancelled by <b> ${preparerStep.userId['fullName']}</b>.<br/><br/>
                     Cancellation Reason - <b>${dto.cancelReason}</b><br/><br/>
                     Department - ${workflow.paymentAdviceId['departmentCode']['code']} - ${workflow.paymentAdviceId['departmentCode']['description']}<br/>
                     Vendor Name - ${workflow.paymentAdviceId['vendorName']}<br/>
                     Amount - <b>${workflow.paymentAdviceId['currency']} ${workflow.paymentAdviceId['amount'].toFixed(2)}</b><br/><br/>
                     Thank you.<br/><br/>
                     Best Regards,<br/>
                     UZMA ECO SYSTEM</p>`,
        });
        await emailScheduler.save();
      }
      
    } else if (dto.status === 'rejected') {
      // Halt the workflow and set it as rejected
      workflow.isCompleted = true;
      workflow.completedAt = new Date();

      // Update the payment advice status to "rejected" in the pa_main collection
      await this.paymentAdviceModel.updateOne(
        { _id: workflow.paymentAdviceId },
        { $set: { status: 'rejected' } },
      );

      // Sent to Creater
      const emailScheduler1 = new this.emailSchedulerModel({
        to: preparerStep.userId['email'],
        // to: '<EMAIL>',
        subject: `Payment Advice Rejected: ${workflow.paymentAdviceId['serialNumber']}`,
        body: `<p>Dear ${preparerStep.userId['fullName']}, <br/>Your payment advice - ${workflow.paymentAdviceId['serialNumber']} has been rejected by ${currentStep.userId['fullName']}.<br/><br/>Department - ${workflow.paymentAdviceId['departmentCode']['code']} ${workflow.paymentAdviceId['departmentCode']['description']}<br/>Vendor Name - ${workflow.paymentAdviceId['vendorName']}<br/>Amount - <b>${workflow.paymentAdviceId['currency']} ${workflow.paymentAdviceId['amount'].toFixed(2)}</b><br/>Comment - ${currentStep.comments}<br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
      });
      await emailScheduler1.save();
    } else {
      // If approved, proceed to the next step
      if (nextStep) {
        nextStep.assignedAt = new Date();

        // Sent to Next Person
        const emailScheduler2 = new this.emailSchedulerModel({
          to: nextStep.userId['email'],
          // to: '<EMAIL>',
          subject: `Payment Advice to be signed: ${workflow.paymentAdviceId['serialNumber']}`,
          body: `<p>Dear ${nextStep.userId['fullName']}, <br/>You have new payment advice - ${workflow.paymentAdviceId['serialNumber']} to be signed.<br/><br/>Department - ${workflow.paymentAdviceId['departmentCode']['code']} ${workflow.paymentAdviceId['departmentCode']['description']}<br/>Vendor Name - ${workflow.paymentAdviceId['vendorName']}<br/>Amount - <b>${workflow.paymentAdviceId['currency']} ${workflow.paymentAdviceId['amount'].toFixed(2)}</b><br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
        });
        await emailScheduler2.save();

        // Sent to Creater
        const emailScheduler1 = new this.emailSchedulerModel({
          to: preparerStep.userId['email'],
          // to: '<EMAIL>',
          subject: `Payment Advice Signed: ${workflow.paymentAdviceId['serialNumber']}`,
          body: `<p>Dear ${preparerStep.userId['fullName']}, <br/>Your payment advice - ${workflow.paymentAdviceId['serialNumber']} has been signed by ${currentStep.userId['fullName']}.<br/><br/>Department - ${workflow.paymentAdviceId['departmentCode']['code']} ${workflow.paymentAdviceId['departmentCode']['description']}<br/>Vendor Name - ${workflow.paymentAdviceId['vendorName']}<br/>Amount - <b>${workflow.paymentAdviceId['currency']} ${workflow.paymentAdviceId['amount'].toFixed(2)}</b><br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
        });
        await emailScheduler1.save();
      } else {
        // Sent to Creater
        // const emailScheduler1 = new this.emailSchedulerModel({
        //   to: preparerStep.userId['email'],
        //   // to: '<EMAIL>',
        //   subject: `Payment Made for PA: ${workflow.paymentAdviceId['serialNumber']}`,
        //   body: `<p>Dear ${preparerStep.userId['fullName']}, <br/>Payment has been made for PA - ${workflow.paymentAdviceId['serialNumber']} by finance department.<br/><br/>Vendor Name - ${workflow.paymentAdviceId['vendorName']}<br/>Amount - <b>${workflow.paymentAdviceId['currency']} ${workflow.paymentAdviceId['amount']}</b><br/><br/>Thank you.<br/><br/>Best Regards,<br/>UZMA ECO SYSTEM</p>`,
        // });
        // await emailScheduler1.save();
      }

      // Update the workflow step count and check completion
      workflow.currentStep += 1;
      if (workflow.currentStep == workflow.steps.length) {
        // Update the payment advice status to "approved" in the pa_main collection
        await this.paymentAdviceModel.updateOne(
          { _id: workflow.paymentAdviceId },
          { $set: { status: 'approved' } },
        );
      } else if (workflow.currentStep > workflow.steps.length) {
        workflow.isCompleted = true;
        workflow.completedAt = new Date();
        // Update the payment advice status to "approved" in the pa_main collection
        await this.paymentAdviceModel.updateOne(
          { _id: workflow.paymentAdviceId },
          { $set: { status: 'paid' } },
        );
      }
    }
    await workflow.save();
    return workflow;
  }

  async updateBankReceipts(dto: UpdateBankReceiptsDto): Promise<PaymentAdvice> {
    const paymentAdvice = await this.paymentAdviceModel.findById(
      dto.paymentAdviceId,
    );

    if (!paymentAdvice) {
      throw new NotFoundException('Payment Advice not found');
    }

    const totalPAAmount = paymentAdvice.amount; // Total amount in Payment Advice

    // Merge existing receipts with new ones
    const existingReceipts = paymentAdvice.bankReceipts || [];
    const newReceipts = dto.bankReceipts.map((receipt) => ({
      ...receipt,
      createdAt: new Date(), // Set createdAt if not present (new receipt)
      updatedAt: new Date(), // Update timestamp for all receipts
    }));

    const allReceipts = [...existingReceipts, ...newReceipts];

    // Calculate cumulative totals and update flags
    let totalReceiptAmount = 0;

    const updatedBankReceipts = allReceipts.map((receipt) => {
      totalReceiptAmount += Number(receipt.amount);

      const isPartial = totalReceiptAmount < totalPAAmount;
      const balance = isPartial ? totalPAAmount - totalReceiptAmount : 0;
      // console.log(" isPartial ", isPartial);
      // console.log(" balance ", balance);

      return {
        ...receipt,
        isPartial,
        balance,
        updatedAt: new Date(), // Update timestamp for every receipt
      };
    });

    // console.log(" totalPAAmount ", totalPAAmount);
    // console.log(" totalReceiptAmount ", totalReceiptAmount);

    // Save updated receipts and update PaymentAdvice
    paymentAdvice.bankReceipts = updatedBankReceipts;
    paymentAdvice.isPartialPayment = totalReceiptAmount < totalPAAmount;
    paymentAdvice.partialPaymentBalance =
      totalReceiptAmount < totalPAAmount
        ? totalPAAmount - totalReceiptAmount
        : 0;

    await paymentAdvice.save();

    return paymentAdvice;
  }

  async updateBankReceiptsRpa(dto: UpdateBankReceiptsRpaDto): Promise<PaymentAdvice> {
    const paymentAdvice = await this.paymentAdviceModel.findOne({ serialNumber: dto.paID }).exec();

    if (!paymentAdvice) {
      throw new NotFoundException('Payment Advice not found');
    }

    const totalPAAmount = paymentAdvice.amount; // Total amount in Payment Advice

    // Merge existing receipts with new ones
    const existingReceipts = paymentAdvice.bankReceipts || [];
    const newReceipts = dto.bankReceipts.map((receipt) => ({
      ...receipt,
      createdAt: new Date(), // Set createdAt if not present (new receipt)
      updatedAt: new Date(), // Update timestamp for all receipts
    }));

    const allReceipts = [...existingReceipts, ...newReceipts];

    // Calculate cumulative totals and update flags
    let totalReceiptAmount = 0;

    const updatedBankReceipts = allReceipts.map((receipt) => {
      totalReceiptAmount += Number(receipt.amount);

      const isPartial = totalReceiptAmount < totalPAAmount;
      const balance = isPartial ? totalPAAmount - totalReceiptAmount : 0;
      // console.log(" isPartial ", isPartial);
      // console.log(" balance ", balance);

      return {
        ...receipt,
        isPartial,
        balance,
        updatedAt: new Date(), // Update timestamp for every receipt
      };
    });

    // console.log(" totalPAAmount ", totalPAAmount);
    // console.log(" totalReceiptAmount ", totalReceiptAmount);

    // Save updated receipts and update PaymentAdvice
    paymentAdvice.bankReceipts = updatedBankReceipts;
    paymentAdvice.isPartialPayment = totalReceiptAmount < totalPAAmount;
    paymentAdvice.partialPaymentBalance =
      totalReceiptAmount < totalPAAmount
        ? totalPAAmount - totalReceiptAmount
        : 0;

    await paymentAdvice.save();

    return paymentAdvice;
  }

  async findAllWithoutSerialNumber() {
    return this.paymentAdviceModel
      .find({ serialNumber: { $exists: false } })
      .exec();
  }

  // Generate a new serial number using the SerialNumberService
  async generateSerialNumber(prefix: string): Promise<string> {
    return this.serialNumberService.getNextSerialNumber(prefix);
  }

  async assignSerialNumbers() {
    const prefix = 'UZMA-EPA';
    const paymentAdvicesWithoutSerial = this.findAllWithoutSerialNumber();

    for (const advice of await paymentAdvicesWithoutSerial) {
      const serialNumber = await this.generateSerialNumber(prefix);
      advice.serialNumber = serialNumber;
      await advice.save();
    }
  }

  // async getAnalysis(encryptedData: string): Promise<any> {
  //   const data = this.securityService.decrypt(encryptedData);

  //   const sixMonthsAgo = new Date();
  //   sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 5);

  //   let matchCondition: any = { createdAt: { $gte: sixMonthsAgo } };

  //   if (data.groupType === 'group' && data.groupName) {
  //       const groupDepartments = await this.getGroupDepartments(data.groupName);
  //       if (groupDepartments.length > 0) {
  //           matchCondition.departmentCode = { $in: groupDepartments };
  //       }
  //   }

  //   const total = await this.paymentAdviceModel.aggregate([
  //       { $match: matchCondition },
  //       {
  //           $addFields: {
  //               actualStatus: {
  //                   $switch: {
  //                       branches: [
  //                           { case: { $eq: ["$status", "rejected"] }, then: "rejected" },
  //                           { case: { $eq: ["$status", "submitted"] }, then: "submitted" },
  //                           {
  //                               case: {
  //                                   $and: [
  //                                       { $eq: ["$status", "approved"] },
  //                                       { $eq: ["$balance", 0] },
  //                                       {
  //                                           $not: {
  //                                               $in: [
  //                                                   true,
  //                                                   {
  //                                                       $map: {
  //                                                           input: { $ifNull: ["$bankReceipts", []] },
  //                                                           as: "receipt",
  //                                                           in: { $ifNull: ["$$receipt.isPartial", false] }
  //                                                       }
  //                                                   }
  //                                               ]
  //                                           }
  //                                       }
  //                                   ]
  //                               },
  //                               then: "paid"
  //                           }
  //                       ],
  //                       default: "approved"
  //                   }
  //               }
  //           }
  //       },
  //       {
  //           $group: {
  //               _id: { status: "$actualStatus", currency: "$currency" },
  //               totalAmount: { $sum: "$amount" },
  //               count: { $sum: 1 }
  //           }
  //       },
  //       {
  //           $group: {
  //               _id: "$_id.status",
  //               currencies: {
  //                   $push: {
  //                       currency: "$_id.currency",
  //                       totalAmount: "$totalAmount"
  //                   }
  //               },
  //               count: { $sum: "$count" }
  //           }
  //       }
  //   ]);
  //   return { data: this.securityService.encrypt({ data: { total } }) };
  // }

  // async getAnalysis(encryptedData: string): Promise<any> {
  //   const data = this.securityService.decrypt(encryptedData);
  //   const sixMonthsAgo = new Date();
  //   sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 5);

  //   let matchCondition: any = { createdAt: { $gte: sixMonthsAgo } };

  //   if (data.groupType === 'group' && data.groupName) {
  //       const groupDepartments = await this.getGroupDepartments(data.groupName);
  //       if (groupDepartments.length > 0) {
  //           matchCondition.departmentCode = { $in: groupDepartments };
  //       }
  //   }

  //   const total = await this.paymentAdviceModel.aggregate([
  //       { $match: matchCondition },
  //       {
  //           $addFields: {
  //               totalPartialAmount: {
  //                   $sum: {
  //                       $map: {
  //                           input: { $ifNull: ["$bankReceipts", []] },
  //                           as: "receipt",
  //                           in: {
  //                               $cond: {
  //                                   if: { $eq: ["$$receipt.isPartial", true] },
  //                                   then: "$$receipt.amount",
  //                                   else: 0
  //                               }
  //                           }
  //                       }
  //                   }
  //               }
  //           }
  //       },
  //       {
  //           $addFields: {
  //               actualStatuses: {
  //                   $cond: {
  //                       if: {
  //                           $and: [
  //                               { $eq: ["$status", "approved"] },
  //                               {
  //                                   $gt: [
  //                                       {
  //                                           $size: {
  //                                               $filter: {
  //                                                   input: { $ifNull: ["$bankReceipts", []] },
  //                                                   as: "receipt",
  //                                                   cond: { $eq: ["$$receipt.isPartial", true] }
  //                                               }
  //                                           }
  //                                       },
  //                                       0
  //                                   ]
  //                               }
  //                           ]
  //                       },
  //                       then: ["approved", "partial"],  // 🟢 Store BOTH statuses
  //                       else: ["$status"]  // 🟢 Wrap other statuses in an array
  //                   }
  //               }
  //           }
  //       },
  //       { $unwind: "$actualStatuses" },
  //       {
  //           $group: {
  //               _id: {
  //                   status: "$actualStatuses",
  //                   currency: "$currency"
  //               },
  //               totalAmount: {
  //                   $sum: {
  //                       $cond: {
  //                           if: { $eq: ["$actualStatuses", "partial"] },
  //                           then: "$totalPartialAmount",
  //                           else: "$amount"
  //                       }
  //                   }
  //               },
  //               count: { $sum: 1 }
  //           }
  //       },
  //       {
  //           $group: {
  //               _id: "$_id.status",
  //               currencies: {
  //                   $push: {
  //                       currency: "$_id.currency",
  //                       totalAmount: "$totalAmount"
  //                   }
  //               },
  //               count: { $sum: "$count" }
  //           }
  //       },
  //       // 🟢 Fix "approved" to include all amounts correctly
  //       {
  //           $facet: {
  //               approvedPartialSeparated: [
  //                   { $match: { _id: { $in: ["approved", "partial"] } } },
  //                   { $unwind: "$currencies" },
  //                   {
  //                       $group: {
  //                           _id: "approved",
  //                           currencies: {
  //                               $push: {
  //                                   currency: "$currencies.currency",
  //                                   totalAmount: "$currencies.totalAmount"
  //                               }
  //                           },
  //                           count: { $sum: "$count" }
  //                       }
  //                   }
  //               ],
  //               partialSeparate: [
  //                   { $match: { _id: "partial" } }
  //               ],
  //               others: [
  //                   { $match: { _id: { $nin: ["approved", "partial"] } } }
  //               ]
  //           }
  //       },
  //       {
  //           $project: {
  //               total: { $concatArrays: ["$approvedPartialSeparated", "$partialSeparate", "$others"] }
  //           }
  //       },
  //       { $unwind: "$total" },
  //       {
  //           $group: {
  //               _id: "$total._id",
  //               currencies: { $push: "$total.currencies" },
  //               count: { $sum: "$total.count" }
  //           }
  //       },
  //       { $unwind: "$currencies" },
  //       { $unwind: "$currencies" },
  //       {
  //           $group: {
  //               _id: "$_id",
  //               currencies: {
  //                   $push: {
  //                       currency: "$currencies.currency",
  //                       totalAmount: { $sum: "$currencies.totalAmount" }
  //                   }
  //               },
  //               count: { $sum: "$count" }
  //           }
  //       }
  //   ]);

  //   return { data: this.securityService.encrypt({ data: { total } }) };
  // }

  // async getAnalysis(encryptedData: string): Promise<any> {
  //   const data = this.securityService.decrypt(encryptedData);

  //   const sixMonthsAgo = new Date();
  //   sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 5);

  //   let matchCondition: any = { createdAt: { $gte: sixMonthsAgo } };

  //   if (data.groupType === 'group' && data.groupName) {
  //       const groupDepartments = await this.getGroupDepartments(data.groupName);
  //       if (groupDepartments.length > 0) {
  //           matchCondition.departmentCode = { $in: groupDepartments };
  //       }
  //   }

  //   const total = await this.paymentAdviceModel.aggregate([
  //       { $match: matchCondition },
  //       {
  //           $addFields: {
  //               totalPartialAmount: {
  //                   $sum: {
  //                       $map: {
  //                           input: { $ifNull: ["$bankReceipts", []] },
  //                           as: "receipt",
  //                           in: {
  //                               $cond: {
  //                                   if: { $eq: ["$$receipt.isPartial", true] },
  //                                   then: "$$receipt.amount",
  //                                   else: 0
  //                               }
  //                           }
  //                       }
  //                   }
  //               }
  //           }
  //       },
  //       {
  //           $addFields: {
  //               actualStatus: {
  //                   $switch: {
  //                       branches: [
  //                           { case: { $eq: ["$status", "rejected"] }, then: "rejected" },
  //                           { case: { $eq: ["$status", "submitted"] }, then: "submitted" },
  //                           { case: { $eq: ["$status", "paid"] }, then: "paid" },
  //                           {
  //                               case: {
  //                                   $and: [
  //                                       { $eq: ["$status", "approved"] },
  //                                       { $gt: ["$totalPartialAmount", 0] }  // ✅ Has partial payments
  //                                   ]
  //                               },
  //                               then: "partial"
  //                           },
  //                       ],
  //                       default: "approved" // ✅ Always include "approved", even if partial exists
  //                   }
  //               }
  //           }
  //       },
  //       {
  //           $group: {
  //               _id: { status: "$actualStatus", currency: "$currency" },
  //               totalAmount: {
  //                   $sum: {
  //                       $cond: {
  //                           if: { $eq: ["$actualStatus", "partial"] },
  //                           then: "$totalPartialAmount", // ✅ Use only the partial amount
  //                           else: "$amount"
  //                       }
  //                   }
  //               },
  //               count: { $sum: 1 }
  //           }
  //       },
  //       {
  //           $group: {
  //               _id: "$_id.status",
  //               currencies: {
  //                   $push: {
  //                       currency: "$_id.currency",
  //                       totalAmount: "$totalAmount"
  //                   }
  //               },
  //               count: { $sum: "$count" }
  //           }
  //       }
  //   ]);

  //   return { data: this.securityService.encrypt({ data: { total } }) };
  // }

  async getAnalysis(encryptedData: string): Promise<any> {
    const data = this.securityService.decrypt(encryptedData);

    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 5);

    let matchCondition: any = { createdAt: { $gte: sixMonthsAgo } };

    if (data.groupType === 'group' && data.groupName) {
      const groupDepartments = await this.getGroupDepartments(data.groupName);
      if (groupDepartments.length > 0) {
        matchCondition.departmentCode = { $in: groupDepartments };
      }
    }

    const total = await this.paymentAdviceModel.aggregate([
      { $match: matchCondition },
      {
        $addFields: {
          totalPartialAmount: {
            $sum: {
              $map: {
                input: { $ifNull: ['$bankReceipts', []] },
                as: 'receipt',
                in: {
                  $cond: {
                    if: { $eq: ['$$receipt.isPartial', true] },
                    then: '$$receipt.amount',
                    else: 0,
                  },
                },
              },
            },
          },
        },
      },
      {
        $facet: {
          // Process all documents by their original status
          originalStatus: [
            {
              $group: {
                _id: { status: '$status', currency: '$currency' },
                totalAmount: { $sum: '$amount' },
                count: { $sum: 1 },
              },
            },
            {
              $group: {
                _id: '$_id.status',
                currencies: {
                  $push: {
                    currency: '$_id.currency',
                    totalAmount: '$totalAmount',
                  },
                },
                count: { $sum: '$count' },
              },
            },
          ],
          // Process only partial payments separately
          partialPayments: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$status', 'approved'] },
                    { $gt: ['$totalPartialAmount', 0] },
                  ],
                },
              },
            },
            {
              $group: {
                _id: { status: 'partial', currency: '$currency' },
                totalAmount: { $sum: '$totalPartialAmount' },
                count: { $sum: 1 },
              },
            },
            {
              $group: {
                _id: '$_id.status',
                currencies: {
                  $push: {
                    currency: '$_id.currency',
                    totalAmount: '$totalAmount',
                  },
                },
                count: { $sum: '$count' },
              },
            },
          ],
        },
      },
      // Combine the results from both facets
      {
        $project: {
          combined: {
            $concatArrays: ['$originalStatus', '$partialPayments'],
          },
        },
      },
      { $unwind: '$combined' },
      {
        $replaceRoot: { newRoot: '$combined' },
      },
    ]);

    return { data: this.securityService.encrypt({ data: { total } }) };
  }

  private async getGroupDepartments(groupName: string): Promise<string[]> {
    const groupMapping = {
      'CEO Office': ['671a5e2e824fe656ce7a8275', '671a5e2e824fe656ce7a826d'],
      Finance: ['671a5e2e824fe656ce7a8271', '671a5e2e824fe656ce7a8315'],
      'Supply Chain Mgmt': [
        '671a5e2e824fe656ce7a827f',
        '671a5e2e824fe656ce7a8305',
      ],
      People: [
        '671a5e2e824fe656ce7a8273',
        '671a5e2e824fe656ce7a8269',
        '67aeb2d139a14b495cc5efdb',
      ],
      Legal: ['671a5e2e824fe656ce7a8267'],
      Innovation: ['671a5e2e824fe656ce7a833d'],
      'Group Production Solution': [
        '671a5e2e824fe656ce7a828b',
        '671a5e2e824fe656ce7a828d',
        '671a5e2e824fe656ce7a828f',
        '671a5e2e824fe656ce7a8295',
        '671a5e2e824fe656ce7a82b9',
        '671a5e2e824fe656ce7a826f',
        '671a5e2e824fe656ce7a82c5',
        '671a5e2e824fe656ce7a82b7',
        '671a5e2e824fe656ce7a82bf',
        '671a5e2e824fe656ce7a8339',
        '671a5e2e824fe656ce7a833b',
        '671a5e2e824fe656ce7a831f',
      ],
      'Group Well Solution': [
        '671a5e2e824fe656ce7a8283',
        '671a5e2e824fe656ce7a8285',
        '671a5e2e824fe656ce7a8331',
        '671a5e2e824fe656ce7a82eb',
        '671a5e2e824fe656ce7a832f',
        '671a5e2e824fe656ce7a831d',
        '671a5e2e824fe656ce7a82cd',
        '671a5e2e824fe656ce7a82cf',
        '671a5e2e824fe656ce7a82d1',
        '671a5e2e824fe656ce7a82e1',
        '671a5e2e824fe656ce7a8317',
      ],
      'New Energy': ['671a5e2e824fe656ce7a82b3', '671a5e2e824fe656ce7a82f7'],
      'Digital Earth': ['671a5e2e824fe656ce7a829f'],
      'Energy Trading': ['671a5e2e824fe656ce7a830f'],
    };
    return groupMapping[groupName] || [];
  }

  async generateReportByDepartmentAPI(encryptedData: any) {
  const filter = this.securityService.decrypt(encryptedData);
  // console.log("decrypt ", filter);
  
  var ePAs: any = await this.generateReportByDepartment(filter);
  return { data: this.securityService.encrypt({ data: ePAs }) };
}

async generateReportByDepartment(filter: any) {
  const { departmentCode, startDate, endDate, assignedUserId } = filter;

  // Build the query object conditionally
  const query: any = {};

  // Apply department filtering logic similar to getPaymentAdviceList
  if (departmentCode) {
    query.departmentCode = departmentCode;
  } else if (assignedUserId) {
    // If no specific department but assignedUserId is provided, filter by user's departments
    const user = await this.userModel
      .findById(assignedUserId)
      .select('departments')
      .exec();
    
    if (user && user.departments && user.departments.length > 0) {
      query.departmentCode = { $in: user.departments.map(d => d.toString()) };
    } else {
      // If user has no departments, return empty result
      return [];
    }
  } else {
    // If neither departmentCode nor assignedUserId is provided, return empty result
    // This prevents returning all payment advices when no proper filtering is applied
    return [];
  }

  // Apply date range filter only if both startDate and endDate are provided
  if (startDate && endDate) {
    query.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate),
    };
  }

  return this.paymentAdviceModel
    .find(query)
    .populate('departmentCode', 'code description') // Optional: populate department details
    .populate('createdBy', 'name email') // Optional: include creator details
    .sort({ createdAt: -1 }) // 🔥 Sort by createdAt in descending order (latest first)
    .lean();
}

  async downloadExcelReport(res: Response, encryptedData: any) {
    const filter = this.securityService.decrypt(encryptedData);

    var ePAs: any = await this.generateReportByDepartment(filter);

    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('e-PA');

    worksheet.columns = [
      { header: 'Serial No', key: 'serialNumber', width: 50 },
      { header: 'Submission Date', key: 'createdAt', width: 20 },
      { header: 'Currency', key: 'currency', width: 10 },
      { header: 'Amount', key: 'amount', width: 20 },
      { header: 'Status', key: 'status', width: 30 },
      { header: 'Company Name', key: 'companyName', width: 50 },
      { header: 'Department Code', key: 'departmentCode', width: 50 },
      { header: 'Department Name', key: 'departmentName', width: 50 },
      { header: 'Vendor Name', key: 'vendorName', width: 50 },
      { header: 'Vendor Address', key: 'vendorAddress', width: 100 },
      { header: 'Payment Term', key: 'paymentTerm', width: 100 },
      { header: 'Payment', key: 'payment', width: 100 },
      { header: 'PR/FR Doc', key: 'prDoc', width: 30 },
      { header: 'PO Doc', key: 'poDoc', width: 30 },
      { header: 'GRN Doc', key: 'grnDoc', width: 30 },
      { header: 'Invoice', key: 'invoice', width: 100 },
    ];

    // 🔥 Apply Bold Styling to Headers
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.font = { bold: true }; // Bold text
      cell.alignment = { horizontal: 'center', vertical: 'middle' }; // Center align
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFFFE0B2' }, // Light yellow background (optional)
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });

    // Add rows from data
    for (const ePA of ePAs) {
      const row = worksheet.addRow({
        serialNumber: ePA.serialNumber,
        createdAt: this.formatDateTime(ePA.createdAt),
        currency: ePA.currency,
        amount: ePA.amount,
        status: ePA.status,
        companyName: ePA.companyName,
        departmentCode: ePA.departmentCode.code,
        departmentName: ePA.departmentCode.description,
        vendorName: ePA.vendorName,
        vendorAddress: ePA.vendorAddress,
        paymentTerm: ePA.paymentTerm?.termType || 'N/A',
      });

      // Make PR/PO/GRN Docs as Hyperlinks
      if (ePA.prDoc?.s3Url) {
        row.getCell('prDoc').value = {
          text: 'PR Doc',
          hyperlink: ePA.prDoc.s3Url,
        };
        row.getCell('prDoc').font = {
          color: { argb: 'FF0000FF' },
          underline: true,
        };
      }

      if (ePA.poDoc?.s3Url) {
        row.getCell('poDoc').value = {
          text: 'PO Doc',
          hyperlink: ePA.poDoc.s3Url,
        };
        row.getCell('poDoc').font = {
          color: { argb: 'FF0000FF' },
          underline: true,
        };
      }

      if (ePA.grnDoc?.s3Url) {
        row.getCell('grnDoc').value = {
          text: 'GRN Doc',
          hyperlink: ePA.grnDoc.s3Url,
        };
        row.getCell('grnDoc').font = {
          color: { argb: 'FF0000FF' },
          underline: true,
        };
      }

      if (ePA.documents && ePA.documents.length > 0) {
        const invoiceLinks = ePA.documents.map(
          (document, index) =>
            `Invoice ${index + 1}: ${document.attachments[0]?.s3Url}`,
        );

        row.getCell('invoice').value = invoiceLinks.join('\n\n'); // Join all links with newlines
        row.getCell('invoice').alignment = {
          wrapText: true,
          vertical: 'middle',
        }; // Enable text wrapping and vertical alignment
        row.height = 50; // Set a fixed row height
      }

      if (ePA.bankReceipts && ePA.bankReceipts.length > 0) {
        const payments = ePA.bankReceipts.map(
          (receipt, index) =>
            `Payment ${index + 1}: ${receipt.receiptNumber}, Amount: ${ePA.currency} ${receipt.amount}, Date: ${receipt.receiptDate}, \nReceipt: ${receipt.s3Url}`,
        );

        row.getCell('payment').value = payments.join('\n\n'); // Join all links with newlines
        row.getCell('payment').alignment = {
          wrapText: true,
          vertical: 'middle',
        }; // Enable text wrapping and vertical alignment
        row.height = 50; // Set a fixed row height
      }
    }

    const today = new Date();
    const formattedDate = this.formatDateTime(today);

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=e-PA-report-${formattedDate}.xlsx`,
    );

    // Ensure we are writing the file stream to the response and not returning the file as JSON
    return workbook.xlsx
      .write(res)
      .then(() => {
        res.status(200).end(); // End the response
      })
      .catch((err) => {
        console.error('Error writing Excel file: ', err);
        res.status(500).send({ message: 'Failed to generate report' });
      });
  }

  formatDateTime(date: Date): string {
    const d = new Date(date);

    // Add 8 hours for GMT+8
    d.setHours(d.getHours() + 8);

    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    let hours = d.getHours();
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12 || 12; // Convert 0 to 12 for 12-hour format
    const formattedTime = `${day}-${month}-${year} ${hours}:${minutes} ${ampm}`;
    return formattedTime;
  }
}
