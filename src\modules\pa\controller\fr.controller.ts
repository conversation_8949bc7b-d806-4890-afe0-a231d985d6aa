import { Controller, Post, Body, NotFoundException, Put } from '@nestjs/common';
import { FieldRequestService } from '../service/fr.service';
import { FieldRequestWorkflowDto } from '../dto/fr-workflow.dto';
import { S3Service } from 'src/modules/common/services/s3.service';

@Controller('field-requisition')
export class FieldRequisitionController {
  constructor(private readonly fieldRequestService: FieldRequestService, private readonly s3service : S3Service) {}

  // Create a new Field Requisition
  @Post('create')
  async create(@Body() encryptedData: any) {
    return this.fieldRequestService.createFieldRequest(encryptedData.data);
  }

  // Get the list of Payment Advice with filtering and pagination
  @Post('list')
  async list(@Body() encryptedData: any) {
    return this.fieldRequestService.getFieldRequisitionList(encryptedData.data);
  }

  @Post('detail')
  async getFieldRequisitionById(@Body() encryptedData: any) {
    try {
      return await this.fieldRequestService.getFieldRequisitionById(encryptedData.data);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException('Error retrieving Field Requisition');
    }
  }

  // Approve or reject a Payment Advice workflow step
  @Put('workflow/approve-reject')
  async approveReject(@Body() dto: FieldRequestWorkflowDto) {
    return this.fieldRequestService.approveRejectFieldRequisition(dto);
  }

  // @Post('attachment')
  // @UseInterceptors(FileInterceptor('file'))
  // async uploadFile(@UploadedFile() file: Express.Multer.File) {
  //   return await this.s3service.uploadPaymentAdviceAttachment(file);
  // }

  // @Patch('bank-receipts')
  // async updateBankReceipts(@Body() dto: UpdateBankReceiptsDto) {
  //   return this.paymentAdviceService.updateBankReceipts(dto);
  // }

  // @Post('assign-pa-serialno')
  // async assignPASerialNo() {
  //   return this.paymentAdviceService.assignSerialNumbers();
  // }
}
