import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Permission, PermissionSchema } from './schema/permission.schema';
import { Role, RoleSchema } from './schema/role.schema';
import { PermissionController } from './controller/permission.controller';
import { RoleController } from './controller/role.controller';
import { PermissionService } from './service/permission.service';
import { RoleService } from './service/role.service';
import { JwtModule } from '@nestjs/jwt';
import { jwtConstants } from './guard/constant';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Permission.name, schema: PermissionSchema },
      { name: Role.name, schema: RoleSchema },
    ]),
    JwtModule.register({
      global: true,
      secret: jwtConstants.secret,
      signOptions: { expiresIn: '1h' },
    }),
  ],
  controllers: [PermissionController, RoleController],
  providers: [PermissionService, RoleService],
  exports: [MongooseModule],
})
export class AuthModule {}
