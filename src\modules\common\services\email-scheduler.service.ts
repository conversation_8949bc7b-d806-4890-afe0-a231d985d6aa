import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Cron, Interval } from '@nestjs/schedule';
import { EmailScheduler, EmailSchedulerDocument } from '../schema/pa-email.schema';
import { EmailService } from 'src/modules/pa/service/email.service';

@Injectable()
export class EmailSchedulerService {
    constructor(
        @InjectModel(EmailScheduler.name) private emailSchedulerModel: Model<EmailSchedulerDocument>,
        private readonly emailService: EmailService,
    ) { }

    //   @Cron('*/1 * * * *') // Run every 5 minutes
    @Cron('*/10 * * * * *')  // Format: second, minute, hour, day, month, dayOfWeek
    async processPendingEmails() {
        // console.log(`Email Scheduler running...`);

        const pendingEmails = await this.emailSchedulerModel.find({ status: 'pending' });

        for (const email of pendingEmails) {
            try {
                console.log(`Email sent to ${email.to}`);

                await this.emailService.sendMail(email.to, email.subject, email.body);

                email.status = 'sent';
                await email.save();
            } catch (error) {
                console.error(`Failed to send email to ${email.to}`, error);
                email.status = 'failed';
                await email.save();  // Save failed status for retry later
            }
        }
    }
}
