import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

// Define the score distribution interface
interface ScoreDistribution {
  excellent: number; // 4.1-5.0
  good: number; // 3.1-4.0
  satisfactory: number; // 2.6-3.0
  needsImprovement: number; // 1.6-2.5
  unsatisfactory: number; // 1.0-1.5
}

@Schema({ timestamps: true, collection: 'ippa_department_stats' })
export class DepartmentIPPAStats {
  _id: Types.ObjectId;

  @Prop({ required: true, index: true })
  departmentCode: string;

  @Prop({ required: true })
  departmentName: string;

  @Prop({ required: true, index: true })
  financialYear: string;

  // Department statistics
  @Prop({ required: true })
  totalEmployees: number;

  @Prop({ required: true })
  submittedCount: number;

  @Prop({ required: true })
  pendingCount: number;

  @Prop({ required: true })
  completedCount: number;

  @Prop({ required: true })
  submissionRate: number; // Percentage

  // Performance statistics
  @Prop()
  averageScore?: number;

  @Prop({ 
    type: Object,
    required: false,
    default: undefined 
  })
  scoreDistribution?: ScoreDistribution;

  // HOD info
  @Prop({ type: Types.ObjectId, ref: 'User' })
  hodId?: Types.ObjectId;

  @Prop()
  hodName?: string;

  @Prop({ default: Date.now })
  lastUpdated: Date;

  @Prop({ default: true })
  isActive: boolean;
}

export type DepartmentIPPAStatsDocument = DepartmentIPPAStats & Document;
export const DepartmentIPPAStatsSchema = SchemaFactory.createForClass(DepartmentIPPAStats);