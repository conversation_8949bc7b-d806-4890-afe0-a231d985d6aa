// src/project-progress/project-progress.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  CreateProjectProgressDto,
  UpdateProjectProgressDto,
} from '../dto/project-progress.dto';
import { ProjectProgress } from '../schema/project-progress.schema';

@Injectable()
export class ProjectProgressService {
  constructor(
    @InjectModel(ProjectProgress.name)
    private readonly projectProgressModel: Model<ProjectProgress>,
  ) {}

  // async createOrUpdate(createOrUpdateDto: CreateProjectProgressDto): Promise<ProjectProgress> {
  //   const { baseline, percentage, updatedBy, ...rest } = createOrUpdateDto;

  //   // Calculate variance
  //   const variance = percentage - baseline;

  //   // Check if a ProjectProgress document already exists
  //   const existingProjectProgress = await this.projectProgressModel.findOne().exec();

  //   if (existingProjectProgress) {
  //     // If it exists, update it
  //     existingProjectProgress.baseline = baseline;
  //     existingProjectProgress.percentage = percentage;
  //     existingProjectProgress.variance = variance;
  //     existingProjectProgress.liveDate = rest.liveDate;
  //     if (updatedBy) {
  //       existingProjectProgress.updatedBy = new Types.ObjectId(updatedBy);
  //     }

  //     return existingProjectProgress.save();
  //   } else {
  //     // If it doesn't exist, create a new one
  //     const newProjectProgress = new this.projectProgressModel({
  //       baseline,
  //       percentage,
  //       variance,
  //       updatedBy: updatedBy ? new Types.ObjectId(updatedBy) : undefined,
  //       ...rest,
  //     });

  //     return newProjectProgress.save();
  //   }
  // }

  async createOrUpdate(
    createOrUpdateDto: CreateProjectProgressDto,
  ): Promise<ProjectProgress> {
    const {
      baseline,
      percentage,
      updatedBy,
      variance: _ignoredVariance,
      ...rest
    } = createOrUpdateDto;

    const variance = percentage - baseline;

    const existingProjectProgress = await this.projectProgressModel
      .findOne()
      .exec();

    if (existingProjectProgress) {
      existingProjectProgress.baseline = baseline;
      existingProjectProgress.percentage = percentage;
      existingProjectProgress.variance = variance;
      existingProjectProgress.liveDate = rest.liveDate;

      if (updatedBy) {
        existingProjectProgress.updatedBy = new Types.ObjectId(updatedBy);
      }

      return existingProjectProgress.save();
    } else {
      const newProjectProgress = new this.projectProgressModel({
        baseline,
        percentage,
        variance,
        updatedBy: updatedBy ? new Types.ObjectId(updatedBy) : undefined,
        ...rest,
      });

      return newProjectProgress.save();
    }
  }

  async findAll(): Promise<ProjectProgress[]> {
    return this.projectProgressModel.find().populate('updatedBy').exec();
  }

  async findOne(id: string): Promise<ProjectProgress> {
    const projectProgress = await this.projectProgressModel
      .findById(id)
      .populate('updatedBy')
      .exec();
    if (!projectProgress) {
      throw new NotFoundException(`ProjectProgress with ID "${id}" not found`);
    }
    return projectProgress;
  }

  async update(
    id: string,
    updateProjectProgressDto: UpdateProjectProgressDto,
  ): Promise<ProjectProgress> {
    const { baseline, percentage, updatedBy, ...rest } =
      updateProjectProgressDto;

    const updatedProjectProgress: Partial<ProjectProgress> = {
      ...rest,
      updatedBy: updatedBy ? new Types.ObjectId(updatedBy) : undefined,
    };

    if (baseline !== undefined || percentage !== undefined) {
      const existingProject = await this.projectProgressModel
        .findById(id)
        .exec();
      if (!existingProject) {
        throw new NotFoundException(
          `ProjectProgress with ID "${id}" not found`,
        );
      }

      const newBaseline =
        baseline !== undefined ? baseline : existingProject.baseline;
      const newPercentage =
        percentage !== undefined ? percentage : existingProject.percentage;
      updatedProjectProgress.baseline = newBaseline;
      updatedProjectProgress.percentage = newPercentage;
      updatedProjectProgress.variance = newBaseline - newPercentage;
    }

    const projectProgress = await this.projectProgressModel
      .findByIdAndUpdate(id, updatedProjectProgress, { new: true })
      .populate('updatedBy')
      .exec();

    if (!projectProgress) {
      throw new NotFoundException(`ProjectProgress with ID "${id}" not found`);
    }
    return projectProgress;
  }

  async remove(id: string): Promise<void> {
    const result = await this.projectProgressModel.findByIdAndDelete(id).exec();
    if (!result) {
      throw new NotFoundException(`ProjectProgress with ID "${id}" not found`);
    }
  }
}
