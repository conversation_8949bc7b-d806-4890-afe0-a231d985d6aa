export class CreateProjectCostMainDto {
    readonly approvedBudget: number;
    readonly budgetUsed: number;
    readonly invoice: number;
    readonly costSaving?: number;
    readonly updatedBy?: string; // ObjectId or User reference
  }
  
  export class UpdateProjectCostMainDto {
    readonly approvedBudget?: number;
    readonly budgetUsed?: number;
    readonly invoice?: number;
    readonly costSaving?: number;
    readonly updatedBy?: string; // ObjectId or User reference
  }
  