import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ESignSerialNumber, ESignSerialNumberDocument } from '../schema/esign-serial-no.schema';

@Injectable()
export class ESignSerialNumberService {
  constructor(
    @InjectModel(ESignSerialNumber.name) private readonly serialNumberModel: Model<ESignSerialNumberDocument>
  ) {}

  async getNextSerialNumber(prefix: string): Promise<string> {
    const serial = await this.serialNumberModel.findOneAndUpdate(
      { prefix },
      { $inc: { currentNumber: 1 } },
      { new: true, upsert: true }
    );

    const paddedNumber = serial.currentNumber.toString().padStart(6, '0');
    return `${prefix}-${paddedNumber}`;
  }
}
