import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as xlsx from 'xlsx';import { Company } from '../schema/company.schema';

interface CompanyEntry {
  name: string;
  description: string;
  logo: string;
  address: string;
  tel: string;
  fax: string;
  isActive: string;  // 'Yes' or 'No' in the Excel file
}

@Injectable()
export class CompanyService {
  constructor(
    @InjectModel(Company.name) private companyModel: Model<Company>,
  ) {}

  async uploadCompanies(fileBuffer: Buffer): Promise<void> {
    const workbook = xlsx.read(fileBuffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const data: CompanyEntry[] = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    for (const entry of data) {
      const company = new this.companyModel({
        name: entry.name,
        description: entry.description,
        logo: entry.logo,
        address: entry.address,
        tel: entry.tel,
        fax: entry.fax,
        isActive: entry.isActive === 'Yes',  // Convert 'Yes'/'No' to true/false
      });
      await company.save();
    }
  }

   // Method to get a department by its code and populate manager, hod, and ceo
   async getCompanyByCode(code: string): Promise<Company> {
    const company = await this.companyModel
      .findOne({ name })
      .exec();

    if (!company) {
      throw new NotFoundException(`Company with code ${code} not found`);
    }
    return company;
  }

  async getAllCompanies(): Promise<any> {
    const companies = await this.companyModel.find().exec();
    return { success: true, data: companies };;
  }

  async getAllCompanyIds(): Promise<string[]> {
    const companies = await this.companyModel.find({}, '_id').exec();
    return companies.map(company => company._id.toString());
  }
}
