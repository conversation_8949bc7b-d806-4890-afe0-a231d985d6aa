import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

// Schema for Bank Guarantee Validity Logs
export type FinanceBGLogDocument = FinanceBGLog & Document;

@Schema({ timestamps: true, collection: 'finance_bg_logs' })
export class FinanceBGLog {

    @Prop({ type: Types.ObjectId, ref: 'FinanceBG', required: true })
    bankGuaranteeId: Types.ObjectId;  // Links to the Payment Advice

    @Prop()
    status: string; // e.g., 'valid', 'expired', 'extended', 'completed'

    @Prop()
    remark: string;

    @Prop()
    updatedBy: string;

    @Prop()
    updatedAt: Date;
}
export const FinanceBGLogSchema = SchemaFactory.createForClass(FinanceBGLog);

// Define indexes for logs schema
FinanceBGLogSchema.index({ bankGuaranteeId: 1 });
FinanceBGLogSchema.index({ status: 1 });