import { Controller, Get, Post, Body, Param, Put, Query, UploadedFile, UseInterceptors } from '@nestjs/common';
import { Billing } from '../schema/billing.schema';
import { ApprovalActionDto } from '../dto/billing-approval.dto';
import { BillingService } from '../service/billing.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { S3Service } from 'src/modules/common/services/s3.service';

@Controller('billing')
export class BillingController {
  constructor(private readonly billingService: BillingService, private readonly s3service : S3Service) { }

  @Post()
  async create(@Body() createBillingDto: any): Promise<Billing> {
    return this.billingService.createBillingAdvice(createBillingDto);
  }

  @Post('get')
  async getBillingAdvice(@Body() body: any): Promise<Billing> {
    return this.billingService.getBillingAdvice(body.billingId);
  }

  @Post('get/all')
  async findAll(@Query() query: any): Promise<Billing[]> {
    return this.billingService.findBillingAdvices(query);
  }

  @Get(':billingId')
  async findOne(@Param('billingId') billingId: string): Promise<Billing> {
    return this.billingService.getBillingAdvice(billingId);
  }

  @Get(':billingId/with-rpa-tasks')
  async findOneWithRpaTasks(@Param('billingId') billingId: string): Promise<any> {
    return this.billingService.getBillingWithRpaTasks(billingId);
  }

  @Put('approve')
  async approve(
    @Body() approvalDto: ApprovalActionDto,
  ): Promise<Billing> {
    return this.billingService.approveBillingAdvice(
      approvalDto.billingId,
      approvalDto.userId,
      approvalDto.status,
      approvalDto.comments,
    );
  }

  @Post(':billingId/sale-order')
  async createSaleOrder(@Param('billingId') billingId: string): Promise<Billing> {
    return this.billingService.createSaleOrder(billingId);
  }

  @Post(':billingId/delivery-order')
  async createDeliveryOrder(@Param('billingId') billingId: string): Promise<Billing> {
    return this.billingService.createDeliveryOrder(billingId);
  }

  @Post(':billingId/invoice')
  async createInvoice(@Param('billingId') billingId: string): Promise<Billing> {
    return this.billingService.createInvoice(billingId);
  }

  @Put('finance-approve')
  async approveFinanceBilling(
    @Body() approvalData: { billingId: string, userId: string, status: string, comments?: string }
  ) {
    return this.billingService.approveFinanceBilling(
      approvalData.billingId,
      approvalData.userId,
      approvalData.status,
      approvalData.comments
    );
  }

  @Get(':billingId/finance-approvals')
  async getFinanceApprovals(@Param('billingId') billingId: string) {
    const billing = await this.billingService.getBillingAdvice(billingId);
    return {
      billingId: billing.billingId,
      financeApprovals: billing.financeApprovals,
      financeApprovalStatus: billing.financeApprovalStatus
    };
  }

  @Post('user-billings')
  async getUserBillingsPaginated(
    @Body() body: {
      userId: string
      companyId: string,
      tab: number,
      page: number,
      limit: number,
      searchText?: string,
      dateFrom?: string,
      dateTo?: string,
      sortBy?: string,
      sortDirection?: 'asc' | 'desc'
    }
  ): Promise<any> {

    const { userId, companyId, tab, page = 1, limit = 10, ...filters } = body;

    return this.billingService.getUserBillingAdvicesPaginated(
      userId,
      companyId,
      { tab, ...filters },
      page,
      limit
    );
  }

  @Post('attachment')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    const s3Url = await this.s3service.uploadAutoBilling(file);
    return { data: s3Url}
  }
}