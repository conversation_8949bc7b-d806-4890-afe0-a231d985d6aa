// import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
// import { Reflector } from '@nestjs/core';
// import { UserService } from 'src/user/service/user.service';

// export const PERMISSIONS_KEY = 'permissions';

// export interface RequiredPermission {
//   action: string;
//   resource: string;
// }

// export const RequirePermissions = (permissions: RequiredPermission[]) => {
//   return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
//     Reflect.defineMetadata(PERMISSIONS_KEY, permissions, descriptor.value);
//   };
// };

// @Injectable()
// export class PermissionGuard implements CanActivate {
//   constructor(
//     private reflector: Reflector,
//     private userService: UserService
//   ) {}

//   async canActivate(context: ExecutionContext): Promise<boolean> {
//     const requiredPermissions = this.reflector.get<RequiredPermission[]>(
//       PERMISSIONS_KEY,
//       context.getHandler()
//     );

//     if (!requiredPermissions) {
//       return true; // No permissions required
//     }

//     const request = context.switchToHttp().getRequest();
//     const user = request.user;

//     if (!user) {
//       throw new ForbiddenException('User not authenticated');
//     }

//     // Check if user has all required permissions
//     for (const permission of requiredPermissions) {
//       const hasPermission = await this.userService.hasPermission(
//         user.userId,
//         permission.action,
//         permission.resource
//       );

//       if (!hasPermission) {
//         throw new ForbiddenException(
//           `Access denied. Required permission: ${permission.action} on ${permission.resource}`
//         );
//       }
//     }

//     return true;
//   }
// }