// src/otp/otp.controller.ts
import { Controller, Post, Body } from '@nestjs/common';
import { OtpService } from '../service/otp.service';
@Controller('otp')
export class OtpController {
  constructor(private readonly otpService: OtpService) {}

  @Post('request')
  async requestOtp(@Body() encryptedData: any): Promise<any> {
    await this.otpService.requestOtp(encryptedData.data);
    return { status: 'success',  message: 'OTP sent to email.' };
  }

  @Post('verify')
  async verifyOtp(
    @Body() encryptedData: any,
  ): Promise<{ response: any }> {
    var response = await this.otpService.verifyOtp(encryptedData.data);
    return response;
  }
}
