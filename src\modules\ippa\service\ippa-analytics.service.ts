// Enhanced ippa-analytics.service.ts
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, PipelineStage } from 'mongoose';
import { Department } from 'src/modules/user/schema/department.schema';
import { IPPASubmission, IPPAStatus } from '../schema/ippa-submission.schema';
import { IppaUser } from '../schema/ippa-user.schema';
import { User } from 'src/modules/user/schema/user.schema';
import { SecurityService } from 'src/modules/user/service/security.service';

export interface EmployeeSubmissionDetail {
  employeeId: string;
  employeeName: string;
  email: string;
  position: string;
  employeeGrade: string;
  lineManagerName?: string;
  hasSubmission: boolean;
  submissionStatus: IPPAStatus | 'NOT_SUBMITTED';
  submissionId?: string;
  ippaId?: string;
  submittedAt?: Date;
  lastModifiedAt?: Date;
  reviewPeriod?: string;
  employeeOverallScore?: number;
  supervisorOverallScore?: number;
  performanceRating?: string;
  daysSinceSubmission?: number;
  isOverdue?: boolean;
  currentReviewerName?: string;
  totalObjectives?: number;
  completedObjectives?: number;
  workflowProgress?: {
    currentStep: number;
    totalSteps: number;
    currentStepTitle: string;
  };
}

export interface DepartmentAnalysis {
  departmentCode: string;
  departmentName: string;
  totalEmployees: number;
  totalSubmissions: number;
  completedSubmissions: number;
  submissionPercentage: number;
  completionPercentage: number;
  statusBreakdown: {
    [key: string]: number;
  };
  // Enhanced employee details
  employees: EmployeeSubmissionDetail[];
  submittedEmployees: EmployeeSubmissionDetail[];
  pendingEmployees: EmployeeSubmissionDetail[];
  notSubmittedEmployees: EmployeeSubmissionDetail[];
  averagePerformanceScore: number;
  overdueSubmissions: number;
  departmentStats: {
    avgDaysToSubmit: number;
    avgDaysForReview: number;
    highPerformers: number; // Score >= 4.0
    lowPerformers: number; // Score < 2.5
  };
}

export interface DashboardSummary {
  totalEmployees: number;
  totalSubmissions: number;
  overallSubmissionRate: number;
  overallCompletionRate: number;
  departmentAnalysis: DepartmentAnalysis[];
  globalStats: {
    avgPerformanceScore: number;
    totalOverdueSubmissions: number;
    topPerformingDepartment: string;
    mostDelayedDepartment: string;
    submissionTrend: 'increasing' | 'decreasing' | 'stable';
  };
}

@Injectable()
export class IppaAnalyticsService {
  constructor(
    @InjectModel(IPPASubmission.name)
    private ippaSubmissionModel: Model<IPPASubmission>,
    @InjectModel(IppaUser.name)
    private ippaUserModel: Model<IppaUser>,
    @InjectModel(Department.name)
    private departmentModel: Model<Department>,
    @InjectModel(User.name)
    private userModel: Model<User>,
    private readonly securityService: SecurityService,
  ) {}

  async getDepartmentSubmissionAnalysis(
    financialYear?: string,
    statusFilter?: string,
  ): Promise<DepartmentAnalysis[]> {
    const currentYear = financialYear || this.getCurrentFinancialYear();

    // Get all active employees with their department information
    const employees = await this.ippaUserModel
      .find({ isActive: true })
      .populate('lineManager', 'fullName email')
      .lean()
      .exec();

    // Get all submissions for the financial year
    const submissions = await this.ippaSubmissionModel
      .find({
        financialYear: currentYear,
        ...(statusFilter ? { status: statusFilter } : {}),
      })
      .populate('currentReviewerId', 'fullName email')
      .lean()
      .exec();

    // Group employees by department
    const departmentMap = new Map<string, any>();

    // Initialize departments
    employees.forEach((employee) => {
      const deptCode = employee.departmentCode;
      const deptName = employee.department;

      if (!departmentMap.has(deptCode)) {
        departmentMap.set(deptCode, {
          departmentCode: deptCode,
          departmentName: deptName,
          employees: [],
          submissions: [],
        });
      }

      departmentMap.get(deptCode).employees.push(employee);
    });

    // Add submissions to departments
    submissions.forEach((submission) => {
      const employee = employees.find(
        (emp) => emp._id.toString() === submission.employeeId.toString(),
      );
      if (employee) {
        const deptCode = employee.departmentCode;
        if (departmentMap.has(deptCode)) {
          departmentMap.get(deptCode).submissions.push({
            ...submission,
            employee,
          });
        }
      }
    });

    // Process each department
    const departmentAnalysis: DepartmentAnalysis[] = [];

    for (const [deptCode, deptData] of departmentMap) {
      const { employees: deptEmployees, submissions: deptSubmissions } =
        deptData;

      // Create employee submission details
      const employeeDetails: EmployeeSubmissionDetail[] = deptEmployees.map(
        (employee) => {
          const submission = deptSubmissions.find(
            (sub) => sub.employee._id.toString() === employee._id.toString(),
          );

          const baseDetail: EmployeeSubmissionDetail = {
            employeeId: employee._id.toString(),
            employeeName: employee.fullName,
            email: employee.email,
            position: employee.position || 'N/A',
            employeeGrade: employee.employeeGrade || 'N/A',
            lineManagerName: employee.lineManager?.fullName || 'N/A',
            hasSubmission: !!submission,
            submissionStatus: submission?.status || 'NOT_SUBMITTED',
          };

          if (submission) {
            const daysSinceSubmission = submission.submittedAt
              ? Math.ceil(
                  (new Date().getTime() -
                    new Date(submission.submittedAt).getTime()) /
                    (1000 * 60 * 60 * 24),
                )
              : 0;

            const isOverdue = this.isSubmissionOverdue(submission);
            const performanceScore =
              submission.supervisorOverallScore ||
              submission.employeeOverallScore ||
              0;
            const workflowProgress = this.getWorkflowProgress(
              submission.approvalWorkflow || [],
            );

            Object.assign(baseDetail, {
              submissionId: submission._id.toString(),
              ippaId: submission.ippaId,
              submittedAt: submission.submittedAt,
              lastModifiedAt: submission.lastModifiedAt,
              reviewPeriod: submission.reviewPeriod,
              employeeOverallScore: submission.employeeOverallScore,
              supervisorOverallScore: submission.supervisorOverallScore,
              performanceRating:
                this.calculatePerformanceRating(performanceScore).rating,
              daysSinceSubmission,
              isOverdue,
              currentReviewerName:
                submission.currentReviewerId?.fullName || null,
              totalObjectives: submission.objectives?.length || 0,
              completedObjectives:
                submission.objectives?.filter((obj) => obj.supervisorRating)
                  .length || 0,
              workflowProgress,
            });
          }

          return baseDetail;
        },
      );

      // Calculate department statistics
      const totalEmployees = employeeDetails.length;
      const submittedEmployees = employeeDetails.filter(
        (emp) => emp.hasSubmission,
      );
      const completedSubmissions = submittedEmployees.filter(
        (emp) => emp.submissionStatus === IPPAStatus.COMPLETED,
      );
      const pendingEmployees = submittedEmployees.filter(
        (emp) =>
          emp.submissionStatus !== IPPAStatus.COMPLETED &&
          emp.submissionStatus !== 'NOT_SUBMITTED',
      );
      const notSubmittedEmployees = employeeDetails.filter(
        (emp) => !emp.hasSubmission,
      );
      const overdueSubmissions = submittedEmployees.filter(
        (emp) => emp.isOverdue,
      ).length;

      // Performance statistics
      const scoresArray = submittedEmployees
        .map(
          (emp) => emp.supervisorOverallScore || emp.employeeOverallScore || 0,
        )
        .filter((score) => score > 0);

      const averagePerformanceScore =
        scoresArray.length > 0
          ? scoresArray.reduce((sum, score) => sum + score, 0) /
            scoresArray.length
          : 0;

      const highPerformers = scoresArray.filter((score) => score >= 4.0).length;
      const lowPerformers = scoresArray.filter((score) => score < 2.5).length;

      // Time-based statistics
      const submissionDays = submittedEmployees
        .map((emp) => emp.daysSinceSubmission || 0)
        .filter((days) => days > 0);

      const avgDaysToSubmit =
        submissionDays.length > 0
          ? submissionDays.reduce((sum, days) => sum + days, 0) /
            submissionDays.length
          : 0;

      // Status breakdown
      const statusBreakdown = {};
      Object.values(IPPAStatus).forEach((status) => {
        statusBreakdown[status] = employeeDetails.filter(
          (emp) => emp.submissionStatus === status,
        ).length;
      });
      statusBreakdown['NOT_SUBMITTED'] = notSubmittedEmployees.length;

      // Create department analysis
      const analysis: DepartmentAnalysis = {
        departmentCode: deptCode,
        departmentName: deptData.departmentName,
        totalEmployees,
        totalSubmissions: submittedEmployees.length,
        completedSubmissions: completedSubmissions.length,
        submissionPercentage:
          totalEmployees > 0
            ? (submittedEmployees.length / totalEmployees) * 100
            : 0,
        completionPercentage:
          submittedEmployees.length > 0
            ? (completedSubmissions.length / submittedEmployees.length) * 100
            : 0,
        statusBreakdown,
        employees: employeeDetails,
        submittedEmployees,
        pendingEmployees,
        notSubmittedEmployees,
        averagePerformanceScore:
          Math.round(averagePerformanceScore * 100) / 100,
        overdueSubmissions,
        departmentStats: {
          avgDaysToSubmit: Math.round(avgDaysToSubmit * 100) / 100,
          avgDaysForReview: 0, // Can be calculated based on review completion times
          highPerformers,
          lowPerformers,
        },
      };

      departmentAnalysis.push(analysis);
    }

    // Sort by department code
    return departmentAnalysis.sort((a, b) =>
      a.departmentCode.localeCompare(b.departmentCode),
    );
  }

  async getDashboardSummary(encryptedData: any): Promise<any> {
    try {
      const decryptedData = this.securityService.decrypt(encryptedData);
      const currentYear =
        decryptedData.financialYear || this.getCurrentFinancialYear();

      // Get enhanced department analysis
      const departmentAnalysis =
        await this.getDepartmentSubmissionAnalysis(currentYear);

      // Calculate overall metrics
      const totalEmployees = departmentAnalysis.reduce(
        (sum, dept) => sum + dept.totalEmployees,
        0,
      );
      const totalSubmissions = departmentAnalysis.reduce(
        (sum, dept) => sum + dept.totalSubmissions,
        0,
      );
      const totalCompleted = departmentAnalysis.reduce(
        (sum, dept) => sum + dept.completedSubmissions,
        0,
      );

      // Calculate global statistics
      const allScores = departmentAnalysis
        .flatMap((dept) => dept.submittedEmployees)
        .map(
          (emp) => emp.supervisorOverallScore || emp.employeeOverallScore || 0,
        )
        .filter((score) => score > 0);

      const avgPerformanceScore =
        allScores.length > 0
          ? allScores.reduce((sum, score) => sum + score, 0) / allScores.length
          : 0;

      const totalOverdueSubmissions = departmentAnalysis.reduce(
        (sum, dept) => sum + dept.overdueSubmissions,
        0,
      );

      // Find top and most delayed departments
      const topPerformingDepartment =
        departmentAnalysis
          .filter((dept) => dept.averagePerformanceScore > 0)
          .sort(
            (a, b) => b.averagePerformanceScore - a.averagePerformanceScore,
          )[0]?.departmentName || 'N/A';

      const mostDelayedDepartment =
        departmentAnalysis
          .filter((dept) => dept.departmentStats.avgDaysToSubmit > 0)
          .sort(
            (a, b) =>
              b.departmentStats.avgDaysToSubmit -
              a.departmentStats.avgDaysToSubmit,
          )[0]?.departmentName || 'N/A';

      const responseData = {
        totalEmployees,
        totalSubmissions,
        overallSubmissionRate:
          totalEmployees > 0
            ? Math.round((totalSubmissions / totalEmployees) * 10000) / 100
            : 0,
        overallCompletionRate:
          totalSubmissions > 0
            ? Math.round((totalCompleted / totalSubmissions) * 10000) / 100
            : 0,
        departmentAnalysis,
        globalStats: {
          avgPerformanceScore: Math.round(avgPerformanceScore * 100) / 100,
          totalOverdueSubmissions,
          topPerformingDepartment,
          mostDelayedDepartment,
          submissionTrend: 'stable', // Can be enhanced with historical data comparison
        },
      };
      return { data: this.securityService.encrypt(responseData) };
    } catch (error) {
      console.error('Error in getDashboardSummary:', error);
      throw new Error('Failed to fetch dashboard summary');
    }
  }

  private calculatePerformanceRating(score: number): {
    rating: string;
    description: string;
  } {
    if (score >= 4.1) {
      return {
        rating: 'STRETCH EXPECTATION',
        description: 'Excellent performance far beyond expectations',
      };
    } else if (score >= 3.1) {
      return {
        rating: 'EXCEED EXPECTATION',
        description:
          'Achieves beyond the targets set and proactively adds value',
      };
    } else if (score >= 2.6) {
      return {
        rating: 'MEETS EXPECTATION',
        description: 'Achieves expectations set at the required level',
      };
    } else if (score >= 1.6) {
      return {
        rating: 'BELOW EXPECTATION',
        description: 'Does not fully meet the expectations set',
      };
    } else {
      return {
        rating: 'SIGNIFICANTLY BELOW EXPECTATION',
        description: 'Below requirement for successful job performance',
      };
    }
  }

  private isSubmissionOverdue(submission: any): boolean {
    if (!submission.submittedAt) return false;

    const daysPending = Math.ceil(
      (new Date().getTime() - new Date(submission.submittedAt).getTime()) /
        (1000 * 60 * 60 * 24),
    );

    switch (submission.status) {
      case IPPAStatus.PENDING_SUPERVISOR_REVIEW:
        return daysPending > 14;
      case IPPAStatus.PENDING_HOD_REVIEW:
        return daysPending > 10;
      case IPPAStatus.PENDING_HR_REVIEW:
        return daysPending > 7;
      default:
        return false;
    }
  }

  private getWorkflowProgress(workflow: any[]): {
    currentStep: number;
    totalSteps: number;
    currentStepTitle: string;
  } {
    if (!workflow.length) {
      return {
        currentStep: 0,
        totalSteps: 0,
        currentStepTitle: 'Not Started',
      };
    }

    const completedSteps = workflow.filter(
      (step) => step.status === 'completed',
    ).length;
    const currentStep = workflow.find((step) => step.status === 'pending');

    return {
      currentStep: completedSteps + 1,
      totalSteps: workflow.length,
      currentStepTitle: currentStep?.title || 'Completed',
    };
  }

  // Keep existing methods
  private getCurrentFinancialYear(): string {
    return 'FY2025';
  }

  async getFinancialYears(): Promise<string[]> {
    try {
      const distinctYears = await this.ippaSubmissionModel
        .distinct('financialYear')
        .sort();

      if (!distinctYears.length) {
        return ['FY2025'];
      }

      return distinctYears.sort().reverse();
    } catch (error) {
      console.error('Error in getFinancialYears:', error);
      return ['FY2025'];
    }
  }

  // Additional helper methods for enhanced functionality
  async getDepartmentEmployeeDetails(
    departmentCode: string,
    financialYear?: string,
  ): Promise<EmployeeSubmissionDetail[]> {
    const currentYear = financialYear || this.getCurrentFinancialYear();
    const analysis = await this.getDepartmentSubmissionAnalysis(currentYear);
    const department = analysis.find(
      (dept) => dept.departmentCode === departmentCode,
    );
    return department?.employees || [];
  }

  async getPerformanceDistribution(financialYear?: string): Promise<{
    [rating: string]: number;
  }> {
    const analysis = await this.getDepartmentSubmissionAnalysis(financialYear);
    const distribution = {
      'STRETCH EXPECTATION': 0,
      'EXCEED EXPECTATION': 0,
      'MEETS EXPECTATION': 0,
      'BELOW EXPECTATION': 0,
      'SIGNIFICANTLY BELOW EXPECTATION': 0,
      NOT_RATED: 0,
    };

    analysis.forEach((dept) => {
      dept.submittedEmployees.forEach((emp) => {
        if (emp.performanceRating) {
          distribution[emp.performanceRating] =
            (distribution[emp.performanceRating] || 0) + 1;
        } else {
          distribution['NOT_RATED']++;
        }
      });
    });

    return distribution;
  }
}
