import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type RPAWebhookDocument = RPAWebhook & Document;

@Schema({ timestamps: true , collection: 'rpa_webhook'})
export class RPAWebhook {
  @Prop({ required: true })
  requestId: string;

  @Prop()
  fileName: string;

  @Prop()
  fileSize: number;
  
  @Prop()
  fileUrl: string;

  @Prop()
  processedRecords: string;

  @Prop({ default: false })
  isProcessed: boolean;

  @Prop()
  processedAt: Date;

  @Prop({ default: false })
  hasError: boolean;

  @Prop()
  errorMessage: string;
}

export const RPAWebhookSchema = SchemaFactory.createForClass(RPAWebhook);