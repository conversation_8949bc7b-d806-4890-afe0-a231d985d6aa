#!/bin/bash

# UzmaCloud API Testing Script
# Usage: ./scripts/api-test.sh [endpoint] [method] [data]

BASE_URL="http://localhost:3000"
API_PREFIX="/api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper function to make API calls
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    
    echo -e "${BLUE}🚀 Testing: ${method} ${endpoint}${NC}"
    echo -e "${YELLOW}📡 URL: ${BASE_URL}${endpoint}${NC}"
    
    if [ -n "$data" ]; then
        echo -e "${YELLOW}📄 Data: ${data}${NC}"
        curl -X "$method" \
             -H "Content-Type: application/json" \
             -d "$data" \
             "${BASE_URL}${endpoint}" \
             -w "\n📊 Status: %{http_code}\n⏱️  Time: %{time_total}s\n" \
             -s
    else
        curl -X "$method" \
             -H "Content-Type: application/json" \
             "${BASE_URL}${endpoint}" \
             -w "\n📊 Status: %{http_code}\n⏱️  Time: %{time_total}s\n" \
             -s
    fi
    
    echo -e "\n${GREEN}✅ Request completed${NC}\n"
}

# Predefined test functions
test_health() {
    echo -e "${GREEN}🏥 Testing Health Check...${NC}"
    api_call "GET" "/health"
}

test_bank_receipts() {
    echo -e "${GREEN}🏦 Testing Bank Receipts...${NC}"
    
    # You need to replace this with a real Payment Advice ID
    local pa_id="60f7b3b3b3b3b3b3b3b3b3b3"
    local timestamp=$(date +%s)
    
    local data='{
        "paymentAdviceId": "'$pa_id'",
        "bankReceipts": [
            {
                "receiptNumber": "TEST-'$timestamp'",
                "receiptDate": "'$(date -u +%Y-%m-%dT%H:%M:%S.000Z)'",
                "amount": 5000.00,
                "bankName": "Test Bank",
                "accountNumber": "**********",
                "s3Url": "https://example.com/test-receipt.pdf",
                "remark": "CLI Test Payment"
            }
        ]
    }'
    
    api_call "PATCH" "${API_PREFIX}/payment-advice/bank-receipts" "$data"
}

test_payment_advice_list() {
    echo -e "${GREEN}📋 Testing Payment Advice List...${NC}"
    
    local data='{
        "data": "encrypted_data_here"
    }'
    
    api_call "POST" "${API_PREFIX}/payment-advice/list" "$data"
}

# Main script logic
case "$1" in
    "health")
        test_health
        ;;
    "bank-receipts")
        test_bank_receipts
        ;;
    "pa-list")
        test_payment_advice_list
        ;;
    "custom")
        if [ $# -lt 3 ]; then
            echo -e "${RED}❌ Usage: $0 custom METHOD ENDPOINT [DATA]${NC}"
            exit 1
        fi
        api_call "$2" "$3" "$4"
        ;;
    *)
        echo -e "${BLUE}🔧 UzmaCloud API Testing Script${NC}"
        echo ""
        echo -e "${YELLOW}Usage:${NC}"
        echo "  $0 health                    # Test health endpoint"
        echo "  $0 bank-receipts            # Test bank receipts endpoint"
        echo "  $0 pa-list                  # Test payment advice list"
        echo "  $0 custom METHOD ENDPOINT [DATA]  # Custom API call"
        echo ""
        echo -e "${YELLOW}Examples:${NC}"
        echo "  $0 health"
        echo "  $0 bank-receipts"
        echo "  $0 custom GET /api/payment-advice/list"
        echo "  $0 custom PATCH /api/payment-advice/bank-receipts '{\"paymentAdviceId\":\"...\"}'"
        ;;
esac
