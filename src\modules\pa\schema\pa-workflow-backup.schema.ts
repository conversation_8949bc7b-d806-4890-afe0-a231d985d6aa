// import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
// import { Document, Types } from 'mongoose';

// export type ApprovalWorkflowDocument = ApprovalWorkflow & Document;

// @Schema({ timestamps: true, collection: 'pa_workflow' })
// export class ApprovalWorkflow {
//   @Prop({ type: Types.ObjectId, ref: 'PaymentAdvice', required: true })
//   paymentAdviceId: Types.ObjectId;  // Links to the Payment Advice

//   @Prop([
//     {
//       userId: { type: Types.ObjectId, ref: 'User', required: true },
//       stepNumber: { type: Number, required: true },
//       approvalType: { type: String, required: true },  // "Approved By" or "Reviewed By"
//       status: { type: String, enum: ['pending', 'approved', 'rejected'], default: 'pending' },
//       approvalDate: { type: Date },
//       comments: { type: String },
//     }
//   ])
//   steps: [
//     {
//       userId: Types.ObjectId;  // User responsible for this step
//       stepNumber: number;  // Step number in the approval process
//       approvalType: string;  // "Approved By" or "Reviewed By"
//       status: string;  // Status of this step (pending, approved, rejected)
//       approvalDate?: Date;  // When the approval/rejection was done
//       comments?: string;  // Any comments on the step
//     }
//   ];

//   @Prop({ type: Number, default: 1 })
//   currentStep: number;  // Tracks which step is currently active

//   @Prop({ type: Boolean, default: false })
//   isCompleted: boolean;  // Marks when the workflow is finished
// }

// export const PaWorkflowSchema = SchemaFactory.createForClass(ApprovalWorkflow);
