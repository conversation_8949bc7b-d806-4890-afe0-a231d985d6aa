// pa-message.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CreateMessageDto } from '../dto/create-message.dto';
import { PaymentAdviceMessage, PaymentAdviceMessageDocument } from '../schema/pa-message.schema';
import { ApprovalWorkflow, ApprovalWorkflowDocument } from '../schema/pa-workflow.schema';
import { PaymentAdvice, PaymentAdviceDocument } from '../schema/pa-main.schema';
import { User, UserDocument } from 'src/modules/user/schema/user.schema';
import { EmailScheduler, EmailSchedulerDocument } from 'src/modules/common/schema/pa-email.schema';

@Injectable()
export class PaymentAdviceMessageService {
  constructor(
    @InjectModel(PaymentAdviceMessage.name) private messageModel: Model<PaymentAdviceMessageDocument>,
    @InjectModel(PaymentAdvice.name) private paymentAdviceModel: Model<PaymentAdviceDocument>,
    @InjectModel(ApprovalWorkflow.name) private approvalWorkflowModel: Model<ApprovalWorkflowDocument>,
    @InjectModel(EmailScheduler.name) private emailSchedulerModel: Model<EmailSchedulerDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) { }

  async create(createMessageDto: CreateMessageDto): Promise<PaymentAdviceMessage | any> {

    const ePA = await this.paymentAdviceModel
      .findById(createMessageDto.paymentAdviceId)
      .select('departmentCode') // Only return departmentCode from the root document
      .populate('departmentCode')
      .exec();

      // console.log('populatedAdvice', ePA);
    const department: any = ePA?.departmentCode;
    const departmentName = department?.description || 'Unknown';
    const departmentCode = department?.code || 'Unknown';

    const workflow = await this.approvalWorkflowModel
      .findOne({ paymentAdviceId: new Types.ObjectId(createMessageDto.paymentAdviceId) })
      .populate({
        path: 'steps.userId',
        select: '_id fullName email'
      })
      .populate({
        path: 'paymentAdviceId',
        select: '_id vendorName serialNumber currency amount'
      })
      .exec();

    if(!workflow) {
      throw new NotFoundException('Permission not found');
    }

    // Find the sender's information
    const sendBy = workflow.steps.find((step) => {
      return step.userId['_id'].toString() === createMessageDto.senderId;
    });

    if (sendBy) {
      // Get sender's name for the email
      const senderName = sendBy.userId['fullName'];

      // Loop through all steps and send emails to everyone except sender
      for (const step of workflow.steps) {
        // Skip sending email to the sender
        if (step.userId['_id'].toString() === createMessageDto.senderId) {
          continue;
        }

        let attachmentUrl = '';
        if (createMessageDto.attachmentUrl) {
          attachmentUrl = createMessageDto.attachmentUrl;
        }

        // Create and save email notification for each recipient
        const emailScheduler = new this.emailSchedulerModel({
          to: step.userId['email'],
          subject: `Payment Advice: ${workflow.paymentAdviceId['serialNumber']} Chat Activity`,
          body: `<p>Dear ${step.userId['fullName']}, <br/>
                     Payment advice - ${workflow.paymentAdviceId['serialNumber']} has a message from<b> ${senderName}</b>.<br/><br/>
                     Department - ${departmentCode}, ${departmentName}<br/>
                     Vendor Name - ${workflow.paymentAdviceId['vendorName']}<br/>
                     Amount - <b>${workflow.paymentAdviceId['currency']} ${workflow.paymentAdviceId['amount'].toFixed(2)}</b><br/><br/>
                     Message - <b>${createMessageDto.message}</b><br/><br/>
                     Attachment - <b>${attachmentUrl}</b><br/><br/>
                     Thank you.<br/><br/>
                     Best Regards,<br/>
                     UZMA ECO SYSTEM</p>`,
        });
        await emailScheduler.save();
      }
      const newMessage = new this.messageModel(createMessageDto);
      return newMessage.save();
    }
  }

  async findAllByPaymentAdviceId(paymentAdviceId: string): Promise<any> {
    return this.messageModel
      .find({ paymentAdviceId: paymentAdviceId })
      .sort({ createdAt: 1 })
      .populate('senderId', 'fullName role faceImage')
      .exec();

  }

  async markAsRead(messageId: string, userId: string): Promise<PaymentAdviceMessage | any> {
    return this.messageModel.findByIdAndUpdate(
      messageId,
      { $addToSet: { readBy: new Types.ObjectId(userId) } },
      { new: true },
    ).exec();
  }

  async getUnreadMessageCount(body: any): Promise<number> {
    const count = await this.messageModel.countDocuments({
      paymentAdviceId: new Types.ObjectId(body.paymentAdviceId),
      readBy: { $ne: new Types.ObjectId(body.userId) }
    }).exec();
    return count;
  }
}