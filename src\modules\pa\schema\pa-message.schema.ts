// pa-message.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type PaymentAdviceMessageDocument = PaymentAdviceMessage & Document;

@Schema({ timestamps: true, collection: 'pa_messages' })
export class PaymentAdviceMessage {
  @Prop({ type: Types.ObjectId, ref: 'PaymentAdvice', required: true })
  paymentAdviceId: Types.ObjectId;  // Link to the Payment Advice

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  senderId: Types.ObjectId;  // User who sent the message
  
  @Prop({ type: String, required: true })
  senderRole: string;  // Role of the sender (e.g., "Submitter", "Approver", "Reviewer")
  
  @Prop({ type: String, required: true })
  message: string;  // Message content
  
  @Prop({ type: Boolean, default: false })
  isRead: boolean;  // Flag to track if message has been read by recipient
  
  @Prop([{ type: Types.ObjectId, ref: 'User' }])
  readBy: Types.ObjectId[];  // List of users who have read this message
  
  @Prop({ type: String })
  attachmentUrl: string;  // Optional attachment
}

export const PaMessageSchema = SchemaFactory.createForClass(PaymentAdviceMessage);