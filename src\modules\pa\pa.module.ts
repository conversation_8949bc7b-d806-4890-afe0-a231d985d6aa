import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { FieldRequestLog, FieldRequestLogSchema } from './schema/fr-main-log.schema';
import { FieldRequest, FieldRequestSchema } from './schema/fr-main.schema';
import { FieldRequestWorkflow, FieldRequestWorkflowSchema } from './schema/fr-workflow.schema';
import { LOA, LOASchema } from './schema/pa-loa.schema';
import { PaMainSchema, PaymentAdvice } from './schema/pa-main.schema';
import { PaMessageSchema, PaymentAdviceMessage } from './schema/pa-message.schema';
import { PASerialNumber, PASerialNumberSchema } from './schema/pa-serial-no.schema';
import { ApprovalWorkflow, PaWorkflowSchema } from './schema/pa-workflow.schema';
import { PurchaseRequest, PurchaseRequestSchema } from './schema/pr-main.schema';
import { EmailController } from '../common/controller/email.controller';
import { FieldRequisitionController } from './controller/fr.controller';
import { LoaController } from './controller/pa-loa.controller';
import { PaymentAdviceMessageController } from './controller/pa-message.controller';
import { PaymentAdviceController } from './controller/pa.controller';
import { EmailSchedulerService } from '../common/services/email-scheduler.service';
import { EmailService } from './service/email.service';
import { FieldRequestService } from './service/fr.service';
import { LoaService } from './service/pa-loa.service';
import { PaymentAdviceMessageService } from './service/pa-message.service';
import { PASerialNumberService } from './service/pa-serial-no.service';
import { PaymentAdviceService } from './service/pa.service';
import { CommonModule } from '../common/common.module';
import { UserModule } from '../user/user.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: FieldRequestLog.name, schema: FieldRequestLogSchema },
      { name: FieldRequest.name, schema: FieldRequestSchema },
      { name: FieldRequestWorkflow.name, schema: FieldRequestWorkflowSchema },
      { name: LOA.name, schema: LOASchema },
      { name: PaymentAdvice.name, schema: PaMainSchema },
      { name: PaymentAdviceMessage.name, schema: PaMessageSchema },
      { name: PASerialNumber.name, schema: PASerialNumberSchema },
      { name: ApprovalWorkflow.name, schema: PaWorkflowSchema },
      { name: PurchaseRequest.name, schema: PurchaseRequestSchema }
    ]),
    CommonModule,
    UserModule
  ],
  controllers: [EmailController, FieldRequisitionController, LoaController, PaymentAdviceMessageController, PaymentAdviceController],
  providers: [EmailSchedulerService, EmailService, FieldRequestService, LoaService, PaymentAdviceMessageService, PASerialNumberService, PaymentAdviceService],
  exports: [],
})
export class PaymentAdviceModule {}