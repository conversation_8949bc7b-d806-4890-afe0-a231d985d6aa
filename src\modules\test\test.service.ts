import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Test, TestDocument } from './schemas/test.schema';

@Injectable()
export class TestsService {
  constructor(
    @InjectModel(Test.name) private userModel: Model<TestDocument>,
  ) {}

  async getProfile() {
    const userCount = await this.userModel.countDocuments();
    const activeUsers = await this.userModel.countDocuments({ isActive: true });
    
    return {
      message: 'User management system',
      stats: {
        totalUsers: userCount,
        activeUsers: activeUsers,
        inactiveUsers: userCount - activeUsers,
      },
      sampleOperations: {
        createSampleUser: 'POST /api/v1/users/sample',
        listUsers: 'GET /api/v1/users',
        getUserStats: 'GET /api/v1/users/stats',
      },
      database: {
        connected: true,
        collection: 'users',
      },
    };
  }

  async createSampleUser() {
    const sampleUser = {
      email: `user-${Date.now()}@uzmacloud.com`,
      firstName: 'Sample',
      lastName: 'User',
      isActive: true,
      role: 'user',
      metadata: {
        source: 'api_demo',
        createdVia: 'sample_endpoint',
      },
    };

    try {
      const user = new this.userModel(sampleUser);
      const savedUser = await user.save();
      
      return {
        success: true,
        message: 'Sample user created successfully',
        user: {
          id: savedUser._id,
          email: savedUser.email,
          firstName: savedUser.firstName,
          lastName: savedUser.lastName,
          role: savedUser.role,
          createdAt: savedUser.createdAt,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to create sample user',
        error: error.message,
      };
    }
  }

  async findAll() {
    try {
      const users = await this.userModel
        .find()
        .select('-__v')
        .sort({ createdAt: -1 })
        .limit(10)
        .exec();

      return {
        success: true,
        count: users.length,
        users: users.map(user => ({
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          isActive: user.isActive,
          createdAt: user.createdAt,
        })),
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to fetch users',
        error: error.message,
      };
    }
  }

  async getStats() {
    try {
      const [
        totalUsers,
        activeUsers,
        usersByRole,
        recentUsers,
      ] = await Promise.all([
        this.userModel.countDocuments(),
        this.userModel.countDocuments({ isActive: true }),
        this.userModel.aggregate([
          { $group: { _id: '$role', count: { $sum: 1 } } }
        ]),
        this.userModel.countDocuments({
          createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
        }),
      ]);

      return {
        success: true,
        stats: {
          total: totalUsers,
          active: activeUsers,
          inactive: totalUsers - activeUsers,
          recentlyCreated: recentUsers,
          byRole: usersByRole.reduce((acc, item) => {
            acc[item._id] = item.count;
            return acc;
          }, {}),
        },
        database: {
          connected: true,
          collection: 'users',
        },
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to get user statistics',
        error: error.message,
      };
    }
  }

  async findByEmail(email: string): Promise<Test | null> {
    return this.userModel.findOne({ email }).exec();
  }
}