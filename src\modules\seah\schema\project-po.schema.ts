import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ProjectPODocument = ProjectPO & Document;

@Schema({ timestamps: true, collection: 'project_po' })
export class ProjectPO {
  // Make this field unique to avoid duplicates
  @Prop()
  PONo: string;

  @Prop()
  BPCode: string;

  @Prop()
  BPName: string;

  @Prop()
  PODate: string;

  @Prop()
  Buyer: string;

  @Prop()
  GrossRM: number;

  @Prop()
  TaxRM: number;

  @Prop()
  TotalRM: number;

  @Prop()
  currency: string;

  @Prop()
  TotalOri: number;

  @Prop()
  term: string;

  @Prop()
  pjmCode: string;

  @Prop()
  signature: string;

  @Prop()
  poStatus: string;

  @Prop()
  grnStatus: string;

  @Prop()
  invoiceStatus: string;

  @Prop()
  department: string;

  @Prop()
  prjCode: string;

  @Prop()
  ppmIssued: number;

  @Prop()
  ppmpaid: number;

  @Prop()
  isExcluded: boolean;

  @Prop()
  remark: string;

  @Prop()
  discountAmount: number;

  @Prop()
  discountRemark: string;

  @Prop()
  defaultAmount: number;

  @Prop()
  isDefault: boolean;

  @Prop()
  type: string; //PO, VO, Consultant
}

export const ProjectPOSchema = SchemaFactory.createForClass(ProjectPO);
