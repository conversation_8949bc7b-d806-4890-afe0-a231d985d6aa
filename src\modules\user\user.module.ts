import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from './schema/user.schema';
import { UserController } from './controller/user.controller';
import { UserService } from './service/user.service';
import { ApprovalFlow, ApprovalFlowSchema } from '../auto-billing/schema/approval-flow.schema';
import { Department, DepartmentSchema } from './schema/department.schema';
import { Company, CompanySchema } from './schema/company.schema';
import { Otp, OtpSchema } from './schema/otp.schema';
import { CompanyController } from './controller/company.controller';
import { DepartmentController } from './controller/department.controller';
import { OtpController } from './controller/otp.controller';
import { CompanyService } from './service/company.service';
import { DepartmentService } from './service/department.service';
import { OtpService } from './service/otp.service';
import { SecurityService } from './service/security.service';
import { TestService } from './service/test.service';
import { S3Service } from '../common/services/s3.service';
import { CommonModule } from '../common/common.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Company.name, schema: CompanySchema},
      { name: Department.name, schema: DepartmentSchema},
      { name: Otp.name, schema: OtpSchema },
      { name: User.name, schema: UserSchema }
    ]),
    CommonModule,
    AuthModule
  ],
  controllers: [CompanyController, DepartmentController, OtpController, UserController],
  providers: [CompanyService, DepartmentService, OtpService, SecurityService, TestService, UserService, S3Service],
  exports: [UserService, SecurityService, MongooseModule],
})
export class UserModule {}