import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsArray, IsNumber, Min, Max, ValidateNested, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class ObjectiveDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  keyResultsArea: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  workObjective: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  targetBase: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  targetExceed: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  targetStretch: string;

  @ApiProperty()
  @IsNumber()
  @Min(0)
  @Max(100)
  weightage: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  yearEndResults: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  performanceAssessment: string;

  @ApiProperty()
  @IsNumber()
  @Min(1)
  @Max(5)
  employeeRating: number;
}

export class DevelopmentPlanDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  competencyType: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  areaForImprovement: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  activity: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  timeline: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  yearEndComments?: string;
}

export class AttachmentDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  fileName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  filePath: string;

  @ApiProperty()
  @IsNumber()
  fileSize: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  mimeType: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;
}

export class CreateIPPADto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  employeeEmail: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  financialYear: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  reviewPeriod: string;

  @ApiProperty({ type: [ObjectiveDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ObjectiveDto)
  objectives: ObjectiveDto[];

  @ApiProperty({ type: [DevelopmentPlanDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DevelopmentPlanDto)
  developmentPlans: DevelopmentPlanDto[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  employeeRemarks?: string;

  @ApiProperty({ type: [AttachmentDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AttachmentDto)
  employeeAttachments?: AttachmentDto[];
}

export class SaveDraftDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  financialYear: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  reviewPeriod?: string;

  @ApiProperty({ type: [ObjectiveDto], required: false })
  @IsOptional()
  @IsArray()
  objectives?: ObjectiveDto[];

  @ApiProperty({ type: [DevelopmentPlanDto], required: false })
  @IsOptional()
  @IsArray()
  developmentPlans?: DevelopmentPlanDto[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  employeeRemarks?: string;

  @ApiProperty({ type: [AttachmentDto], required: false })
  @IsOptional()
  @IsArray()
  employeeAttachments?: AttachmentDto[];
}

// dto/supervisor-review.dto.ts
export class ObjectiveReviewDto {
  @ApiProperty()
  @IsNumber()
  objectiveIndex: number;

  @ApiProperty()
  @IsNumber()
  @Min(1)
  @Max(5)
  supervisorRating: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  supervisorComments?: string;
}

export class SupervisorReviewDto {
  @ApiProperty({ type: [ObjectiveReviewDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ObjectiveReviewDto)
  objectives: ObjectiveReviewDto[];

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  remarks: string;

  @ApiProperty({ type: [AttachmentDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AttachmentDto)
  attachments?: AttachmentDto[];
}

// dto/hr-review.dto.ts
export class HRReviewDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  remarks: string;

  @ApiProperty({ type: [AttachmentDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AttachmentDto)
  attachments?: AttachmentDto[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  finalComments?: string;
}