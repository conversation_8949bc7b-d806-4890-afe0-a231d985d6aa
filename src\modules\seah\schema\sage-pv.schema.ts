import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type SageReportRPADocument = SageReportRPA & Document;

@Schema({ timestamps: true, collection: 'sage_rpa_log' }) 
export class SageReportRPA {

    @Prop()
    type: string;

    @Prop()
    remark: string;

    @Prop()
    totalRecord: string;

    @Prop()
    totalAmount: number;

    @Prop()
    isPosted: boolean;

    @Prop()
    postedDateTime: boolean;

    @Prop()
    isSuccess: boolean;
}

export const SageReportRPASchema = SchemaFactory.createForClass(SageReportRPA);
