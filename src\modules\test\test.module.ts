import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Test, TestSchema } from './schemas/test.schema';
import { TestsController } from './test.controller';
import { TestsService } from './test.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Test.name, schema: TestSchema }
    ]),
  ],
  controllers: [TestsController],
  providers: [TestsService],
  exports: [TestsService], // Export for use in auth module
})
export class TestModule {}