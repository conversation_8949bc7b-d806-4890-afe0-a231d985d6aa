import { Types } from 'mongoose';
import { IppaUserDocument } from '../schema/ippa-user.schema';

export interface PopulatedUser {
  _id: Types.ObjectId;
  fullName: string;
  email: string;
  firstName?: string;
  lastName?: string;
}

export interface PopulatedDepartment {
  _id: Types.ObjectId;
  code: string;
  description: string;
  manager?: PopulatedUser;
  hod?: PopulatedUser;
  ceo?: PopulatedUser;
}

export interface PopulatedIppaUser extends Omit<IppaUserDocument, 'lineManager' | 'departmentRef'> {
  lineManager?: Types.ObjectId | PopulatedUser;
  departmentRef?: Types.ObjectId | PopulatedDepartment;
}

// Type guard functions
export function isPopulatedUser(user: Types.ObjectId | PopulatedUser | undefined): user is PopulatedUser {
  return user != null && typeof user === 'object' && 'fullName' in user;
}

export function isPopulatedDepartment(dept: Types.ObjectId | PopulatedDepartment | undefined): dept is PopulatedDepartment {
  return dept != null && typeof dept === 'object' && 'code' in dept;
}