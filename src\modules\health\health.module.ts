import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { HealthController } from './controller/health.controller';
import { HealthService } from './service/health.service';

@Module({
  imports: [
    // Health module can access the database connection
    MongooseModule.forFeature([]),
  ],
  controllers: [HealthController],
  providers: [HealthService],
  exports: [HealthService], // Export for use in other modules
})
export class HealthModule {}