import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as xlsx from 'xlsx';
import { Department } from '../schema/department.schema';

interface DepartmentEntry {
  code: string;
  description: string;
  dimensionType: string;
  isActive: string;  // 'Yes' or 'No' in the Excel file
  manager: string;
  hod: string;
  ceo: string;
}

@Injectable()
export class DepartmentService {
  constructor(
    @InjectModel(Department.name) private departmentModel: Model<Department>,
  ) {}

  async uploadDepartments(fileBuffer: Buffer): Promise<void> {
    const workbook = xlsx.read(fileBuffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const data: DepartmentEntry[] = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    for (const entry of data) {
      const department = new this.departmentModel({
        code: entry.code,
        description: entry.description,
        dimensionType: entry.dimensionType,
        isActive: entry.isActive === 'Yes',  // Convert 'Yes'/'No' to true/false
        manager: entry.manager,
        hod: entry.hod,
        ceo: entry.ceo
      });
      await department.save();
    }
  }

   // Method to get a department by its code and populate manager, hod, and ceo
   async getDepartmentByCode(code: string): Promise<Department> {
    const department = await this.departmentModel
      .findOne({ code })
      .populate('manager', '_id name email fullName faceImage designation')  // Populate manager field with name and email
      .populate('hod', '_id name email fullName faceImage designation')      // Populate hod field with name and email
      .populate('ceo', '_id name email fullName faceImage designation')      // Populate ceo field with name and email
      .exec();

    if (!department) {
      throw new NotFoundException(`Department with code ${code} not found`);
    }

    return department;
  }

  async getAllDepartmentIds(): Promise<string[]> {
    // Fetch all department IDs and return them as a string array
    const departments = await this.departmentModel.find({}, '_id').exec();
    return departments.map(department => department._id.toString());
  }
}
