// import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsArray, IsBoolean, IsOptional, IsDate, IsMongoId, ValidateNested } from 'class-validator';
// import { Type } from 'class-transformer';

// class StepDTO {
//   @IsNumber()
//   stepNumber: number;

//   @IsString()
//   approvalType: string;

//   @IsString()
//   approver: string;

//   @IsString()
//   status: string;  // e.g., "pending", "approved", "rejected"

//   @IsDate()
//   approvalDate: Date;

//   @IsString()
//   comments: string;
// }

// export class CreateApprovalWorkflowDTO {
//   @IsMongoId()
//   paymentAdviceId: string;

//   @IsArray()
//   @ValidateNested({ each: true })
//   @Type(() => StepDTO)
//   steps: StepDTO[];

//   @IsNumber()
//   currentStep: number;

//   @IsBoolean()
//   isCompleted: boolean;
// }
