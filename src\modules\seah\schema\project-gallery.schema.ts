import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from 'src/modules/user/schema/user.schema';

@Schema({ timestamps: true, collection: 'project_gallery' }) // Custom collection name
export class ProjectGallery extends Document {
  @Prop({ required: true })
  site: string;  // Equivalent to 'site', representing the segment

  @Prop({ required: true })
  title: string;    // Title of the gallery section (e.g., based on date or a custom name)

  @Prop({ required: false })
  date: Date;       // Date of the gallery section

  @Prop([
    {
      fileName: { type: String, required: true },
      description: { type: String, required: true },
      url: { type: String, required: true },
    },
  ])
  files: {
    fileName: string;
    description: string;
    url: string;
  }[];  // Array of files with description and URL

  @Prop({ required: false })
  order: number;  // Represents the order of the gallery or the files

  @Prop({ type: Types.ObjectId, ref: 'User' })
  updatedBy: Types.ObjectId | User;  // User who updated the gallery
}

export const ProjectGallerySchema = SchemaFactory.createForClass(ProjectGallery);
