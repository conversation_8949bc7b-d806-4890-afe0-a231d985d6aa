import { Controller, Get } from '@nestjs/common';
import { HealthService } from '../service/health.service';

@Controller('api/v1/healths/')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get()
//   @ApiOperation({ summary: 'Health check endpoint' })
//   @ApiResponse({ 
//     status: 200, 
//     description: 'Service health status',
//     schema: {
//       type: 'object',
//       properties: {
//         status: { type: 'string', example: 'ok' },
//         timestamp: { type: 'string', example: '2025-01-01T00:00:00.000Z' },
//         uptime: { type: 'number', example: 123.456 },
//         environment: { type: 'string', example: 'production' },
//         database: {
//           type: 'object',
//           properties: {
//             connected: { type: 'boolean', example: true },
//             status: { type: 'string', example: 'healthy' }
//           }
//         }
//       }
//     }
//   })
  async checkHealth() {
    return await this.healthService.checkHealth();
  }

  @Get('detailed')
//   @ApiOperation({ summary: 'Detailed health check' })
//   @ApiResponse({ 
//     status: 200, 
//     description: 'Detailed service health information',
//     schema: {
//       type: 'object',
//       properties: {
//         status: { type: 'string', example: 'ok' },
//         service: { type: 'string', example: 'uzmacloud-api' },
//         version: { type: 'string', example: '1.0.0' },
//         uptime: {
//           type: 'object',
//           properties: {
//             seconds: { type: 'number', example: 123.456 },
//             human: { type: 'string', example: '2h 3m 45s' }
//           }
//         },
//         system: {
//           type: 'object',
//           properties: {
//             nodeVersion: { type: 'string', example: 'v18.17.0' },
//             platform: { type: 'string', example: 'linux' },
//             arch: { type: 'string', example: 'x64' },
//             memory: {
//               type: 'object',
//               properties: {
//                 used: { type: 'number', example: 45 },
//                 total: { type: 'number', example: 128 }
//               }
//             }
//           }
//         },
//         database: {
//           type: 'object',
//           properties: {
//             connected: { type: 'boolean', example: true },
//             status: { type: 'string', example: 'healthy' },
//             host: { type: 'string', example: 'cluster.docdb.amazonaws.com' },
//             collections: { type: 'number', example: 3 }
//           }
//         }
//       }
//     }
//   })
  async checkDetailedHealth() {
    return await this.healthService.checkDetailedHealth();
  }

  @Get('ready')
//   @ApiOperation({ summary: 'Readiness probe' })
//   @ApiResponse({ status: 200, description: 'Service is ready to accept traffic' })
  async checkReadiness() {
    const health = await this.healthService.checkHealth();
    
    if (health.status === 'ok' && health.database?.connected) {
      return { status: 'ready', message: 'Service is ready to accept traffic' };
    }
    
    throw new Error('Service not ready');
  }

  @Get('live')
//   @ApiOperation({ summary: 'Liveness probe' })
//   @ApiResponse({ status: 200, description: 'Service is alive' })
  checkLiveness() {
    return { 
      status: 'alive', 
      timestamp: new Date().toISOString(),
      pid: process.pid 
    };
  }
}