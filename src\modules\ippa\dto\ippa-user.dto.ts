import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class GetIppaUserByEmailDto {
  @ApiProperty({
    description: 'Email address of the IPPA user',
    example: '<EMAIL>'
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;
}

export class IppaUserResponseDto {
  @ApiProperty({ description: 'User ID' })
  _id: string;

  @ApiProperty({ description: 'Username' })
  username: string;

  @ApiProperty({ description: 'Full name of the user' })
  fullName: string;

  @ApiProperty({ description: 'Email address' })
  email: string;

  @ApiProperty({ description: 'Company name' })
  company: string;

  @ApiProperty({ description: 'Group' })
  group: string;

  @ApiProperty({ description: 'Business unit' })
  businessUnit: string;

  @ApiProperty({ description: 'Employee ID' })
  employeeId: string;

  @ApiProperty({ description: 'Employee grade' })
  employeeGrade: string;

  @ApiProperty({ description: 'Position' })
  position: string;

  @ApiProperty({ description: 'Position level' })
  positionLevel: string;

  @ApiProperty({ description: 'Division' })
  division: string;

  @ApiProperty({ description: 'Department name' })
  department: string;

  @ApiProperty({ description: 'Department information', required: false })
  departmentInfo?: {
    _id: string;
    code: string;
    description: string;
    manager?: {
      _id: string;
      fullName: string;
      email: string;
    };
    hod?: {
      _id: string;
      fullName: string;
      email: string;
    };
  };

  @ApiProperty({ description: 'Department code' })
  departmentCode: string;

  @ApiProperty({ description: 'Department ID' })
  departmentId: string;

  @ApiProperty({ description: 'Section' })
  section: string;

  @ApiProperty({ description: 'Line manager information', required: false })
  lineManagerInfo?: {
    _id: string;
    fullName: string;
    email: string;
    designation: string;
  };

  @ApiProperty({ description: 'Work location' })
  workLocation: string;

  @ApiProperty({ description: 'Joined date' })
  joinedDate: Date;

  @ApiProperty({ description: 'Active status' })
  isActive: boolean;
}