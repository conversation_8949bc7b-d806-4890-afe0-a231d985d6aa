// ippa-message.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type IPPAMessageDocument = IPPAMessage & Document;

@Schema({ timestamps: true, collection: 'ippa_messages' })
export class IPPAMessage {
  @Prop({ type: Types.ObjectId, ref: 'IPPASubmission', required: true })
  ippaSubmissionId: Types.ObjectId;  // Link to the IPPA Submission

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  senderId: Types.ObjectId;  // User who sent the message
  
  @Prop({ type: String, required: true })
  senderRole: string;  // Role of the sender (e.g., "Employee", "Supervisor", "HR", "HOD")
  
  @Prop({ type: String, required: true })
  message: string;  // Message content
  
  @Prop({ type: Boolean, default: false })
  isRead: boolean;  // Flag to track if message has been read by recipient
  
  @Prop([{ type: Types.ObjectId, ref: 'User' }])
  readBy: Types.ObjectId[];  // List of users who have read this message
  
  @Prop({ type: String })
  attachmentUrl: string;  // Optional attachment
}

export const IPPAMessageSchema = SchemaFactory.createForClass(IPPAMessage);