// import { IsS<PERSON>, Is<PERSON><PERSON>ber, IsArray, IsObject, IsBoolean, IsOptional, ValidateNested, IsMongoId, IsNotEmpty } from 'class-validator';
// import { Type } from 'class-transformer';

// class PaymentTermDTO {
//   @IsNumber()
//   days: number;

//   @IsString()
//   termType: string;  // e.g., "back_to_back", "advance_payment"
// }

// class DocumentDTO {
//   @IsString()
//   invoiceNumber: string;

//   @IsString()
//   poNumber: string;

//   @IsString()
//   projectCode: string;

//   @IsNumber()
//   amount: number;

//   @IsArray()
//   attachments: Array<{ type: string; s3Url: string }>;
// }

// class ChargeBackDTO {
//   @IsString()
//   glCode: string;

//   @IsNumber()
//   amount: number;
// }

// class WithholdingTaxDTO {
//   @IsNumber()
//   percentage: number;

//   @IsObject()
//   trfAttachment: { s3Url: string };

//   @IsObject()
//   verifiedBy: { name: string; date: Date };
// }

// class WorkflowParticipantDTO {
//   @IsMongoId()
//   userId: string;  // User ID of the participant

//   @IsString()
//   @IsNotEmpty()
//   approvalType: string;  // Could be 'Approved By' or 'Reviewed By'
// }

// export class CreatePaymentAdviceDTO {
//   @IsString()
//   companyName: string;

//   @IsString()
//   companyAddress: string;

//   @IsString()
//   telNumber: string;

//   @IsString()
//   faxNumber: string;

//   @IsString()
//   companyLogo: string;

//   @IsString()
//   departmentCode: string;

//   @IsString()
//   vendorName: string;

//   @IsString()
//   vendorAddress: string;

//   @ValidateNested()
//   @Type(() => PaymentTermDTO)
//   paymentTerm: PaymentTermDTO;

//   @IsArray()
//   @ValidateNested({ each: true })
//   @Type(() => DocumentDTO)
//   documents: DocumentDTO[];

//   @IsArray()
//   @ValidateNested({ each: true })
//   @Type(() => WorkflowParticipantDTO)
//   workflowParticipants: WorkflowParticipantDTO[];  // Array of workflow participants with approval types

//   @IsString()
//   detailsPayment: string;

//   @IsNumber()
//   @IsNotEmpty()
//   amount: number;  // Payment amount

//   @IsString()
//   apbBudgeted: string;

//   @IsString()
//   serviceLocation: string;

//   @IsString()
//   staffConsultant: string;

//   @IsBoolean()
//   chargeable: boolean;

//   @IsString()
//   accountManager: string;

//   @IsString()
//   costCategory: string;

//   @IsArray()
//   @ValidateNested({ each: true })
//   @Type(() => ChargeBackDTO)
//   chargeBacks: ChargeBackDTO[];

//   @ValidateNested()
//   @Type(() => WithholdingTaxDTO)
//   withholdingTax: WithholdingTaxDTO;

//   // @IsMongoId()
//   approvalWorkflowId: string;

//   @IsString()
//   status: string;  // e.g., "submitted", "approved", "rejected", "pending"
// }
