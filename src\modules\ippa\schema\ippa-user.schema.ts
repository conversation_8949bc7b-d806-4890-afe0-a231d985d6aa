import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type IppaUserDocument = IppaUser & Document;

@Schema({ timestamps: true, collection: 'ippa_user' })
export class IppaUser {
  _id: Types.ObjectId;

  @Prop({ required: true, index: true })
  username: string;

  @Prop({ required: true, index: true })
  fullName: string;

  @Prop({ required: true, unique: true, index: true })
  email: string;

  @Prop({ required: true, index: true })
  company: string;

  @Prop({ required: true, index: true })
  group: string;

  @Prop({ required: true, index: true })
  businessUnit: string;

  @Prop({ required: true, unique: true, index: true })
  employeeId: string;

  @Prop({ required: true, index: true })
  employeeGrade: string;

  @Prop({ required: true, index: true })
  position: string;

  @Prop({ required: true, index: true })
  positionLevel: string;

  @Prop({ required: true, index: true })
  division: string;

  @Prop({ required: true })
  department: string; // Keep as string for backward compatibility

  @Prop({ type: Types.ObjectId, ref: 'Department', index: true })
  departmentRef: Types.ObjectId; // Reference to Department table

  @Prop({ required: true })
  departmentCode: string;

  @Prop({ required: false, default: 'UNKNOWN' }) // Made optional with default
  departmentId: string;

  @Prop({ required: true, index: true })
  section: string;

  @Prop({ type: Types.ObjectId, ref: 'User', index: true })
  lineManager: Types.ObjectId; // Reference to User table

  @Prop({ required: true, index: true })
  workLocation: string;

  @Prop({ required: true, index: true })
  joinedDate: Date;

  @Prop({ default: true, index: true })
  isActive: boolean;
}

export const IppaUserSchema = SchemaFactory.createForClass(IppaUser);

// Additional compound indexes for common query patterns
IppaUserSchema.index({ company: 1, businessUnit: 1 });
IppaUserSchema.index({ departmentRef: 1, isActive: 1 });
IppaUserSchema.index({ lineManager: 1, isActive: 1 });
IppaUserSchema.index({ employeeGrade: 1, positionLevel: 1 });
IppaUserSchema.index({ workLocation: 1, isActive: 1 });
IppaUserSchema.index({ joinedDate: 1, isActive: 1 });

// Text index for search functionality
IppaUserSchema.index({ 
  fullName: 'text', 
  email: 'text', 
  position: 'text',
  department: 'text'
});