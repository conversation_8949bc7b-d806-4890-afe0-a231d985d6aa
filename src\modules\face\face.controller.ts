import { Controller, Post, UseInterceptors, UploadedFile, Query, Body, Get, Param } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FaceComparisonService } from './face-comparison.service';
import { SecurityService } from '../user/service/security.service';


@Controller('face')
export class FaceController {
  constructor(private readonly faceComparisonService: FaceComparisonService,
    private readonly securityService: SecurityService
  ) { }

  @Post('compare')
  @UseInterceptors(FileInterceptor('file'))
  async compareImage(@UploadedFile() file: Express.Multer.File, @Body('s3Key') s3Key: string) {
    if (!s3Key) {
      throw new Error('S3 key is required');
    }

    // Retrieve the target image from S3
    const s3Image = await this.faceComparisonService.getImageFromS3ByKey(s3Key);
    if (!s3Image) {
      return { match: false, message: 'Image not found in S3' };
    }
    // Compare the local image with the S3 image
    // const isMatch = await this.faceComparisonService.compareFace(file.buffer, s3Image);
    const result = await this.faceComparisonService.searchFaces(file.buffer);
    return { data: this.securityService.encrypt(result) };
  }

  // Create Face Live Session
  @Get('create-face-liveness')
  async createFaceLiveness() {
    return this.faceComparisonService.createLivenessSession();
  }

  // Get Face Live Session
  @Get('get-face-liveness')
  async getFaceLiveness(@Query('sessionId') sessionId: string) {
    return this.faceComparisonService.getLivenessResults(sessionId);
  }
}
