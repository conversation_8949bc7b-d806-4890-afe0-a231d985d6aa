import { Controller, Get, Param, Post, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { DepartmentService } from '../service/department.service';
import { Department } from '../schema/department.schema';
import { CompanyService } from '../service/company.service';

@Controller('companies')
export class CompanyController {
  constructor(private readonly companyService: CompanyService) { }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    await this.companyService.uploadCompanies(file.buffer);
    return { message: 'Companies uploaded successfully' };
  }

  @Post('all')
  async getCompanies(): Promise<any> {
    return this.companyService.getAllCompanies();
  }

  // API to get department by code with populated manager, hod, and ceo
  @Get(':code')
  async getCompanyByCode(@Param('code') code: string): Promise<any> {
    return this.companyService.getCompanyByCode(code);
  }

  @Post('ids')
  async getCompanyIds(): Promise<string[]> {
    return this.companyService.getAllCompanyIds();
  }
}
