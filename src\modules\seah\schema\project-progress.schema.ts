import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from 'src/modules/user/schema/user.schema';

@Schema({ timestamps: true, collection: 'project_progress' }) // Custom collection name
export class ProjectProgress extends Document {
  @Prop({ required: true })
  percentage: number;

  @Prop({ required: true })
  baseline: number;

  @Prop({ required: false })
  variance: number;

  @Prop({ required: false })
  liveDate: string;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  updatedBy: Types.ObjectId | User;
}

export const ProjectProgressSchema = SchemaFactory.createForClass(ProjectProgress);
