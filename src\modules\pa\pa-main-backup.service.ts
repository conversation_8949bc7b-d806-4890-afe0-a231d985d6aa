// import { Injectable, NotFoundException } from '@nestjs/common';
// import { InjectModel } from '@nestjs/mongoose';
// import { Model, Types } from 'mongoose';
// import { PaymentAdvice, PaymentAdviceDocument } from './schema/pa-main-backup.schema';
// import { CreatePaymentAdviceDTO } from './dto/pa-main.dto';
// import { ApprovalWorkflow, ApprovalWorkflowDocument } from './schema/pa-workflow-backup.schema';

// @Injectable()
// export class PaymentAdviceService {
//   constructor(
//     @InjectModel(PaymentAdvice.name) private paymentAdviceModel: Model<PaymentAdviceDocument>,
//     @InjectModel(ApprovalWorkflow.name) private approvalWorkflowModel: Model<ApprovalWorkflowDocument>,
//   ) { }

//   async createPaymentAdvice(createPaymentAdviceDto: CreatePaymentAdviceDTO, userId: string): Promise<PaymentAdvice> {
//     const { workflowParticipants, ...paymentAdviceData } = createPaymentAdviceDto;

//     // Transform workflowParticipants to proper Mongoose Types.ObjectId and save
//     const participants = workflowParticipants.map(participant => ({
//       userId: new Types.ObjectId(participant.userId),
//       approvalType: participant.approvalType,
//     }));

//     // Create Payment Advice first and save it to get the _id
//     const paymentAdvice = new this.paymentAdviceModel({
//       ...paymentAdviceData,
//       createdBy: new Types.ObjectId(userId),
//       status: 'submitted',
//       workflowParticipants: participants,  // Assign transformed participants
//     });

//     const savedPaymentAdvice = await paymentAdvice.save();

//     // Create Approval Workflow
//     const workflowSteps = workflowParticipants.map((participant, index) => ({
//       userId: new Types.ObjectId(participant.userId),
//       stepNumber: index + 1,
//       approvalType: participant.approvalType,
//       status: 'pending',
//     }));

//     const approvalWorkflow = new this.approvalWorkflowModel({
//       paymentAdviceId: savedPaymentAdvice._id,
//       steps: workflowSteps,
//       currentStep: 1,
//       isCompleted: false,
//     });

//     const savedWorkflow = await approvalWorkflow.save();

//     // Update Payment Advice with Approval Workflow ID
//     savedPaymentAdvice.approvalWorkflowId = savedWorkflow._id as Types.ObjectId;
//     await savedPaymentAdvice.save();

//     // Populate Approval Workflow
//     const populatedPaymentAdvice = await this.paymentAdviceModel
//       .findById(savedPaymentAdvice._id)
//       .populate({
//         path: 'approvalWorkflowId',
//         populate: {
//           path: 'steps.userId',
//           model: 'User',
//         }
//       })
//       .exec();

//     return populatedPaymentAdvice;
//   }

//   async findByCreatedBy(userId: string, page: number, limit: number): Promise<any> {
//     const objectId = new Types.ObjectId(userId);

//     // Calculate the number of documents to skip
//     const skip = (page - 1) * limit;

//     const paymentAdvices = await this.approvalWorkflowModel.aggregate([
//       {
//         // Lookup payment advice details based on createdBy field in pa_main collection
//         $lookup: {
//           from: 'pa_main',
//           localField: 'paymentAdviceId',
//           foreignField: '_id',
//           as: 'paymentAdvice'
//         }
//       },
//       {
//         // Unwind payment advice array to access the details individually
//         $unwind: "$paymentAdvice"
//       },
//       {
//         // Match the payment advice based on the createdBy user ID
//         $match: {
//           "paymentAdvice.createdBy": objectId
//         }
//       },
//       {
//         // Perform a lookup to retrieve the createdBy user details in the paymentAdvice document
//         $lookup: {
//           from: 'user',  // Use the 'user' collection for lookup
//           localField: 'paymentAdvice.createdBy',
//           foreignField: '_id',
//           as: 'createdByUser'
//         }
//       },
//       {
//         // Add the userDetails for the creator
//         $addFields: {
//           "paymentAdvice.createdByUser": { $arrayElemAt: ["$createdByUser", 0] }
//         }
//       },
//       {
//         // Unwind the steps array to get each step separately
//         $unwind: "$steps"
//       },
//       {
//         // Perform a lookup to retrieve the user details for each userId in steps
//         $lookup: {
//           from: 'user',
//           localField: 'steps.userId',
//           foreignField: '_id',
//           as: 'userDetails'
//         }
//       },
//       {
//         // Add userDetails to each step by merging the first element of the lookup result
//         $addFields: {
//           "steps.userDetails": { $arrayElemAt: ["$userDetails", 0] }
//         }
//       },
//       {
//         // Group back the steps into an array after adding the userDetails
//         $group: {
//           _id: "$_id",
//           paymentAdvice: { $first: "$paymentAdvice" },
//           steps: { $push: "$steps" }
//         }
//       },
//       {
//         // Add the $sort stage to order by paymentAdvice.createdAt in descending order
//         $sort: { "paymentAdvice.createdAt": -1 }
//       },
//       {
//         // Apply pagination using $skip and $limit
//         $skip: skip
//       },
//       {
//         $limit: limit
//       },
//       {
//         // Project the required fields for the final result, ensuring steps include user details
//         $project: {
//           _id: 0,
//           paymentAdvice: 1,
//           steps: 1
//         }
//       }
//     ]).exec();

//     // Get the total count of documents for pagination metadata
//     const totalRecords = await this.approvalWorkflowModel.aggregate([
//       {
//         $lookup: {
//           from: 'pa_main',
//           localField: 'paymentAdviceId',
//           foreignField: '_id',
//           as: 'paymentAdvice'
//         }
//       },
//       {
//         $unwind: "$paymentAdvice"
//       },
//       {
//         $match: {
//           "paymentAdvice.createdBy": objectId
//         }
//       },
//       {
//         $count: "total"
//       }
//     ]).exec();

//     // Get the total count value
//     const totalCount = totalRecords.length > 0 ? totalRecords[0].total : 0;

//     // If no records found, return an empty array and metadata
//     if (!paymentAdvices || paymentAdvices.length === 0) {
//       return {
//         data: [],
//         currentPage: page,
//         totalPages: 0,
//         totalRecords: 0,
//       };
//     }

//     // Return paginated response with metadata
//     return {
//       data: paymentAdvices.map(result => ({
//         ...result.paymentAdvice,
//         approvalWorkflow: {
//           steps: result.steps
//         }
//       })),
//       currentPage: page,
//       totalPages: Math.ceil(totalCount / limit),
//       totalRecords: totalCount
//     };
//   }

//   async findByParticipant(userId: string, page: number, limit: number): Promise<any> {
//     const objectId = new Types.ObjectId(userId);

//     // Calculate the number of documents to skip
//     const skip = (page - 1) * limit;

//     const paymentAdvices = await this.approvalWorkflowModel.aggregate([
//       {
//         // Match workflows where the given userId appears in any step
//         $match: {
//           "steps.userId": objectId
//         }
//       },
//       {
//         // Lookup the corresponding payment advice from pa_main
//         $lookup: {
//           from: 'pa_main',
//           localField: 'paymentAdviceId',
//           foreignField: '_id',
//           as: 'paymentAdvice'
//         }
//       },
//       {
//         // Unwind the payment advice array (since lookup returns an array)
//         $unwind: "$paymentAdvice"
//       },
//       {
//         // Unwind the steps array to get each step separately
//         $unwind: "$steps"
//       },
//       {
//         // Perform a lookup to retrieve the user details for each userId in steps
//         $lookup: {
//           from: 'user',  // Use 'user' as the collection name
//           localField: 'steps.userId',  // Lookup based on steps.userId
//           foreignField: '_id',         // Match with the _id field in user collection
//           as: 'userDetails'            // Save the result as userDetails
//         }
//       },
//       {
//         // Add userDetails to each step by merging the first element of the lookup result
//         $addFields: {
//           "steps.userDetails": { $arrayElemAt: ["$userDetails", 0] }  // Get the first matching user details
//         }
//       },
//       {
//         // Group back the steps into an array after adding the userDetails
//         $group: {
//           _id: "$_id",
//           paymentAdvice: { $first: "$paymentAdvice" },
//           steps: { $push: "$steps" }
//         }
//       },
//       {
//         // Project the required fields for the final result, ensuring steps include user details
//         $project: {
//           _id: 0,
//           paymentAdvice: 1,
//           steps: 1
//         }
//       }
//     ]).exec();

//       // Get the total count of documents for pagination metadata
//       const totalRecords = await this.approvalWorkflowModel.aggregate([
//         {
//           $lookup: {
//             from: 'pa_main',
//             localField: 'paymentAdviceId',
//             foreignField: '_id',
//             as: 'paymentAdvice'
//           }
//         },
//         {
//           $unwind: "$paymentAdvice"
//         },
//         {
//           $match: {
//             "paymentAdvice.createdBy": objectId
//           }
//         },
//         {
//           $count: "total"
//         }
//       ]).exec();

//     // Get the total count value
//     const totalCount = totalRecords.length > 0 ? totalRecords[0].total : 0;

//     // If no records found, return an empty array and metadata
//     if (!paymentAdvices || paymentAdvices.length === 0) {
//       return {
//         data: [],
//         currentPage: page,
//         totalPages: 0,
//         totalRecords: 0,
//       };
//     }

//     // Return paginated response with metadata
//     return {
//       data: paymentAdvices.map(result => ({
//         ...result.paymentAdvice,
//         approvalWorkflow: {
//           steps: result.steps
//         }
//       })),
//       currentPage: page,
//       totalPages: Math.ceil(totalCount / limit),
//       totalRecords: totalCount
//     };
//   }

//   // Fetch all Payment Advices assigned to a specific user
//   async getAssignedPaymentAdvices(userId: string): Promise<PaymentAdvice[]> {
//     const workflows = await this.approvalWorkflowModel
//       .find({ 'steps.userId': new Types.ObjectId(userId) })
//       .populate({
//         path: 'paymentAdviceId',  // Ensure the full PaymentAdvice is populated
//         model: 'PaymentAdvice',
//       })
//       .exec();

//     // Map the populated PaymentAdvice documents, ensuring only fully populated documents are returned
//     return workflows
//       .map((workflow) => workflow.paymentAdviceId as unknown as PaymentAdvice)  // Cast via unknown
//       .filter((paymentAdvice) => !!paymentAdvice);  // Filter out null or undefined documents
//   }

//   async listSubmittedPaymentAdvices(): Promise<PaymentAdvice[]> {
//     return await this.paymentAdviceModel
//       .find({ status: 'submitted' })  // Query for all Payment Advices with status 'submitted'
//       .populate({
//         path: 'approvalWorkflowId',   // First populate the approvalWorkflowId
//         populate: {
//           path: 'steps.userId',       // Then populate userId in each step of the workflow
//           model: 'User',              // Specify the model to populate user details
//         }
//       })
//       .exec();
//   }

//   // Retrieve all Payment Advices
//   async findAll(): Promise<PaymentAdvice[]> {
//     return this.paymentAdviceModel.find().exec();
//   }

//   // Retrieve a single Payment Advice by ID
//   async findById(id: string): Promise<PaymentAdvice> {
//     const paymentAdvice = await this.paymentAdviceModel.findById(id).exec();
//     if (!paymentAdvice) {
//       throw new NotFoundException(`Payment Advice with ID ${id} not found`);
//     }
//     return paymentAdvice;
//   }

//   // Update a Payment Advice by ID
//   async update(id: string, updatePaymentAdviceDto: CreatePaymentAdviceDTO): Promise<PaymentAdvice> {
//     const updatedPaymentAdvice = await this.paymentAdviceModel
//       .findByIdAndUpdate(id, updatePaymentAdviceDto, { new: true })
//       .exec();

//     if (!updatedPaymentAdvice) {
//       throw new NotFoundException(`Payment Advice with ID ${id} not found`);
//     }
//     return updatedPaymentAdvice;
//   }

//   // Delete a Payment Advice by ID and its related Approval Workflow
//   async delete(id: string): Promise<{ deleted: boolean }> {
//     const paymentAdvice = await this.paymentAdviceModel.findById(id).exec();
//     if (!paymentAdvice) {
//       throw new NotFoundException(`Payment Advice with ID ${id} not found`);
//     }

//     // Delete related Approval Workflow
//     await this.approvalWorkflowModel.findByIdAndDelete(paymentAdvice.approvalWorkflowId).exec();

//     // Delete Payment Advice
//     await this.paymentAdviceModel.findByIdAndDelete(id).exec();

//     return { deleted: true };
//   }
// }
