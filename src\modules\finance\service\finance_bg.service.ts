import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import * as XLSX from 'xlsx';
import { FinanceBG, FinanceBGDocument } from '../schema/finance_bg';
import { FinanceBGLog, FinanceBGLogDocument } from '../schema/finance_bg_logs';
import { SecurityService } from 'src/modules/user/service/security.service';

@Injectable()
export class FinanceBGService {
  constructor(
    @InjectModel(FinanceBG.name) private financeBGModel: Model<FinanceBGDocument>,
    @InjectModel(FinanceBGLog.name) private financeBGLogModel: Model<FinanceBGLogDocument>,
    private readonly securityService: SecurityService
  ) { }

  async importFinanceBG(buffer: Buffer): Promise<string> {
    // Parse the uploaded Excel file
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const sheet = workbook.Sheets[workbook.SheetNames[0]];

    // Convert sheet to JSON with default behavior (first row as header)
    const excelData = XLSX.utils.sheet_to_json(sheet);

    if (!Array.isArray(excelData) || excelData.length === 0) {
      throw new Error('Invalid Excel format: No data rows found.');
    }

    // Store new records to be inserted
    const newBG: any[]= [];
    const newBGLogs: any[] = [];

    for (const row of excelData as any[]) {
      // Parse the dates to determine the status
      const currentDate = new Date();
      // console.log("no", row)
      // const issuedDate = this.parseDate(row['Issued Date']);
      // const expiredDate = this.parseDate(row['Expired Date']);

      let status = 'valid';
      // if (issuedDate <= currentDate && currentDate <= expiredDate) {
      //   status = 'valid';
      // }

      // Add the bank guarantee data
      const bg = {
        serialNo: row['No.'],
        accountHolderName: row['Account Holder Name'],
        issuedOnBehalf: row['Issued On Behalf of'],
        bankName: row['Bank Name'],
        bankAccountNo: row['Bank Account No.'],
        bankGuaranteeNo: row['Bank Guarantee No.'],
        bankGuaranteeType: row['Bank Guarantee Type'],
        issuedDate: row['Issued Date'],
        expiredDate: row['Expired Date'],
        bankGuaranteeStatus: row['Bank Guarantee Status'],
        currency: row['Currency Type'],
        bankGuaranteeAmountOrg: row['Bank Guarantee Amount (Original Currency)'] === '-' ? 0 : row['Bank Guarantee Amount (Original Currency)'],
        bankGuaranteeAmountMyr: row['Bank Guarantee Amount (MYR)'] === '-' ? 0 : row['Bank Guarantee Amount (MYR)'],
        depositPledgedMyr: row['Deposit Pledged (MYR)'] === '-' ? 0 : row['Deposit Pledged (MYR)'],
        bankCommission: row['Bank Commission (MYR)'] === '-' ? 0 : row['Bank Commission (MYR)'],
        stampDutyMyr: row['Stamp Duty (MYR)'] === '-' ? 0 : row['Stamp Duty (MYR)'],
        bankHandlingFeeMyr: row['Bank Handling Fees (MYR)'] === '-' ? 0 : row['Bank Handling Fees (MYR)'],
        beneficiaryName: row['Beneficiary Name'],
        contractName: row['Contract Name'],
        contractNo: row['Contract No.'],
        pic: row['PIC'],
        remark: row['Remarks'],
        createdBy: 'Admin',
      };

      newBG.push(bg);

      // Prepare the log entry (this will use bankGuaranteeNo, but it will be updated after insert)
      newBGLogs.push({
        status,
        remark: `Status set to ${status} based on dates.`,
        updatedBy: 'System',
        updatedAt: currentDate,
      });
    }

    // Insert the bank guarantees and get the inserted records with _id
    const insertedBGs = await this.financeBGModel.insertMany(newBG);

    // Now map the _id from the inserted FinanceBG records to the log entries
    insertedBGs.forEach((bg, index) => {
      newBGLogs[index].bankGuaranteeId = bg._id;  // Assign the _id to the log
    });

    // Insert the log entries now that bankGuaranteeId is set
    await this.financeBGLogModel.insertMany(newBGLogs);

    console.log(`${newBG.length} new bank guarantees and logs imported successfully.`);
    return `${newBG.length} new bank guarantees and logs imported successfully.`;
  }

  // Helper to parse dates
  private parseDate(dateString: string): Date {
    // console.log("parsed date ", dateString)
    const [day, month, year] = dateString.split('/').map(Number);
    return new Date(year, month - 1, day);
  }

  // async getAllFinanceBG(encryptedData: any): Promise<any> {
  //   const { page, limit } = this.securityService.decrypt(encryptedData);
  //   try {
  //     // Calculate the number of records to skip
  //     const skip = (page - 1) * limit;

  //     // Fetch the total number of YGL01 records
  //     const total = await this.financeBGModel.countDocuments();

  //     // Fetch the YGL01 records with pagination
  //     const bgs = await this.financeBGModel
  //       .aggregate([
  //         {
  //           $addFields: {
  //             serialNoNumeric: { $toDouble: "$serialNo" }  // Convert serialNo to numeric
  //           }
  //         },
  //         {
  //           $sort: { serialNoNumeric: -1 }  // Sort numerically in descending order
  //         },
  //         {
  //           $skip: skip  // Pagination: skip the first `skip` records
  //         },
  //         {
  //           $limit: Number(limit)  // Pagination: limit to `limit` records
  //         }
  //       ])
  //       .exec();

  //     // console.log(`Page: ${page}, Skip: ${skip}, Limit: ${limit}`);

  //     const bankGuaranteeIds = bgs.map(bg => bg._id);
  //     // console.log("bankGuaranteeIds ", bankGuaranteeIds)

  //     const logs = await this.financeBGLogModel.find({ bankGuaranteeId: { $in: bankGuaranteeIds } }).exec();
  //     // console.log("logs ", logs)
  //     const finalBgs = bgs.map(bg => {
  //       const bgLog = logs.find(log => log.bankGuaranteeId.toString() == bg._id.toString());
  //       return {
  //         ...bg,
  //         log: bgLog
  //       }
  //     });

  //     // Return the paginated data with metadata
  //     var response = {
  //       data: finalBgs,
  //       total,
  //       page,
  //       limit,
  //     };
  //     return { data: this.securityService.encrypt(response) };

  //   } catch (error) {
  //     throw new Error(`Error fetching YGL01 records: ${error.message}`);
  //   }
  // }

  async getAllFinanceBG(): Promise<any> {
    try {
      // Fetch all YGL01 records sorted by serial number in descending order
      const bgs = await this.financeBGModel
        .aggregate([
          {
            $addFields: {
              serialNoNumeric: { $toDouble: "$serialNo" }  // Convert serialNo to numeric
            }
          },
          {
            $sort: { serialNoNumeric: -1 }  // Sort numerically in descending order
          }
        ])
        .exec();

      const bankGuaranteeIds = bgs.map(bg => bg._id);

      // console.log("bgs ", bgs)


      // Fetch related logs for all records
      const logs = await this.financeBGLogModel.find({ bankGuaranteeId: { $in: bankGuaranteeIds } }).exec();

      // console.log("bgLogs ", logs)

      // Merge logs with bank guarantee records
      const finalBgs = bgs.map(bg => {
        const bgLog = logs.find(log => log.bankGuaranteeId.toString() == bg._id.toString());
        return {
          ...bg,
          log: bgLog
        };
      });
      // console.log("finalBgs ", finalBgs)

      // Return the data
      return { data: this.securityService.encrypt(finalBgs) };

    } catch (error) {
      throw new Error(`Error fetching YGL01 records: ${error.message}`);
    }
  }

  convertToDate(dateString: string): Date {
    // Check if dateString is valid and follows `DD/MM/YYYY` format
    if (!dateString || typeof dateString !== 'string') return new Date();

    // Split the dateString into [DD, MM, YYYY]
    const parts = dateString.split('/');

    // Ensure there are exactly 3 parts (day, month, year)
    if (parts.length !== 3) return new Date();

    // Rearrange to `YYYY-MM-DD` format and create a new Date object
    const formattedDate = `${parts[2]}-${parts[1]}-${parts[0]}`;
    return new Date(formattedDate);
  }

  isRowEmpty(row: any[]): boolean {
    const nonEmptyCells = row.filter(cell => cell !== null && cell !== undefined && cell !== '');
    return nonEmptyCells.length === 0;  // Only process rows with at least one meaningful value
  }

  async findAll(): Promise<any> {
    const payments = await this.financeBGModel.aggregate([
      {
        $addFields: {
          parsedDate: {
            $dateFromString: {
              dateString: "$date",
              format: "%d/%m/%Y"  // Specify the format for conversion
            }
          }
        }
      },
      {
        $sort: { parsedDate: -1 }  // Sort by the converted date in descending order
      },
      {
        $project: {
          parsedDate: 0  // Remove the temporary `parsedDate` field from the result
        }
      }
    ]).exec();

    return { success: true, data: payments };
  }

  async createFinanceBG(encryptedData: any): Promise<any> {
    // Decrypt the incoming data
    const data = this.securityService.decrypt(encryptedData);

    // Fetch the latest serialNo with numeric comparison
    const latestBG = await this.financeBGModel
      .findOne({})
      .sort({ serialNo: -1 }) // Sort numerically
      .collation({ locale: 'en', numericOrdering: true }) // Enable numeric ordering for strings
      .exec();

    // Convert serialNo to a number, or default to 0 if no record exists
    const latestSerialNo = latestBG ? parseInt(latestBG.serialNo, 10) : 0;

    // Increment the serial number
    const newSerialNo = (latestSerialNo + 1).toString();

    // Assign the new serialNo
    data.serialNo = newSerialNo;

    // Parse dates to determine the status
    const currentDate = new Date();
    const issuedDate = this.parseDate(data.issuedDate);
    const expiredDate = this.parseDate(data.expiredDate);

    let status = 'invalid';
    if (issuedDate <= currentDate && currentDate <= expiredDate) {
      status = 'valid';
    }

    data.bankGuaranteeStatus = status;

    // Create and save the new FinanceBG document
    const newFinanceBG = new this.financeBGModel(data);
    const savedFinanceBG = await newFinanceBG.save();

    // Create a log entry for the new FinanceBG
    const logEntry = {
      bankGuaranteeId: savedFinanceBG._id, // Link to the FinanceBG document
      status,
      remark: `Status set to ${status} based on dates.`,
      updatedBy: 'System', // System-generated entry
      updatedAt: currentDate, // Log creation timestamp
    };

    // Save the log entry
    await this.financeBGLogModel.create(logEntry);

    // Return the created FinanceBG document
    return { data: this.securityService.encrypt(savedFinanceBG) };
  }

  async getFinanceBGById(encryptedData: any): Promise<any> {
    const data = this.securityService.decrypt(encryptedData);

    const objectId = Types.ObjectId.isValid(data) ? new Types.ObjectId(data) : null;
    if (!objectId) {
      throw new NotFoundException('Invalid BG ID');
    }

    var bg = await this.financeBGModel.findById(objectId);

    if(!bg) {
      throw new NotFoundException('BG not found')
    }

    const log = await this.financeBGLogModel.findOne({ bankGuaranteeId: bg._id }).exec();
    // console.log("logs ", logs)
    var result = {
      ...bg.toObject(),
      log: log
    }
    return { data: this.securityService.encrypt(result) };
  }

  async updateFinanceBG(id: string, updateData: any): Promise<FinanceBG | any> {
    return await this.financeBGModel.findByIdAndUpdate(id, updateData, { new: true }).exec();
  }

  async deleteFinanceBG(id: string): Promise<{ deleted: boolean }> {
    const result = await this.financeBGModel.findByIdAndDelete(id).exec();
    return { deleted: !!result };
  }
}
