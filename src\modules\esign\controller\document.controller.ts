import { 
    Controller, 
    Post, 
    Get, 
    Put, 
    Param, 
    Body, 
    UseGuards, 
    UseInterceptors, 
    UploadedFile,
    Query,
    Res,
    StreamableFile
  } from '@nestjs/common';
  import { FileInterceptor } from '@nestjs/platform-express';
  import { Response } from 'express';
  import * as fs from 'fs';
import { ApproveDocumentDto, DocumentsService, UploadDocumentDto } from '../service/document.service';
  

  @Controller('e-sign/documents')
  export class DocumentsController {
    constructor(private documentsService: DocumentsService) {}
  
    @Post('upload')
    @UseInterceptors(FileInterceptor('file'))
    async uploadDocument(
      @UploadedFile() file: Express.Multer.File,
      @Body() body: UploadDocumentDto,
    ) {
      return this.documentsService.uploadDocument(file, body);
    }
  
    @Get('department')
    async getDocumentsByDepartment(@Body('departmentId') departmentId: string) {
      return this.documentsService.getDocumentsByDepartment(departmentId);
    }
  
    @Get('pending')
    async getPendingDocuments() {
      return this.documentsService.getPendingDocuments();
    }

    @Post('submitted')
    async getSubmittedDocuments(@Body() data: any) {
      return this.documentsService.getSubmittedDocuments(data.userId);
    }
  
    @Get(':id')
    async getDocument(@Param('id') id: string) {
      return this.documentsService.getDocumentById(id);
    }
  
    @Post('approve')
    @UseInterceptors(FileInterceptor('file'))
    async approveDocument(
      @UploadedFile() file: Express.Multer.File,
      @Body() body?: any,
    ) {
      return this.documentsService.approveDocument(body.id, file, body.notes);
    }
  
    @Post('reject')
    async rejectDocument(
      @Body() body?: any,
    ) {
      return this.documentsService.rejectDocument(body.id, body.notes);
    }
  }