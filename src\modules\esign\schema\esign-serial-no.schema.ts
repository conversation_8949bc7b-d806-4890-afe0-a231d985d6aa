import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ESignSerialNumberDocument = ESignSerialNumber & Document;

@Schema({ collection: 'e_sign_serial_no' })
export class ESignSerialNumber {
  @Prop({ required: true })
  prefix: string; // e.g., "UZMA-EPA"

  @Prop({ required: true })
  currentNumber: number; // Last used serial number
}

export const ESignSerialNumberSchema = SchemaFactory.createForClass(ESignSerialNumber);
