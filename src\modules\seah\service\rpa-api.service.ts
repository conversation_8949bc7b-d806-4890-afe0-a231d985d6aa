import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { SeahPVService } from './pv.service';
import { SageReportRPAService } from './sage-report-rpa.service';
import { ProjectPVService } from './project-pv.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { RPAWebhook, RPAWebhookDocument } from '../schema/rpa-webhook.schema';
import { S3Service } from 'src/modules/common/services/s3.service';

@Injectable()
export class RPAApiService {
  private readonly logger = new Logger(RPAApiService.name);
  private readonly rpaApiUrl: string;
  private readonly token: string;


  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly sageReportRPAService: SageReportRPAService,
    private readonly seahPVService: ProjectPVService,
    private readonly s3Service: S3Service,
    @InjectModel(RPAWebhook.name) private rpaWebhookModel: Model<RPAWebhookDocument>,
  ) {
    this.rpaApiUrl = this.configService.get<string>('RPA_API_URL')!;
    this.token = this.configService.get<string>('RPA_API_TOKEN')!;
  }

  async callRPAApi(params: any): Promise<any> {
    try {
      this.logger.log(`Calling RPA API with params: ${JSON.stringify(params)}`);

      // Call external RPA API
      const response = await firstValueFrom(
        this.httpService.post(this.rpaApiUrl, params, {
          responseType: 'arraybuffer', // Important for receiving file response
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/octet-stream',
            'Authorization': `Bearer ${this.token}`,  // ✅ Add Bearer token here
          },
        })
      );

      // Log successful API call
      await this.sageReportRPAService.logRPAActivity(
        'RPA_API_CALL',
        '1', // We're making one API call
        0, // No monetary amount involved
        true,
        `Successfully called RPA API for date range ${params.dateFrom} to ${params.dateTo}`
      );

      // // Convert the file buffer from the response
      // const fileBuffer = Buffer.from(response.data);

      // // Process the Excel file from RPA
      // const importResult = await this.seahPVService.importPaymentsFromExcel(fileBuffer);
      const importResult = params;

      return {
        success: true,
        message: 'RPA API call successful',
        importResult,
      };
    } catch (error) {
      // Log error in RPA activity
      await this.sageReportRPAService.logRPAActivity(
        'RPA_API_CALL',
        '0',
        0,
        false,
        `Error calling RPA API: ${error.message || 'Unknown error'}`
      );

      this.logger.error(`Error calling RPA API: ${error.message}`, error.stack);
      throw new HttpException(
        `Failed to call RPA API: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Method to be called by the scheduler
  async runScheduledRPAApiCall(reportCode?: string): Promise<any> {
    try {
      // Get today's date
      const today = new Date();

      // Calculate yesterday's date (for the default startDate)
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      // Format dates as DD/MM/YYYY
      const formatDate = (date: Date): string => {
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
      };

      const now = new Date();
      const requestId = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}` +
        `-${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}`;

      // Create the parameters for the API call
      const params: any = {
        reportcode: reportCode,
        company: this.configService.get<string>('RPA_DEFAULT_COMPANY', 'DEFAULT_COMPANY'),
        startDate: formatDate(yesterday),  // e.g., "20/04/2025"
        endDate: formatDate(today),        // e.g., "21/04/2025"
        department: this.configService.get<string>('RPA_DEFAULT_DEPARTMENT', ''),
        project: this.configService.get<string>('RPA_DEFAULT_PROJECT', ''),
        requestId: requestId               // e.g., "20250421-104525"
      };

      const body = { "injason": params}

      this.logger.log(`Running scheduled RPA API call with params: ${JSON.stringify(params)}`);
      await this.callRPAApi(JSON.stringify(body));
      this.logger.log('Scheduled RPA API call completed successfully');
    } catch (error) {
      this.logger.error(`Error in scheduled RPA API call: ${error.message}`, error.stack);

      // Log error in RPA activity even for scheduler errors
      await this.sageReportRPAService.logRPAActivity(
        'SCHEDULED_RPA_API_CALL',
        '0',
        0,
        false,
        `Scheduler error: ${error.message || 'Unknown error'}`
      );
    }
  }

  /**
  * Process a file received from an external RPA API
  * @param requestId - Unique identifier for the RPA request
  * @param file - The uploaded Excel file
  */
  async processRPAFile(requestId: string, file: Express.Multer.File): Promise<any> {
    this.logger.log(`Processing RPA file from webhook. RequestId: ${requestId}, Filename: ${file.originalname}`);

    // Create a new webhook record
    const webhookRecord = new this.rpaWebhookModel({
      requestId,
      fileName: file.originalname,
      fileSize: file.size,
      isProcessed: false,
      hasError: false,
    });

    // Save the initial webhook record
    await webhookRecord.save();

    try {
      // Log the start of file processing
      await this.sageReportRPAService.logRPAActivity(
        'RPA_WEBHOOK_RECEIVED',
        '1', // One file received
        0,   // No monetary amount yet
        true, // Initial success status for receiving the file
        `File received from RPA webhook. RequestId: ${requestId}, Filename: ${file.originalname}`
      );

      // First, upload the file to S3
      let fileUrl = '';
      try {
        // Upload the file to S3 using the S3Service
        fileUrl = await this.s3Service.uploadRPAFile(file);
        this.logger.log(`Successfully uploaded file to S3: ${fileUrl}`);

        // Update the webhook record with the S3 URL
        await this.rpaWebhookModel.findByIdAndUpdate(webhookRecord._id, {
          fileUrl: fileUrl
        });
      } catch (s3Error) {
        this.logger.error(`Error uploading file to S3: ${s3Error.message}`, s3Error.stack);
        // Continue processing even if S3 upload fails
      }

      // Convert the multer file buffer to a Node.js Buffer
      const fileBuffer = Buffer.from(file.buffer);

      // Use the existing SeahPVService to process the Excel file
      const importResult = await this.seahPVService.importPaymentsFromExcel(fileBuffer);

      // Extract the number of records and estimate total amount from the import result
      const recordCount = importResult.match(/(\d+)/)?.[1] || '0';

      // Update the webhook record with success information
      await this.rpaWebhookModel.findByIdAndUpdate(webhookRecord._id, {
        isProcessed: true,
        processedAt: new Date(),
        processedRecords: recordCount,
        fileUrl: fileUrl || null // In case S3 upload failed
      });

      // Log successful processing
      await this.sageReportRPAService.logRPAActivity(
        'RPA_WEBHOOK_PROCESSED',
        recordCount,
        0, // We don't have the exact amount here, so using 0
        true,
        `Successfully processed file from RPA webhook. RequestId: ${requestId}, Result: ${importResult}, File URL: ${fileUrl || 'Not available'}`
      );

      return {
        success: true,
        requestId,
        message: 'File processed successfully',
        details: importResult,
        fileUrl: fileUrl || null
      };
    } catch (error) {
      this.logger.error(`Error processing RPA file from webhook: ${error.message}`, error.stack);

      // Update the webhook record with error information
      await this.rpaWebhookModel.findByIdAndUpdate(webhookRecord._id, {
        isProcessed: true,
        processedAt: new Date(),
        hasError: true,
        errorMessage: error.message || 'Unknown error'
      });

      // Log error in RPA activity
      await this.sageReportRPAService.logRPAActivity(
        'RPA_WEBHOOK_ERROR',
        '0',
        0,
        false,
        `Error processing file from RPA webhook. RequestId: ${requestId}, Error: ${error.message || 'Unknown error'}`
      );

      throw new HttpException(
        `Failed to process RPA file: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
  * Get webhook history with optional filtering and pagination
  */
  async getWebhookHistory(limit: number = 10, skip: number = 0, hasError?: boolean): Promise<any> {
    try {
      // Build the query
      const query: any = {};

      // Add hasError filter if provided
      if (hasError !== undefined) {
        query.hasError = hasError;
      }

      // Get total count for pagination
      const total = await this.rpaWebhookModel.countDocuments(query).exec();

      // Get paginated results
      const webhooks = await this.rpaWebhookModel.find(query)
        .sort({ createdAt: -1 }) // Sort by most recent first
        .skip(skip)
        .limit(limit)
        .exec();

      return {
        success: true,
        data: webhooks,
        pagination: {
          total,
          limit,
          skip,
          hasMore: total > skip + limit
        }
      };
    } catch (error) {
      this.logger.error(`Error fetching webhook history: ${error.message}`, error.stack);
      throw new HttpException(
        `Failed to fetch webhook history: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Get a specific webhook by request ID
   */
  async getWebhookByRequestId(requestId: string): Promise<any> {
    try {
      const webhook = await this.rpaWebhookModel.findOne({ requestId }).exec();

      if (!webhook) {
        throw new HttpException(
          `No webhook found with request ID: ${requestId}`,
          HttpStatus.NOT_FOUND
        );
      }

      return {
        success: true,
        data: webhook
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(`Error fetching webhook with request ID ${requestId}: ${error.message}`, error.stack);
      throw new HttpException(
        `Failed to fetch webhook: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}