import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { CreateProjectDto, UpdateProjectDto, ProjectResponseDto } from '../dto/billing-project.dto';
import { BillingProjectService } from '../service/billing-project.service';

@Controller('auto-billing/projects')
export class BillingProjectController {
  constructor(private readonly projectService: BillingProjectService) {}

  @Post()
  async create(@Body() createProjectDto: CreateProjectDto) {
    const project = await this.projectService.create(createProjectDto);
    return this.mapToResponseDto(project);
  }

  @Post('get')
  async findAll(@Body() body?: any) {
    let projects;
    if (body.departmentId) {
      projects = await this.projectService.findByCompany(body.departmentId);
    } else {
      projects = await this.projectService.findAll();
    }
    return projects.map(project => this.mapToResponseDto(project));
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const project = await this.projectService.findOne(id);
    return this.mapToResponseDto(project);
  }

  @Get('by-project-id/:projectId')
  async findByProjectId(@Param('projectId') projectId: string) {
    const project = await this.projectService.findByProjectId(projectId);
    return this.mapToResponseDto(project);
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateProjectDto: UpdateProjectDto) {
    const project = await this.projectService.update(id, updateProjectDto);
    return this.mapToResponseDto(project);
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    const project = await this.projectService.remove(id);
    return this.mapToResponseDto(project);
  }

  private mapToResponseDto(project: any): ProjectResponseDto {
    return {
      id: project._id.toString(),
      projectId: project.projectId,
      projectName: project.projectName,
      departmentId: project.departmentId,
      description: project.description,
      isActive: project.isActive,
      startDate: project.startDate,
      endDate: project.endDate,
      projectManager: project.projectManager,
      additionalDetails: project.additionalDetails,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
    };
  }
}